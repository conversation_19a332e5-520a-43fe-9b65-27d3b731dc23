# 任务总结：添加游客登录功能

本文档总结了为 Filink 应用添加游客登录功能的详细过程和所做的修改。

## 1. 任务目标

在设置页面添加一个“游客登录”按钮，实现以下功能：
*   游客登录逻辑留空（后续实现）。
*   游客登录使用固定的用户 ID (`id1`) 和用户名 (`guest`)。
*   游客登录需要检测 `cf_clearance` cookie 的两次变化才算成功。
*   游客登录的 WebView Cookie 需要与正常登录的 WebView Cookie 隔离，但游客 Cookie 仍需持久化。

## 2. 已完成的修改

### 2.1. `Pages/SettingsView.swift`

*   **添加“游客登录”按钮：** 在“用户”部分的现有“添加新用户”按钮下方，新增了一个“游客登录”按钮。
*   **Sheet 视图逻辑调整：** 修改了 `.sheet` 修饰符的逻辑，使其根据 `settingsManager.isGuestLoginActive` 的状态来决定显示 `LoginViewRepresentable`（用于正常登录）还是 `GuestViewRepresentable`（用于游客登录）。
*   **按钮 Action 逻辑：**
    *   “添加新用户”按钮的 `action` 中，将 `settingsManager.isGuestLoginActive` 设置为 `false`。
    *   “游客登录”按钮的 `action` 中，将 `settingsManager.isGuestLoginActive` 设置为 `true`，并直接触发 `isPresentingLoginSheet`。

### 2.2. `Managers/SettingsManager.swift`

*   **新增 `isGuestLoginActive` 属性：** 在 `SettingsManager` 类中添加了一个新的 `@Published` 属性 `isGuestLoginActive: Bool`，用于在应用层面管理当前是否处于游客登录流程。此属性的值会持久化到 `UserDefaults`。
*   **初始化顺序修复：** 修复了 `init()` 方法中属性的初始化顺序问题，确保 `isGuestLoginActive` 和 `username` 等属性在所有存储属性初始化之前被正确赋值，解决了编译错误。

### 2.3. `Login/GuestViewController.swift` (新增文件)

*   **文件创建：** 在 `Login` 文件夹下创建了 `GuestViewController.swift`。
*   **基本结构：** 复制了 `LoginViewController.swift` 的基本结构，并进行了以下关键修改：
    *   **类名：** `GuestViewController`。
    *   **协议：** 定义了 `FilinkGuestLoginViewControllerDelegate` 协议，用于回调游客登录结果。
    *   **导航栏标题：** 修改为“游客登录”。
    *   **初始 URL 加载：** `loadInitialPage()` 方法现在直接加载 `settingsManager.website` 的根 URL，不包含 `loginPath`。
    *   **`cf_clearance` Cookie 轮询：**
        *   引入了 `cfClearanceChangeCount` 和 `lastCfClearanceValue` 属性来跟踪 `cf_clearance` 的变化。
        *   在 `didFinish` 方法中，如果检测到 Cloudflare 验证页面且是标准 key，则启动一个 `Timer` (`cfClearanceCheckTimer`)，每隔 1.5 秒轮询 `WKWebsiteDataStore` 获取 `cf_clearance` 的最新值。
        *   当 `cfClearanceChangeCount` 达到 2 时，视为游客登录成功，并调用 `reportResultAndDismiss`。
    *   **UI 交互（加载菊花/覆盖层）：**
        *   `showBackgroundVerificationOverlay` 和 `hideBackgroundVerificationOverlay` 方法用于控制加载菊花和半透明覆盖层的显示与隐藏。
        *   当检测到非标准 key 的 Cloudflare 验证页面时，会隐藏覆盖层并停止菊花，允许用户手动交互。
    *   **错误处理：** 统一的错误处理逻辑。
    *   **`reportResultAndDismiss`：** 修改此方法，使其在成功时调用 `delegate.didFinishGuestLogin`。

### 2.4. `Login/GuestViewRepresentable.swift` (新增文件)

*   **文件创建：** 在 `Login` 文件夹下创建了 `GuestViewRepresentable.swift`。
*   **基本结构：** 复制了 `LoginViewRepresentable.swift` 的基本结构，并进行了以下关键修改：
    *   **类名：** `GuestViewRepresentable`。
    *   **封装的 UIViewController：** 将其封装的 `UIViewController` 更改为 `GuestViewController`。
    *   **初始化器：** 只提供了一个明确的初始化器 `init(isPresented:url:)`，确保 `url` 参数被正确传递为 `settingsManager.website` 的根 URL。
    *   **Coordinator 逻辑：**
        *   `Coordinator` 内部的 `didFinishGuestLogin` 方法不再尝试获取用户 Profile。
        *   在游客登录成功后，直接调用 `saveCookiesAndDismiss` 方法，并传入固定的 `userId: "id1"` 和 `username: "guest"`，以及从 `GuestViewController` 获取到的所有 Cookie。
        *   修复了 `saveCookiesAndDismiss` 方法在非主 Actor 上下文调用的问题，将其包装在 `Task { @MainActor in ... }` 中。

## 3. 编译错误修复

在开发过程中，解决了以下编译错误：
*   `Login/GuestViewController.swift:198:15 Initializer for conditional binding must have Optional type, not 'String'`：修复了 `loadInitialPage` 中对 `settingsManager.website` 的不当 `guard let` 绑定。
*   `Login/GuestViewController.swift:313:21 Unterminated string literal` 和 `Login/GuestViewController.swift:313:64 Cannot find ')' to match opening '(' in string interpolation`：修复了 `getWebViewUserAgent` 方法中字符串插值和字面量闭合的语法错误。
*   `Login/GuestViewRepresentable.swift:87:18 Call to main actor-isolated instance method 'saveCookiesAndDismiss(cookiesToSave:userId:username:)' in a synchronous nonisolated context`：将 `saveCookiesAndDismiss` 的调用包装在 `Task { @MainActor in ... }` 中，确保在主线程执行。
*   `Managers/SettingsManager.swift` 中的初始化错误：调整了 `SettingsManager` 的 `init()` 方法中 `@Published` 属性的初始化顺序，确保所有存储属性在 `init` 结束前被正确初始化。

## 4. 待完成事项 (WebView Cookie 隔离)

虽然目前 `GuestViewController` 和 `LoginViewController` 使用了独立的 `WKWebView` 实例，但它们默认共享 `WKWebsiteDataStore.default()`。为了实现真正的 Cookie 隔离，还需要进行以下修改，这将在下一份文档中详细说明。
