# WebView Cookie 隔离实现方案

本文档详细说明了在 Filink 应用中实现 WebView Cookie 隔离的必要性、原理以及具体实现方案。

## 1. 问题背景

在 iOS 应用中，`WKWebView` 是用于显示网页内容的组件。默认情况下，所有 `WKWebView` 实例都共享一个全局的 `WKWebsiteDataStore.default()`。这个默认的数据存储包含了所有网站数据，如 Cookie、缓存、本地存储等。

对于 Filink 应用，我们有以下两种用户场景：
1.  **正常登录用户：** 用户通过用户名密码登录，其会话 Cookie 需要被管理和持久化。
2.  **游客登录用户：** 用户以游客身份访问，其会话（特别是 Cloudflare 的 `cf_clearance` Cookie）也需要被管理和持久化。

如果正常登录和游客登录的 WebView 共享同一个 `WKWebsiteDataStore.default()`，可能会导致以下问题：
*   **Cookie 冲突：** 正常用户的登录 Cookie 可能会意外地影响游客模式，反之亦然。例如，如果正常用户的 Cookie 仍然存在，游客模式可能会被网站识别为已登录状态，导致行为异常。
*   **状态混乱：** 难以清晰地管理和区分不同用户会话的网站数据。
*   **安全隐患：** 虽然在此场景下不构成严重安全漏洞，但在更复杂的应用中，共享数据存储可能增加数据泄露或会话劫持的风险。

因此，为了确保不同登录模式之间的独立性和稳定性，实现 WebView Cookie 的隔离是必要的。

## 2. 解决方案：独立的 `WKWebsiteDataStore`

`WKWebsiteDataStore` 是 `WKWebView` 用于管理网站数据的核心组件。通过为不同的 `WKWebView` 实例配置独立的 `WKWebsiteDataStore`，我们可以实现 Cookie 和其他网站数据的隔离。

### 2.1. 原理

*   **`WKWebsiteDataStore.default()`：** 这是系统提供的默认数据存储，所有未指定 `WKWebsiteDataStore` 的 `WKWebView` 都会使用它。其数据是持久化的，并在应用的不同运行会话之间共享。
*   **`WKWebsiteDataStore.init(forIdentifier:)`：** 我们可以通过这个初始化器创建新的、独立的 `WKWebsiteDataStore` 实例。每个通过此方法创建的 `WKWebsiteDataStore` 都有自己的独立存储空间，不会与 `default()` 或其他自定义的 `WKWebsiteDataStore` 实例共享数据。这些自定义的数据存储也是持久化的，除非显式清除。

### 2.2. 实现方案

我们将为游客登录的 `GuestViewController` 配置一个独立的 `WKWebsiteDataStore`，而正常登录的 `LoginViewController` 将继续使用默认的 `WKWebsiteDataStore.default()`。

**具体修改点：**

1.  **修改文件：** `Login/GuestViewController.swift`
2.  **修改方法：** `setupWebView()`

**修改内容：**

在 `GuestViewController` 的 `setupWebView()` 方法中，我们将修改 `WKWebViewConfiguration` 的创建方式，为其指定一个独立的 `WKWebsiteDataStore`。

```swift
// Login/GuestViewController.swift

// ... (其他代码) ...

private func setupWebView() {
    // 创建一个独立的 WKWebsiteDataStore 实例，用于游客模式。
    // 用户明确要求游客数据也需要持久化，因此不在此处清除，也不使用 nonPersistent()。
    // 使用一个唯一的标识符来区分这个数据存储。
    let guestDataStore = WKWebsiteDataStore.init(forIdentifier: "FilinkGuestDataStore")
    
    let configuration = WKWebViewConfiguration()
    configuration.websiteDataStore = guestDataStore // 将独立的 DataStore 分配给配置

    webView = WKWebView(frame: .zero, configuration: configuration)
    webView.translatesAutoresizingMaskIntoConstraints = false
    webView.navigationDelegate = self
    // 使用与 LoginViewController 相同的 User-Agent
    webView.customUserAgent =
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    webView.isOpaque = false
    webView.backgroundColor = .clear
    webView.scrollView.backgroundColor = .clear

    view.addSubview(webView)

    NSLayoutConstraint.activate([
        webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
        webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
        webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
        webView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
    ])
}

// ... (其他代码) ...
```

### 2.3. 预期效果

*   **Cookie 隔离：** 游客模式下的 `WKWebView` 将拥有自己独立的 Cookie 存储，不会与正常登录模式下的 Cookie 混淆。
*   **数据持久化：** 游客模式下获取的 `cf_clearance` 等 Cookie 将被持久化，即使应用关闭并重新打开，这些 Cookie 也会保留，符合用户要求。
*   **清晰管理：** 正常登录和游客登录的会话数据将得到明确的区分和管理。

## 3. 备用方案（不采用）

*   **`WKWebsiteDataStore.nonPersistent()`：** 这是一个创建非持久化数据存储的方法（iOS 11.0+）。如果用户要求游客数据在应用关闭后自动清除，这将是一个合适的选择。但由于用户明确要求持久化，因此不采用此方案。
*   **手动清除数据：** 可以在 `GuestViewController` 的 `deinit` 方法中，显式调用 `guestDataStore.removeData(...)` 来清除所有数据。但这与用户要求持久化的意图相悖，因此不采用。

通过上述修改，我们将确保 Filink 应用在处理不同登录模式的 WebView Cookie 时，能够提供稳定、隔离且符合预期的行为。
