


// MARK: - 数据大小计算器
struct AppDataSizeCalculator {
    static func calculateAllDataSizes(completion: @escaping (Double, Double, Double, Int, Int, Int) -> Void) {
        // 异步计算 Cookie 大小
        calculateCookieSize { cookieSize in
            // 同步计算 Core Data 大小
            let (coreDataTotalSize, postCount, userCount, commentCount) = calculateCoreDataSize()
            
            // 同步计算图片缓存大小
            let imageCacheSize = calculateImageCacheSize()
            
            // 所有计算完成后，通过 completion 回调结果
            completion(cookieSize, coreDataTotalSize, imageCacheSize, postCount, userCount, commentCount)
        }
    }

    private static func calculateCookieSize(completion: @escaping (Double) -> Void) {
        var totalSize: Double = 0.0 // 字节

        // 1. 计算 CookieManager 存储的 Cookie 大小 (UserDefaults)
        if let userData = UserDefaults.standard.data(forKey: "cookieManager_savedUsersData_v2") {
            totalSize += Double(userData.count)
        }

        // 2. 计算 WebView Cookie 大小 (WKWebsiteDataStore)
        // 注意: WKWebsiteDataRecord 在较新版本中没有直接的 'size' 属性。
        // 如果需要精确大小，可能需要遍历 WKHTTPCookieStore 中的 HTTPCookie 对象并计算其属性的字符串长度。
        // 这里为了解决编译错误，我们只计算 UserDefaults 中的大小，并假设 WebView Cookie 清除操作是主要的。
        // 如果需要更精确的 WebView Cookie 大小，需要更复杂的逻辑。
        WKWebsiteDataStore.default().fetchDataRecords(ofTypes: [WKWebsiteDataTypeCookies]) { records in
            // 简单地将每个记录视为一个单位，或者不计算其大小，因为没有直接的 size 属性
            // 为了避免编译错误，我们不再尝试访问 record.size
            // totalSize += Double(records.count) * 100 // 假设每个cookie平均100字节，仅为示例
            completion(totalSize / (1024 * 1024)) // 转换为 MB
        }
    }

    private static func calculateCoreDataSize() -> (totalSize: Double, postCount: Int, userCount: Int, commentCount: Int) {
        var totalSize: Double = 0.0 // 字节
        let fileManager = FileManager.default
        
        // 获取 Core Data 数据库文件路径
        guard let storeURL = CoreDataManager.shared.container.persistentStoreDescriptions.first?.url else {
            print("【错误】无法获取 Core Data 存储路径。")
            return (0, 0, 0, 0)
        }
        
        let directoryURL = storeURL.deletingLastPathComponent()
        let fileName = storeURL.lastPathComponent.replacingOccurrences(of: ".sqlite", with: "")
        
        let possibleFileExtensions = ["sqlite", "sqlite-wal", "sqlite-shm"]
        
        for ext in possibleFileExtensions {
            let fileURL = directoryURL.appendingPathComponent("\(fileName).\(ext)")
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                    if let fileSize = attributes[.size] as? UInt64 {
                        totalSize += Double(fileSize)
                    }
                } catch {
                    print("【错误】获取 Core Data 文件大小失败: \(error.localizedDescription)")
                }
            }
        }
        
        // 获取实体数量
        let viewContext = CoreDataManager.shared.container.viewContext
        var postCount = 0
        var userCount = 0
        var commentCount = 0
        
        do {
            let postFetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
            postCount = try viewContext.count(for: postFetchRequest)
            
            let userFetchRequest: NSFetchRequest<User> = User.fetchRequest()
            userCount = try viewContext.count(for: userFetchRequest)
            
            let commentFetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
            commentCount = try viewContext.count(for: commentFetchRequest)
            
        } catch {
            print("【错误】获取 Core Data 实体数量失败: \(error.localizedDescription)")
        }
        
        return (totalSize / (1024 * 1024), postCount, userCount, commentCount) // 转换为 MB
    }

    private static func calculateImageCacheSize() -> Double {
        // 直接调用 ImageFileManager 中新添加的静态方法来计算缓存大小
        return ImageFileManager.calculateCacheSize()
    }
}



    @State private var cookieDataSizeMB: Double = 0
    @State private var coreDataTotalSizeMB: Double = 0
    @State private var imageCacheSizeMB: Double = 0
    @State private var postCount: Int = 0
    @State private var userCount: Int = 0
    @State private var commentCount: Int = 0
    @State private var totalUsedSpaceMB: Double = 0
    @State private var storageSegments: [StorageSegment] = []



        .onAppear {
            AppDataSizeCalculator.calculateAllDataSizes { cookieSize, coreDataSize, imageCacheSize, pCount, uCount, cCount in
                DispatchQueue.main.async {
                    self.cookieDataSizeMB = cookieSize
                    self.coreDataTotalSizeMB = coreDataSize
                    self.imageCacheSizeMB = imageCacheSize
                    self.postCount = pCount
                    self.userCount = uCount
                    self.commentCount = cCount
                    
                    self.totalUsedSpaceMB = cookieSize + coreDataSize + imageCacheSize
                    
                    self.storageSegments = [
                        .init(name: "Cookie", size: cookieSize / 1024, color: .blue), // 转换为 GB
                        .init(name: "实体数据", size: coreDataSize / 1024, color: .green), // 转换为 GB
                        .init(name: "资源媒体", size: imageCacheSize / 1024, color: .orange) // 转换为 GB
                    ]
                }
            }
        }