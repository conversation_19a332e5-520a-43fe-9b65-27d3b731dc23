# 按页面应用字体功能 - 剩余开发步骤

## 1. 摘要

我们已经成功地重构了 `FontManager`，使其能够支持按作用域（页面）启用/禁用全局自定义字体。我们还更新了设置界面 (`FontManagementView`)，使其能够正确地保存用户的偏好设置。

现在，最后一步是将这个新的字体逻辑应用到所有相关的UI组件上。这主要是通过将旧的 `customFont()` 调用替换为新的 `customFont(for: scope)` 调用来完成。

## 2. 待办事项

### 2.1 修改 `PostContentView.swift`

这个文件用于渲染帖子详情页的内容。我们需要将 `fontScope` 参数传递给它包含的 `AttributedTextView`。

**文件路径**: `Filink/Pages/PostDetailView/Components/PostContentView.swift`

**需要修改的地方**:

1.  **为 `IsolatedAttributedTextView` 添加 `fontScope`**:
    ```swift
    struct IsolatedAttributedTextView: View, Equatable {
        let contentItems: [ContentItem]
        let fontScope: FontScope // <-- 新增
        let onImageTap: (([URL], Int) -> Void)?
        @EnvironmentObject var settingsManager: SettingsManager
        // ...
    }
    ```

2.  **更新 `AttributedTextView` 的调用**:
    在 `IsolatedAttributedTextView` 的 `body` 中，找到 `AttributedTextView` 的初始化调用。

    **修改前**:
    ```swift
    return AttributedTextView(
        contentItems: .constant(contentItems),
        bottomPadding: 0,
        lineSpacing: 4,
        onImageTap: onImageTap
    )
    ```

    **修改后**:
    ```swift
    return AttributedTextView(
        contentItems: .constant(contentItems),
        bottomPadding: 0,
        lineSpacing: 4,
        fontScope: self.fontScope, // <-- 新增
        onImageTap: onImageTap
    )
    ```

3.  **更新 `IsolatedAttributedTextView` 的创建位置**:
    在 `EquatableContentView` 的 `body` 中，找到创建 `IsolatedAttributedTextView` 的地方。

    **修改前**:
    ```swift
    IsolatedAttributedTextView(
        contentItems: contentItems,
        onImageTap: onImageTap
    )
    ```

    **修改后** (假设帖子详情页的作用域是 `.detail`):
    ```swift
    IsolatedAttributedTextView(
        contentItems: contentItems,
        fontScope: .detail, // <-- 新增
        onImageTap: onImageTap
    )
    ```

### 2.2 修改 `CommentView.swift`

如果评论区也使用了 `AttributedTextView` 来显示内容，那么也需要进行类似的修改。

**文件路径**: `Filink/Pages/PostDetailView/Comments/CommentView.swift`

**需要修改的地方**:
找到 `AttributedTextView` 的调用点，并传入 `fontScope: .comment`。

**示例**:
```swift
AttributedTextView(
    contentItems: comment.contentItems,
    // ... 其他参数
    fontScope: .comment // <-- 新增
)
```

### 2.3 修改 `SettingsView.swift`

如果设置页面本身也使用了 `customFont` 来显示某些文本，需要找到对应的调用点并传入 `fontScope: .settings`。

**文件路径**: `Filink/Pages/SettingsView/SettingsView.swift`

**示例**:
```swift
Text("关于应用")
    .font(.customFont(size: 16, for: .settings)) // <-- 修改
```

## 3. 验证

完成以上所有代码修改后，请编译并运行应用。

1.  进入 **设置 -> 字体管理**。
2.  导入一个自定义字体。
3.  **测试场景1**: 只打开“首页”开关。返回首页，确认标题字体已改变。进入详情页和评论区，确认字体未改变。
4.  **测试场景2**: 打开“详情页”和“评论区”开关，关闭“首页”。返回首页，确认字体是系统默认。进入详情页和评论区，确认字体已改变。
5.  **测试场景3**: 点击“恢复默认字体”，确认所有页面的字体都恢复为系统默认。

完成以上步骤后，按页面应用字体功能即开发完成。
