# 草稿功能使用说明

## 📝 功能概述

草稿功能是富文本编辑器的重要组成部分，提供了完整的文档草稿管理解决方案。支持自动保存、手动保存、草稿列表管理、搜索、导出等功能。

## 🚀 主要特性

### 1. 自动保存
- **智能触发**：在用户停止输入3秒后自动保存
- **防抖处理**：避免频繁保存影响性能
- **错误重试**：保存失败时自动重试，最多3次
- **状态指示**：实时显示保存状态（等待、保存中、已保存、失败）

### 2. 图片管理
- **本地存储**：图片保存在沙盒目录中，不占用数据库空间
- **HTML引用**：草稿中使用HTML标签引用本地图片路径
- **自动清理**：删除草稿时自动清理关联的图片文件
- **孤立清理**：支持清理未被任何草稿引用的图片

### 3. 草稿列表
- **智能排序**：按更新时间倒序排列
- **搜索功能**：支持按标题和内容搜索
- **批量操作**：支持批量删除、导出
- **预览显示**：显示标题、预览内容、字数、更新时间

### 4. 数据存储
- **Core Data**：使用Core Data进行持久化存储
- **HTML格式**：草稿内容以HTML格式存储，便于搜索和预览
- **纯文本备份**：同时保存纯文本版本用于快速搜索

## 🛠️ 使用方法

### 基本操作

#### 1. 开始写作
- 打开富文本编辑器
- 开始输入内容
- 系统会自动创建草稿并开始自动保存

#### 2. 手动保存
- 点击工具栏中的保存按钮（下载图标）
- 或使用快捷键（如果支持）

#### 3. 查看草稿列表
- 点击编辑器顶部的草稿按钮（文档图标）
- 查看所有已保存的草稿
- 草稿数量会显示在按钮上的红色徽章中

#### 4. 恢复草稿
- 在草稿列表中点击任意草稿
- 或点击草稿行右侧的菜单选择"恢复编辑"
- 草稿内容会自动加载到编辑器中

### 高级功能

#### 1. 搜索草稿
- 在草稿列表顶部的搜索框中输入关键词
- 支持搜索标题和内容
- 实时过滤显示结果

#### 2. 批量管理
- 在草稿列表中点击"编辑"按钮
- 选择多个草稿
- 使用"操作"菜单进行批量删除或导出

#### 3. 导出草稿
- 单个导出：在草稿行菜单中选择导出选项
- 批量导出：选择多个草稿后使用批量操作
- 支持纯文本和HTML格式导出

#### 4. 设置管理
- 点击编辑器顶部的设置按钮（齿轮图标）
- 调整自动保存设置
- 管理显示选项
- 查看缓存信息

## ⚙️ 设置选项

### 自动保存设置
- **启用自动保存**：开启/关闭自动保存功能
- **保存延迟**：设置停止输入后多久开始保存（1-10秒）

### 显示选项
- **显示字数统计**：在编辑器底部显示字数统计栏

### 缓存管理
- **查看缓存大小**：显示草稿图片占用的存储空间
- **清理缓存**：删除未使用的图片文件
- **清空所有草稿**：删除所有草稿和相关文件

## 📊 状态指示

### 保存状态
- **空闲**：无显示
- **等待保存**：显示"等待保存..."（橙色）
- **正在保存**：显示"正在保存..."（蓝色）
- **已保存**：显示"已保存 X分钟前"（绿色）
- **保存失败**：显示"保存失败: 错误信息"（红色）

### 草稿标识
- **自动保存**：草稿列表中显示"自动保存"标签（橙色）
- **手动保存**：无特殊标识

## 🔧 技术实现

### 架构组件
1. **DraftManager**：草稿管理核心类
2. **AutoSaveService**：自动保存服务
3. **DraftImageManager**：图片文件管理
4. **DraftListView**：草稿列表界面
5. **DraftSettingsView**：设置管理界面

### 数据模型
```swift
entity Draft {
    id: UUID                    // 主键
    title: String              // 草稿标题
    htmlContent: String        // HTML内容
    plainTextContent: String   // 纯文本内容（用于搜索）
    createdAt: Date           // 创建时间
    updatedAt: Date           // 更新时间
    wordCount: Int32          // 字数
    isAutoSaved: Bool         // 自动保存标记
}
```

### 图片存储
- **存储位置**：`Documents/DraftImages/`
- **文件命名**：UUID + `.jpg`
- **HTML引用**：`<img src="file://本地路径" />`

## 🚨 注意事项

### 数据安全
- 草稿数据存储在本地，不会自动同步到云端
- 删除应用会丢失所有草稿数据
- 建议定期导出重要草稿

### 性能考虑
- 大量图片可能占用较多存储空间
- 建议定期清理未使用的图片缓存
- 草稿数量过多时可能影响列表加载速度

### 兼容性
- 支持iOS 15.0及以上版本
- 支持iPhone和iPad
- 支持横竖屏切换

## 🐛 故障排除

### 常见问题

#### 1. 自动保存不工作
- 检查设置中是否启用了自动保存
- 确认内容长度超过最小要求（5个字符）
- 查看是否有错误提示

#### 2. 图片显示异常
- 检查图片文件是否存在
- 尝试清理缓存后重新插入图片
- 确认图片格式支持

#### 3. 草稿列表为空
- 确认已经创建过草稿
- 检查搜索框是否有过滤条件
- 尝试刷新列表

#### 4. 导出功能异常
- 确认已选择要导出的草稿
- 检查系统分享功能是否正常
- 尝试重新选择草稿

### 错误代码
- **AutoSaveError.noDraftManager**：草稿管理器未初始化
- **AutoSaveError.contentTooShort**：内容太短，无需保存
- **AutoSaveError.noContentChange**：内容未发生变化

## 📞 技术支持

如果遇到问题或有功能建议，请：
1. 查看本文档的故障排除部分
2. 检查应用设置和权限
3. 尝试重启应用
4. 联系开发团队获取支持

---

*最后更新：2024年12月*
