# 草稿功能开发文档

## 📋 功能概述

草稿功能是富文本编辑器的下一个重要功能，允许用户保存未完成的文档，支持自动保存、草稿管理和恢复编辑。

## 🎯 功能需求

### 核心功能
1. **自动保存草稿**：编辑过程中自动保存内容
2. **草稿列表管理**：查看、搜索、删除草稿
3. **恢复编辑**：从草稿列表恢复到编辑器
4. **草稿同步**：本地存储，支持iCloud同步

### 用户场景
- 用户编辑长文档时意外退出，可恢复内容
- 用户同时编写多篇文档，可在草稿间切换
- 用户在不同设备间同步草稿内容

## 🏗️ 技术架构

### 数据模型 (Core Data)
```swift
entity Draft {
    id: UUID                    // 主键
    title: String              // 草稿标题
    content: Data              // NSAttributedString序列化
    htmlContent: String        // HTML预览内容
    createdAt: Date           // 创建时间
    updatedAt: Date           // 更新时间
    wordCount: Int32          // 字数
    isAutoSaved: Bool         // 自动保存标记
}
```

### 核心组件
1. **DraftManager**: 草稿管理器
2. **DraftListView**: 草稿列表界面
3. **AutoSaveService**: 自动保存服务
4. **DraftStorage**: 数据存储层

## 🔧 详细设计

### 1. DraftManager (草稿管理器)
```swift
class DraftManager: ObservableObject {
    @Published var drafts: [Draft] = []
    @Published var currentDraft: Draft?
    
    // 创建新草稿
    func createDraft(from attributedText: NSAttributedString) -> Draft
    
    // 保存草稿
    func saveDraft(_ draft: Draft, content: NSAttributedString)
    
    // 删除草稿
    func deleteDraft(_ draft: Draft)
    
    // 获取所有草稿
    func fetchDrafts() -> [Draft]
    
    // 搜索草稿
    func searchDrafts(keyword: String) -> [Draft]
}
```

### 2. AutoSaveService (自动保存服务)
```swift
class AutoSaveService: ObservableObject {
    private var saveTimer: Timer?
    private var lastSaveTime: Date = Date()
    
    // 启动自动保存
    func startAutoSave(for editor: RichTextEditor)
    
    // 停止自动保存
    func stopAutoSave()
    
    // 立即保存
    func saveNow()
    
    // 保存策略
    private func shouldSave() -> Bool {
        // 3秒无操作 + 内容有变化
    }
}
```

### 3. DraftListView (草稿列表界面)
```swift
struct DraftListView: View {
    @StateObject private var draftManager = DraftManager()
    @State private var searchText = ""
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            List {
                ForEach(filteredDrafts) { draft in
                    DraftRowView(draft: draft) {
                        // 恢复编辑
                        restoreToEditor(draft)
                    }
                }
                .onDelete(perform: deleteDrafts)
            }
            .searchable(text: $searchText)
            .navigationTitle("草稿")
        }
    }
}
```

### 4. DraftRowView (草稿行视图)
```swift
struct DraftRowView: View {
    let draft: Draft
    let onRestore: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(draft.title)
                .font(.headline)
                .lineLimit(1)
            
            Text(draft.htmlContent.stripHTML())
                .font(.body)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                Text("\(draft.wordCount)字")
                Spacer()
                Text(draft.updatedAt.relativeTime())
            }
            .font(.caption)
            .foregroundColor(.secondary)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onRestore()
        }
    }
}
```

## 🔄 工作流程

### 自动保存流程
1. **监听编辑事件**：文本变化时启动计时器
2. **防抖处理**：3秒内无新变化才触发保存
3. **后台保存**：在后台队列执行保存操作
4. **状态更新**：保存完成后更新UI状态

### 草稿恢复流程
1. **选择草稿**：用户从列表选择草稿
2. **加载内容**：反序列化NSAttributedString
3. **恢复编辑器**：设置编辑器内容和光标位置
4. **关联草稿**：建立编辑器与草稿的关联

## 📱 用户界面设计

### 草稿入口
- **编辑器顶部**：草稿按钮，显示草稿数量徽章
- **设置页面**：草稿管理入口

### 草稿列表
- **搜索栏**：支持标题和内容搜索
- **排序选项**：按时间、字数、标题排序
- **批量操作**：多选删除、导出

### 状态提示
- **自动保存指示器**：显示保存状态
- **网络状态**：iCloud同步状态
- **错误提示**：保存失败提醒

## 🛠️ 开发计划

### Phase 1: 基础功能 (1周)
- [ ] Core Data模型建立
- [ ] DraftManager基础实现
- [ ] 简单的手动保存功能
- [ ] 基础草稿列表界面

### Phase 2: 自动保存 (3-4天)
- [ ] AutoSaveService实现
- [ ] 编辑器集成自动保存
- [ ] 保存状态UI指示
- [ ] 错误处理和重试机制

### Phase 3: 高级功能 (3-4天)
- [ ] 搜索和筛选功能
- [ ] 草稿导出分享
- [ ] 批量操作
- [ ] iCloud同步支持

### Phase 4: 优化完善 (2-3天)
- [ ] 性能优化
- [ ] 用户体验细节
- [ ] 单元测试
- [ ] 文档更新

## 🧪 测试策略

### 单元测试
- DraftManager的CRUD操作
- AutoSaveService的保存逻辑
- 数据序列化/反序列化

### 集成测试
- 编辑器与草稿系统集成
- iCloud同步功能
- 错误场景处理

### 用户测试
- 长文档编辑场景
- 多草稿切换体验
- 网络异常情况

## ⚠️ 技术风险

### 数据安全
- **风险**：草稿数据丢失
- **缓解**：多重备份，本地+iCloud

### 性能问题
- **风险**：大量草稿影响性能
- **缓解**：分页加载，懒加载

### 同步冲突
- **风险**：多设备同步冲突
- **缓解**：时间戳比较，冲突解决策略

## 📊 成功指标

### 功能指标
- 自动保存成功率 > 99%
- 草稿恢复成功率 > 99%
- 同步成功率 > 95%

### 性能指标
- 保存操作 < 100ms
- 草稿列表加载 < 500ms
- 内存使用 < 50MB

### 用户体验
- 草稿功能使用率 > 60%
- 用户满意度 > 4.5/5
- 数据丢失投诉 = 0

---

## 📝 开发备注

### 依赖项
- Core Data框架
- CloudKit (可选)
- Combine框架

### 兼容性
- iOS 15.0+
- 支持iPhone和iPad
- 支持横竖屏切换

### 国际化
- 支持中英文界面
- 时间格式本地化
- 错误信息本地化
