<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "E01A823E-0B37-4E3D-96BE-5D8A8A80C40E"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A1920FFD-5A8C-41BB-804E-E088F40F924D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Views/EmptyContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "211"
            endingLineNumber = "211"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "22667877-FEBC-41DC-AD22-60D97891EC35"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Services/APIService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "512"
            endingLineNumber = "512"
            landmarkName = "parseLatestDataManually(from:page:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FB21B27C-0020-48BB-9DEA-98D4749B80DC"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Services/APIService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "521"
            endingLineNumber = "521"
            landmarkName = "parseLatestDataManually(from:page:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D762810A-96EE-48AD-8A91-02F1DA5D684D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Services/APIService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "520"
            endingLineNumber = "520"
            landmarkName = "parseLatestDataManually(from:page:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "73A7E763-B6CD-41EE-A0B4-7A53A989B42C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Services/APIService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "522"
            endingLineNumber = "522"
            landmarkName = "parseLatestDataManually(from:page:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AA05B84F-FF1B-4133-A6EF-D88CAA634957"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Pages/HomeView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "69"
            endingLineNumber = "69"
            landmarkName = "makeUIView(context:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DD2E03EB-FB98-479F-AAA9-736CC03B3154"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Pages/HomeView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "70"
            endingLineNumber = "70"
            landmarkName = "makeUIView(context:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "409D5CE8-1E04-4FEC-BD3C-0CF714C80F37"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Filink/Pages/DynamicHeightView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "3"
            endingLineNumber = "3"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
