import Foundation

// MARK: - 分类数据模型
struct CategoryItem: Identifiable, Hashable { // 添加 Hashable 协议
    let id: Int
    let name: String
    let icon: String
    let color: String
    let slug: String // 添加 slug 字段
}

// 类别数据
let categories: [CategoryItem] = [
    CategoryItem(id: 0, name: "所有类别", icon: "list.bullet", color: "555555", slug: "all"), // 添加 slug 字段
    CategoryItem(id: 4, name: "开发调优", icon: "hammer", color: "33FFFF", slug: "develop"),
    CategoryItem(id: 14, name: "资源荟萃", icon: "square.and.arrow.up", color: "12A89D", slug: "resource"),
    CategoryItem(id: 42, name: "文档共建", icon: "doc.text", color: "ddf2fd", slug: "wiki"),
    CategoryItem(id: 10, name: "跳蚤市场", icon: "cylinder.split.1x2", color: "ED207B", slug: "trade"),
    CategoryItem(id: 27, name: "非我莫属", icon: "briefcase", color: "a8c6fe", slug: "job"),
    CategoryItem(id: 32, name: "读书成诗", icon: "book.closed", color: "f5ec00", slug: "reading"),
    CategoryItem(id: 46, name: "扬帆起航", icon: "sailboat", color: "ff9838", slug: "startup"),
    CategoryItem(id: 34, name: "前沿快讯", icon: "newspaper", color: "BB8FCE", slug: "news"),
    CategoryItem(id: 92, name: "网络记忆", icon: "wifi", color: "F7941D", slug: "feeds"),
    CategoryItem(id: 36, name: "福利羊毛", icon: "gift", color: "E45735", slug: "welfare"),
    CategoryItem(id: 11, name: "搞七捻三", icon: "drop", color: "3AB54A", slug: "gossip"),
    CategoryItem(id: 2, name: "运营反馈", icon: "bubble.left.and.bubble.right", color: "808281", slug: "feedback"),
    CategoryItem(id: 45, name: "深海幽域", icon: "water.waves", color: "45B7D1", slug: "muted")
]
