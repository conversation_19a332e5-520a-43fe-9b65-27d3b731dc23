import Foundation
import SwiftUI // 引入 SwiftUI 以便使用 ObservableObject 和 @Published

// MARK: - Tag 结构体 (保持不变)
struct Tag: Codable, Identifiable, Hashable {
    let id: String
    let text: String
    let name: String
    let icon: String?
    let description: String?
    let count: Int
    let pm_only: Bool
    let target_tag: String?
}

// MARK: - AppTagGroup 结构体 (保持不变)
struct AppTagGroup: Codable, Identifiable {
    let id: Int
    let name: String
    let tags: [Tag]
}

// MARK: - AppTagsData 结构体 (保持不变)
struct AppTagsData: Codable {
    let tag_groups: [AppTagGroup]
}

// MARK: - TagDataLoader 类 (优化后)
@MainActor // 推荐：确保 @Published 属性的更新在主线程上
class TagDataLoader: ObservableObject {
    static let shared = TagDataLoader() // 单例

    @Published private(set) var tagGroups: [AppTagGroup] = [] // 供 Menu 组件直接使用
    @Published private(set) var tagsMap: [String: Tag] = [:]   // 用于快速查找
    @Published private(set) var isLoading: Bool = false
    @Published private(set) var loadingError: Error? = nil

    private var hasAttemptedLoad = false // 防止不必要的重复加载尝试

    private init() {
        // 不在 init 中同步加载数据
        // 可以考虑在这里触发异步加载，如果希望数据尽早可用
        // Task { await loadTagsData() }
    }

    /// 异步加载和解析 tags.json 文件。
    /// 这个方法可以被视图的 .task 修饰符调用。
    func loadTagsIfNeeded() async {
        // 如果正在加载或已成功加载，则不执行任何操作
        if isLoading {
            print("【调试】TagsData: 数据正在加载中，跳过重复请求")
            return
        }
        
        if hasAttemptedLoad && loadingError == nil && !tagGroups.isEmpty {
            print("【调试】TagsData: 数据已成功加载，跳过重复请求 (标签组: \(tagGroups.count), 标签: \(tagsMap.count))")
            return
        }

        // 标记已尝试加载，并重置状态
        hasAttemptedLoad = true
        isLoading = true
        loadingError = nil
        print("【调试】TagsData: ===== 开始加载 tags.json... =====")

        do {
            guard let url = Bundle.main.url(forResource: "tags", withExtension: "json") else {
                throw NSError(domain: "TagDataLoaderError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法找到 tags.json 文件。"])
            }

            // 异步读取文件内容 (将同步的 Data(contentsOf:) 包装在 Task.detached 中)
            let data = try await Task.detached(priority: .userInitiated) {
                try Data(contentsOf: url)
            }.value // 获取 Task 的结果

            // 解析 JSON (JSONDecoder 相对较快，但如果数据巨大，也可以考虑移到 Task.detached)
            let decodedData = try JSONDecoder().decode(AppTagsData.self, from: data)

            // 更新 @Published 属性 (由于 @MainActor，这会自动在主线程上完成)
            self.tagGroups = decodedData.tag_groups
            var newTagsMap: [String: Tag] = [:]
            for group in decodedData.tag_groups {
                for tag in group.tags {
                    newTagsMap[tag.id] = tag
                }
            }
            self.tagsMap = newTagsMap
            print("【调试】TagsData: ===== 成功加载并解析 tags.json ===== 标签组: \(self.tagGroups.count)，总标签: \(self.tagsMap.count)")
        } catch {
            print("【错误】TagsData: ===== 加载或解析 tags.json 失败 ===== \(error.localizedDescription)")
            self.loadingError = error
            // 发生错误时，确保数据是空的
            self.tagGroups = []
            self.tagsMap = [:]
        }
        
        isLoading = false // 确保在所有路径都设置 isLoading 为 false
    }

    /// 根据标签 ID (即标签名) 获取 Tag 对象。
    /// 这个方法是同步的，依赖于数据已经通过 loadTagsIfNeeded() 加载。
    /// 在 SwiftUI 视图中，通常应该观察 `tagGroups` 或 `tagsMap` 的变化，而不是直接调用这个。
    func getTag(by id: String) -> Tag? {
        return tagsMap[id]
    }

    // 如果需要一个扁平化的所有标签列表
    var allTags: [Tag] {
        tagGroups.flatMap { $0.tags }
    }
}