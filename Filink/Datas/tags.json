{"tag_groups": [{"id": 9, "name": "推广标签", "tags": [{"id": "高级推广", "text": "高级推广", "name": "高级推广", "icon": null, "description": "专用标签，拥有<a href=\"/s\">富可敌国头衔</a>的账号可用。", "count": 36, "pm_only": false, "target_tag": null}]}, {"id": 5, "name": "活动专用", "tags": [{"id": "我与Linux的那些事", "text": "我与Linux的那些事", "name": "我与Linux的那些事", "icon": null, "description": null, "count": 39, "pm_only": false, "target_tag": null}, {"id": "晒年味", "text": "晒年味", "name": "晒年味", "icon": null, "description": null, "count": 2085, "pm_only": false, "target_tag": null}, {"id": "圆圆满满", "text": "圆圆满满", "name": "圆圆满满", "icon": null, "description": null, "count": 1536, "pm_only": false, "target_tag": null}]}, {"id": 3, "name": "特殊标签", "tags": [{"id": "NSFW", "text": "NSFW", "name": "NSFW", "icon": "exclamationmark.triangle.fill", "description": null, "count": 230, "pm_only": false, "target_tag": null}, {"id": "优质博文", "text": "优质博文", "name": "优质博文", "icon": "doc.richtext.fill", "description": null, "count": 2, "pm_only": false, "target_tag": null}, {"id": "文档", "text": "文档", "name": "文档", "icon": "doc.text.fill", "description": null, "count": 27, "pm_only": false, "target_tag": null}, {"id": "推广", "text": "推广", "name": "推广", "icon": "megaphone.fill", "description": null, "count": 63, "pm_only": false, "target_tag": null}, {"id": "公益推广", "text": "公益推广", "name": "公益推广", "icon": "heart.fill", "description": null, "count": 74, "pm_only": false, "target_tag": null}, {"id": "作品集", "text": "作品集", "name": "作品集", "icon": "photo.on.rectangle.angled", "description": null, "count": 619, "pm_only": false, "target_tag": null}, {"id": "AFF", "text": "AFF", "name": "AFF", "icon": "link", "description": "该标签用于提醒他人本话题中存在aff链接，含有aff的链接仍需显著标明", "count": 5466, "pm_only": false, "target_tag": null}, {"id": "快问快答", "text": "快问快答", "name": "快问快答", "icon": "questionmark.circle.fill", "description": null, "count": 44178, "pm_only": false, "target_tag": null}]}, {"id": 1, "name": "社区官方", "tags": [{"id": "危险", "text": "危险", "name": "危险", "icon": "exclamationmark.octagon.fill", "description": null, "count": 6, "pm_only": false, "target_tag": null}, {"id": "封禁", "text": "封禁", "name": "封禁", "icon": "person.fill.badge.minus", "description": null, "count": 5, "pm_only": false, "target_tag": null}, {"id": "精华神帖", "text": "精华神帖", "name": "精华神帖", "icon": "hand.thumbsup.fill", "description": null, "count": 29, "pm_only": false, "target_tag": null}, {"id": "禁水", "text": "禁水", "name": "禁水", "icon": "minus.rectangle.fill", "description": null, "count": 7, "pm_only": false, "target_tag": null}, {"id": "livestream", "text": "livestream", "name": "livestream", "icon": "headphones", "description": null, "count": 6, "pm_only": false, "target_tag": null}, {"id": "公告", "text": "公告", "name": "公告", "icon": "speaker.wave.2.fill", "description": null, "count": 114, "pm_only": false, "target_tag": null}, {"id": "碎碎碎念", "text": "碎碎念", "name": "碎碎念", "icon": "drop.fill", "description": null, "count": 38, "pm_only": false, "target_tag": null}]}, {"id": 4, "name": "类版块组", "tags": [{"id": "硬件开发", "text": "硬件开发", "name": "硬件开发", "icon": "cpu.fill", "description": null, "count": 30, "pm_only": false, "target_tag": null}, {"id": "数据库", "text": "数据库", "name": "数据库", "icon": "cylinder.split.1x2.fill", "description": null, "count": 60, "pm_only": false, "target_tag": null}, {"id": "硬件调试", "text": "硬件调试", "name": "硬件调试", "icon": "wrench.and.screwdriver.fill", "description": null, "count": 112, "pm_only": false, "target_tag": null}, {"id": "影视", "text": "影视", "name": "影视", "icon": "film.fill", "description": null, "count": 1572, "pm_only": false, "target_tag": null}, {"id": "硬件测试", "text": "硬件测试", "name": "硬件测试", "icon": null, "description": null, "count": 28, "pm_only": false, "target_tag": null}, {"id": "转载", "text": "转载", "name": "转载", "icon": "arrowshape.turn.up.backward.fill", "description": null, "count": 529, "pm_only": false, "target_tag": null}, {"id": "美食", "text": "美食", "name": "美食", "icon": "fork.knife", "description": null, "count": 341, "pm_only": false, "target_tag": null}, {"id": "健身", "text": "健身", "name": "健身", "icon": "heart.text.square.fill", "description": null, "count": 328, "pm_only": false, "target_tag": null}, {"id": "嵌入式", "text": "嵌入式", "name": "嵌入式", "icon": "memorychip.fill", "description": null, "count": 74, "pm_only": false, "target_tag": null}, {"id": "赏金任务", "text": "赏金任务", "name": "赏金任务", "icon": "medal.fill", "description": null, "count": 279, "pm_only": false, "target_tag": null}, {"id": "拼车", "text": "拼车", "name": "拼车", "icon": "car.fill", "description": null, "count": 433, "pm_only": false, "target_tag": null}, {"id": "职场", "text": "职场", "name": "职场", "icon": "briefcase.fill", "description": null, "count": 2554, "pm_only": false, "target_tag": null}, {"id": "音乐", "text": "音乐", "name": "音乐", "icon": "music.note", "description": null, "count": 446, "pm_only": false, "target_tag": null}, {"id": "动漫", "text": "动漫", "name": "动漫", "icon": "face.smiling.fill", "description": null, "count": 604, "pm_only": false, "target_tag": null}, {"id": "摄影", "text": "摄影", "name": "摄影", "icon": "camera.fill", "description": null, "count": 158, "pm_only": false, "target_tag": null}, {"id": "抽奖", "text": "抽奖", "name": "抽奖", "icon": "gift.fill", "description": null, "count": 474, "pm_only": false, "target_tag": null}, {"id": "旅行", "text": "旅行", "name": "旅行", "icon": "mappin.and.ellipse", "description": null, "count": 379, "pm_only": false, "target_tag": null}, {"id": "VPS", "text": "VPS", "name": "VPS", "icon": "server.rack", "description": null, "count": 1752, "pm_only": false, "target_tag": null}, {"id": "游戏", "text": "游戏", "name": "游戏", "icon": "gamecontroller.fill", "description": null, "count": 1468, "pm_only": false, "target_tag": null}, {"id": "订阅节点", "text": "订阅节点", "name": "订阅节点", "icon": "network", "description": null, "count": 121, "pm_only": false, "target_tag": null}, {"id": "病友", "text": "病友", "name": "病友", "icon": "person.fill", "description": null, "count": 7359, "pm_only": false, "target_tag": null}, {"id": "二次元", "text": "二次元", "name": "二次元", "icon": "theatermasks.fill", "description": null, "count": 95, "pm_only": false, "target_tag": null}, {"id": "人工智能", "text": "人工智能", "name": "人工智能", "icon": "brain.head.profile", "description": null, "count": 24397, "pm_only": false, "target_tag": null}, {"id": "网络安全", "text": "网络安全", "name": "网络安全", "icon": "shield.fill", "description": null, "count": 2018, "pm_only": false, "target_tag": null}, {"id": "纯水", "text": "纯水", "name": "纯水", "icon": "drop.fill", "description": null, "count": 32620, "pm_only": false, "target_tag": null}, {"id": "快问快答", "text": "快问快答", "name": "快问快答", "icon": "questionmark.circle.fill", "description": null, "count": 44178, "pm_only": false, "target_tag": null}, {"id": "软件开发", "text": "软件开发", "name": "软件开发", "icon": "hammer.fill", "description": null, "count": 3034, "pm_only": false, "target_tag": null}, {"id": "金融经济", "text": "金融经济", "name": "金融经济", "icon": "chart.line.uptrend.xyaxis", "description": null, "count": 820, "pm_only": false, "target_tag": null}, {"id": "计算机网络", "text": "计算机网络", "name": "计算机网络", "icon": "cloud.fill", "description": null, "count": 608, "pm_only": false, "target_tag": null}, {"id": "软件测试", "text": "软件测试", "name": "软件测试", "icon": "ladybug.fill", "description": null, "count": 124, "pm_only": false, "target_tag": null}, {"id": "软件调试", "text": "软件调试", "name": "软件调试", "icon": "wrench.adjustable.fill", "description": null, "count": 272, "pm_only": false, "target_tag": null}, {"id": "配置优化", "text": "配置优化", "name": "配置优化", "icon": "slider.horizontal.3", "description": null, "count": 767, "pm_only": false, "target_tag": null}, {"id": "算法", "text": "算法", "name": "算法", "icon": "circles.hexagonpath.fill", "description": null, "count": 394, "pm_only": false, "target_tag": null}, {"id": "树洞", "text": "树洞", "name": "树洞", "icon": "tree.fill", "description": null, "count": 4113, "pm_only": false, "target_tag": null}, {"id": "求资源", "text": "求资源", "name": "求资源", "icon": "magnifyingglass", "description": null, "count": 831, "pm_only": false, "target_tag": null}]}, {"id": 2, "name": "编程语言", "tags": [{"id": "PHP", "text": "PHP", "name": "PHP", "icon": null, "description": null, "count": 17, "pm_only": false, "target_tag": null}, {"id": "CPP", "text": "CPP", "name": "CPP", "icon": null, "description": null, "count": 9, "pm_only": false, "target_tag": null}, {"id": "HTML", "text": "HTML", "name": "HTML", "icon": null, "description": null, "count": 16, "pm_only": false, "target_tag": null}, {"id": "Rust", "text": "Rust", "name": "Rust", "icon": null, "description": null, "count": 34, "pm_only": false, "target_tag": null}, {"id": "JavaScript", "text": "JavaScript", "name": "JavaScript", "icon": null, "description": null, "count": 50, "pm_only": false, "target_tag": null}, {"id": "CSS", "text": "CSS", "name": "CSS", "icon": null, "description": null, "count": 92, "pm_only": false, "target_tag": null}, {"id": "Python", "text": "Python", "name": "Python", "icon": null, "description": null, "count": 120, "pm_only": false, "target_tag": null}, {"id": "Golang", "text": "Golang", "name": "Golang", "icon": null, "description": null, "count": 17, "pm_only": false, "target_tag": null}, {"id": "<PERSON>", "text": "<PERSON>", "name": "<PERSON>", "icon": null, "description": null, "count": 0, "pm_only": false, "target_tag": null}, {"id": "Java", "text": "Java", "name": "Java", "icon": null, "description": null, "count": 161, "pm_only": false, "target_tag": null}, {"id": "CSharp", "text": "CSharp", "name": "CSharp", "icon": null, "description": null, "count": 1, "pm_only": false, "target_tag": null}, {"id": "C", "text": "C", "name": "C", "icon": null, "description": null, "count": 50, "pm_only": false, "target_tag": null}]}, {"id": 6, "name": "网盘A组", "tags": [{"id": "文叔叔", "text": "文叔叔", "name": "文叔叔", "icon": null, "description": null, "count": 0, "pm_only": false, "target_tag": null}, {"id": "谷歌云盘", "text": "谷歌云盘", "name": "谷歌云盘", "icon": null, "description": null, "count": 2, "pm_only": false, "target_tag": null}, {"id": "阿里云盘", "text": "阿里云盘", "name": "阿里云盘", "icon": null, "description": null, "count": 56, "pm_only": false, "target_tag": null}, {"id": "百度网盘", "text": "百度网盘", "name": "百度网盘", "icon": null, "description": null, "count": 1025, "pm_only": false, "target_tag": null}]}, {"id": 7, "name": "网盘B组", "tags": [{"id": "迅雷网盘", "text": "迅雷网盘", "name": "迅雷网盘", "icon": null, "description": null, "count": 30, "pm_only": false, "target_tag": null}, {"id": "夸克网盘", "text": "夸克网盘", "name": "夸克网盘", "icon": null, "description": null, "count": 3826, "pm_only": false, "target_tag": null}, {"id": "UC网盘", "text": "UC网盘", "name": "UC网盘", "icon": null, "description": null, "count": 31, "pm_only": false, "target_tag": null}]}]}