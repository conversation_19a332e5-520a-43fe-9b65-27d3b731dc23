import SwiftUI

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0) // Fallback to clear color
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    // 根据颜色方案调整颜色亮度
    static func categoryColor(hex: String, for colorScheme: ColorScheme) -> Color {
        let baseColor = Color(hex: hex) // 使用现有的 hex 初始化器创建基础颜色

        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0

        // 将 SwiftUI Color 转换为 UIColor/NSColor 以获取 HSB 值
        #if canImport(UIKit)
        UIColor(baseColor).getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        #elseif canImport(AppKit)
        NSColor(baseColor).getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        #endif

        if colorScheme == .light {
            // 在浅色模式下，降低亮度，使其更深
            let newBrightness = max(0.2, brightness * 0.7) // 降低亮度到70%，但至少为0.2
            return Color(hue: hue, saturation: saturation, brightness: newBrightness, opacity: alpha)
        } else {
            // 在深色模式下，可以稍微提高亮度，或者保持原样
            let newBrightness = min(1.0, brightness * 1.1) // 稍微提高亮度，但最多为1.0
            return Color(hue: hue, saturation: saturation, brightness: newBrightness, opacity: alpha)
        }
    }
}
