import Foundation

extension Date {
    func timeAgoDisplay() -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN") // Keep locale setting
        formatter.unitsStyle = .full // Or .short, .abbreviated based on preference
        return formatter.localizedString(for: self, relativeTo: Date())
    }
    
    func formatDateTime() -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: self)
    }
    
    func formattedToSecond() -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: self)
    }

    /// 获取相对时间字符串（用于草稿功能）
    func relativeTime() -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.unitsStyle = .short
        return formatter.localizedString(for: self, relativeTo: Date())
    }
    
    // 创建标准配置的 ISO8601DateFormatter
    static func standardISO8601Formatter() -> ISO8601DateFormatter {
        let formatter = ISO8601DateFormatter()
        // 使用更宽松的格式选项，支持更多变体
        formatter.formatOptions = [
            .withInternetDateTime,
            .withFractionalSeconds,
            .withTimeZone
        ]
        // 确保时区处理正确 - ISO8601DateFormatter会自动处理UTC时间
        return formatter
    }

    // 创建备用的ISO8601格式化器，用于处理不同的格式变体
    static func fallbackISO8601Formatters() -> [ISO8601DateFormatter] {
        var formatters: [ISO8601DateFormatter] = []

        // 标准格式（带毫秒和时区）
        let standard = ISO8601DateFormatter()
        standard.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        formatters.append(standard)

        // 无毫秒格式
        let noFractional = ISO8601DateFormatter()
        noFractional.formatOptions = [.withInternetDateTime]
        formatters.append(noFractional)

        // 带时区但无毫秒
        let withTimezone = ISO8601DateFormatter()
        withTimezone.formatOptions = [.withInternetDateTime, .withTimeZone]
        formatters.append(withTimezone)

        // 完整格式
        let full = ISO8601DateFormatter()
        full.formatOptions = [.withFullDate, .withFullTime, .withFractionalSeconds]
        formatters.append(full)

        return formatters
    }

    // 创建灵活的日期解析器，专门处理API格式
    static func flexibleDateParser(from dateString: String) -> Date? {

        // 首先尝试标准 ISO8601DateFormatter
        let iso8601Formatter = standardISO8601Formatter()
        if let date = iso8601Formatter.date(from: dateString) {
            return date
        }

        // 尝试备用的ISO8601格式化器
        let fallbackFormatters = fallbackISO8601Formatters()
        for (index, formatter) in fallbackFormatters.enumerated() {
            if let date = formatter.date(from: dateString) {
                print("【调试】✅ Date.flexibleDateParser: 备用ISO8601格式化器[\(index)]解析成功: \(date)")
                return date
            }
        }

        // 如果ISO8601失败，尝试手动解析常见格式
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(abbreviation: "UTC")

        // 尝试多种可能的日期格式
        let formats = [
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // 2025-07-07T03:38:40.646Z
            "yyyy-MM-dd'T'HH:mm:ss'Z'",         // 2025-07-07T03:38:40Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",  // 微秒格式
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", // 更多微秒位
            "yyyy-MM-dd'T'HH:mm:ss",            // 无时区标识
            "yyyy-MM-dd HH:mm:ss",              // 空格分隔
            "yyyy-MM-dd'T'HH:mm:ss.SSS",        // 无Z后缀
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ",    // 微秒+Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSZ"    // 更多微秒+Z
        ]

        for format in formats {
            formatter.dateFormat = format
            if let date = formatter.date(from: dateString) {
                print("【调试】✅ Date.flexibleDateParser: 格式'\(format)'解析成功: \(date)")
                return date
            }
        }

        print("【调试】❌ Date.flexibleDateParser: 所有格式都解析失败，日期字符串: '\(dateString)'")
        return nil
    }
    
    // 创建标准配置的 JSONDecoder 日期解析策略
    static func createISO8601DecodingStrategy() -> JSONDecoder.DateDecodingStrategy {
        return .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // 使用增强的灵活解析器
            if let date = Date.flexibleDateParser(from: dateString) {
                return date
            }

            throw DecodingError.dataCorruptedError(
                in: container,
                debugDescription: "无法解码日期字符串 \(dateString)"
            )
        }
    }
}

// 扩展 JSONDecoder 以便更容易地设置标准日期解析策略
extension JSONDecoder {
    static func withStandardDateDecoding() -> JSONDecoder {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = Date.createISO8601DecodingStrategy()
        return decoder
    }
}
