import SwiftUI

extension Font {
    /// 返回一个自定义字体，如果已设置；否则返回系统字体。
    /// - Parameter size: 字体大小
    /// - Returns: 配置好的字体
    static func customFont(size: CGFloat, for scope: FontScope) -> Font {
        if let fontName = FontManager.shared.getFontName(for: scope) {
            return .custom(fontName, size: size)
        } else {
            return .system(size: size)
        }
    }

    /// 返回一个带样式的自定义字体（如.bold()）。
    /// - Parameters:
    ///   - size: 字体大小
    ///   - weight: 字体粗细
    /// - Returns: 配置好的字体
    static func customFont(size: CGFloat, weight: Font.Weight, for scope: FontScope) -> Font {
        if let fontName = FontManager.shared.getFontName(for: scope) {
            // 注意：.custom() 创建的字体，其 weight 需要字体本身支持不同的字重文件。
            // 如果导入的字体只有一个字重，.weight() 修饰可能无效。
            // 一个更可靠的方法是根据 weight 加载不同的字体文件，例如 "MyFont-Bold", "MyFont-Regular"。
            // 这里我们先用简单的方式。
            return .custom(fontName, size: size).weight(weight)
        } else {
            return .system(size: size, weight: weight)
        }
    }
}

// 为了在 UIKit 环境中（例如 AttributedTextView）也能使用，我们同样扩展 UIFont
extension UIFont {
    /// 返回一个自定义的 UIFont，如果已设置；否则返回系统字体。
    /// - Parameter size: 字体大小
    /// - Returns: 配置好的 UIFont
    static func customFont(size: CGFloat, for scope: FontScope) -> UIFont {
        if let fontName = FontManager.shared.getFontName(for: scope) {
            return UIFont(name: fontName, size: size) ?? .systemFont(ofSize: size)
        } else {
            return .systemFont(ofSize: size)
        }
    }

    /// 返回一个带样式的自定义 UIFont。
    /// - Parameters:
    ///   - size: 字体大小
    ///   - weight: 字体粗细
    /// - Returns: 配置好的 UIFont
    static func customFont(size: CGFloat, weight: UIFont.Weight, for scope: FontScope) -> UIFont {
        if let fontName = FontManager.shared.getFontName(for: scope) {
            print("【调试】UIFont.customFont: 使用自定义字体 '\(fontName)', 请求大小=\(size), 权重=\(weight)")

            // 1. 创建基础字体描述符
            let baseDescriptor = UIFontDescriptor(name: fontName, size: size)

            // 2. 如果需要粗体，尝试获取加粗的描述符
            if weight == .bold {
                if let boldDescriptor = baseDescriptor.withSymbolicTraits(.traitBold) {
                    // 3. 用加粗的描述符创建字体
                    let boldFont = UIFont(descriptor: boldDescriptor, size: size)
                    print("【调试】UIFont.customFont: 创建粗体字体成功, 实际大小=\(boldFont.pointSize)")
                    return boldFont
                }
            }

            // 如果无法创建粗体，或者 weight 不是 bold，返回常规字体
            let regularFont = UIFont(name: fontName, size: size) ?? .systemFont(ofSize: size, weight: weight)
            print("【调试】UIFont.customFont: 创建常规字体, 实际大小=\(regularFont.pointSize)")
            return regularFont
        } else {
            print("【调试】UIFont.customFont: 使用系统字体, 请求大小=\(size), 权重=\(weight)")
            return .systemFont(ofSize: size, weight: weight)
        }
    }
}
