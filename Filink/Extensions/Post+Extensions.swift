import Foundation
import CoreData
import SwiftUI

// 假设你的 Core Data 实体名为 Post
// 如果你的实体名称不同，请将 'Post' 替换为实际的实体名称
extension Post {
    /// 将 Core Data 中存储的逗号分隔的 tags 字符串转换为 [String] 数组。
    /// 如果 tags 为 nil 或空字符串，则返回空数组。
    var tagsArray: [String] {
        get {
            guard let tagsString = self.tags, !tagsString.isEmpty else {
                return []
            }
            return tagsString.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
        }
        // 如果需要双向绑定或设置，可以添加 set 方法
        // set(newValue) {
        //     self.tags = newValue.joined(separator: ",")
        // }
    }
}
