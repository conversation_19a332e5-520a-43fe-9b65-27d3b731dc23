import UIKit

// 扩展UIFont提供加粗功能
extension UIFont {
    func withTraits(_ traits: UIFontDescriptor.SymbolicTraits) -> UIFont {
        // 尝试获取带有指定符号特征的字体描述符
        guard let descriptor = fontDescriptor.withSymbolicTraits(traits) else {
            // 如果失败（理论上不应发生），返回原始字体以避免崩溃
            return self
        }
        // 使用新的描述符创建字体，size: 0 表示使用原始字体大小
        return UIFont(descriptor: descriptor, size: 0)
    }
} 