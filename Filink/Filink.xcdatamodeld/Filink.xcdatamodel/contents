<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23605" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Comment" representedClassName="Comment" syncable="YES" codeGenerationType="class">
        <attribute name="cooked" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="like_count" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="plain_text_content" optional="YES" attributeType="String"/>
        <attribute name="postNumber" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="reply_count" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="replyToPostNumber" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="replyToUserId" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="shortContent" optional="YES" attributeType="String"/>
        <attribute name="updated_at" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="post" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Post" inverseName="comment" inverseEntity="Post"/>
    </entity>
    <entity name="Item" representedClassName="Item" syncable="YES" codeGenerationType="class">
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="Post" representedClassName="Post" syncable="YES" codeGenerationType="class">
        <attribute name="can_vote" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="category_id" optional="YES" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="contentHTML" optional="YES" attributeType="String"/>
        <attribute name="created_at" attributeType="Date"/>
        <attribute name="excerpt" optional="YES" attributeType="String"/>
        <attribute name="highest_post_number" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="Integer 64" usesScalarValueType="YES"/>
        <attribute name="image_url" optional="YES" attributeType="String"/>
        <attribute name="isActiveInHome" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isPined" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isRead" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="last_fetched" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="last_posted_at" optional="YES" attributeType="Date"/>
        <attribute name="lastFetched" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="like_count" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="linkCountsJson" optional="YES" attributeType="String"/>
        <attribute name="posts_count" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="reply_count" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="tags" optional="YES" attributeType="String"/>
        <attribute name="title" attributeType="String"/>
        <attribute name="updated_at" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="views" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="comment" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Comment" inverseName="post" inverseEntity="Comment"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="posts" inverseEntity="User"/>
    </entity>
    <entity name="User" representedClassName="User" syncable="YES" codeGenerationType="class">
        <attribute name="avatar" optional="YES" attributeType="String"/>
        <attribute name="displayUsername" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="username" optional="YES" attributeType="String"/>
        <attribute name="usersignature" optional="YES" attributeType="String"/>
        <attribute name="usertitle" optional="YES" attributeType="String"/>
        <relationship name="posts" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Post" inverseName="user" inverseEntity="Post"/>
    </entity>
    <entity name="UserCache" representedClassName="UserCache" syncable="YES" codeGenerationType="class">
        <attribute name="accessCount" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="avatarUrl" optional="YES" attributeType="String"/>
        <attribute name="lastAccessed" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userId" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="username" optional="YES" attributeType="String"/>
    </entity>
    <entity name="Draft" representedClassName="Draft" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" attributeType="String" defaultValueString="无标题草稿"/>
        <attribute name="htmlContent" attributeType="String"/>
        <attribute name="plainTextContent" optional="YES" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="wordCount" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isAutoSaved" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
    </entity>
</model>