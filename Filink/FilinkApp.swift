//
//  FilinkApp.swift
//  Filink
//
//  Created by wot on 2025/3/27.
//

// FilinkApp.swift
import SwiftUI
import CoreData // Import CoreData

@main
struct FilinkApp: App {

    // --- Core Data 设置 ---
    let persistenceController = CoreDataManager.shared

    // --- 状态管理器 ---
    @StateObject private var settingsManager = SettingsManager()
    @StateObject private var cookieManager = CookieManager.shared // 添加 CookieManager 声明
    @StateObject private var tagDataLoader = TagDataLoader.shared // 添加 TagDataLoader 声明
    
    // --- 网络服务层 ---
    @StateObject private var networkService: NetworkServiceImpl
    
    // --- 领域服务层 ---
    @StateObject private var postService: PostServiceImpl
    @StateObject private var commentService: CommentServiceImpl
    @StateObject private var userService: UserServiceImpl

    // --- 使用 init 来确保依赖顺序 ---
    init() {
        let persistence = CoreDataManager.shared
        let settings = SettingsManager.shared  // 使用单例而不是创建新实例
        let cookies = CookieManager.shared
        let tagData = TagDataLoader.shared // 获取 TagDataLoader 单例
        
        // 在应用启动时立即加载标签数据，确保只加载一次
        Task {
            print("【调试】FilinkApp: 应用启动时加载标签数据（仅一次）")
            await tagData.loadTagsIfNeeded()
        }
        
        // 创建网络服务
        let network = NetworkServiceImpl(
            cookieManager: cookies,
            settingsManager: settings
        )
        
        // 创建领域服务
        let post = PostServiceImpl(
            networkService: network,
            viewContext: persistence.container.viewContext
        )
        
        let comment = CommentServiceImpl(
            networkService: network,
            viewContext: persistence.container.viewContext
        )
        
        let user = UserServiceImpl(
            networkService: network,
            cookieManager: cookies,
            viewContext: persistence.container.viewContext
        )
        
        // 将共享实例也赋给 @StateObject 属性，以便 SwiftUI 管理
        _settingsManager = StateObject(wrappedValue: settings)
        _cookieManager = StateObject(wrappedValue: cookies)
        _tagDataLoader = StateObject(wrappedValue: tagData)
        
        // 注入服务实例
        _networkService = StateObject(wrappedValue: network)
        _postService = StateObject(wrappedValue: post)
        _commentService = StateObject(wrappedValue: comment)
        _userService = StateObject(wrappedValue: user)

        // 配置TabBar外观，解决内容不足时透明问题
        let tabBarAppearance = UITabBarAppearance()
        
        // 使用默认背景配置，保持亚克力效果
        tabBarAppearance.configureWithDefaultBackground()
        
        // 可以添加亚克力效果（iOS 15+），如果需要更明显的模糊效果
        if #available(iOS 15.0, *) {
            tabBarAppearance.backgroundEffect = UIBlurEffect(style: .systemMaterial)
        }
        
        // 关键：同时设置standardAppearance和scrollEdgeAppearance
        // 这样无论内容是否滚动，TabBar都会保持一致的外观
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance

        // 加载自定义字体
        FontManager.shared.loadCustomFonts()
        
        print("【调试】[MyApp] FilinkApp: All managers and services initialized.")
    }

    var body: some Scene {
        WindowGroup {
            // 恢复TabView作为主导航容器
            TabView {
                // 主题列表标签
                NavigationView {
                    FeedView()
                }
                .tabItem {
                    Label("主题", systemImage: "list.bullet")
                }
                
                // Feed标签
                // NavigationView {
                //     HomeView()
                // }
                // .tabItem {
                //     Label("动态", systemImage: "house.fill")
                // }
                
                // 设置标签
                NavigationView {
                    SettingsView()
                }
                .tabItem {
                    Label("设置", systemImage: "gear")
                }
                
                // 动态高度页面标签
                // NavigationView {
                //     DynamicHeightView()
                // }
                // .tabItem {
                //     Label("动态高度", systemImage: "arrow.up.and.down.circle.fill")
                // }

            }
            .withToast()
            // --- Inject environment objects (ensure all needed are here) ---
            .environment(\.managedObjectContext, persistenceController.container.viewContext) // Core Data Context
            .environmentObject(settingsManager) // Settings Manager
            .environmentObject(cookieManager) // Cookie Manager
            .environmentObject(tagDataLoader) // Tag Data Loader
            
            // --- 新的模块化服务 ---
            .environmentObject(networkService)
            .environmentObject(postService)
            .environmentObject(commentService)
            .environmentObject(userService)
            .environmentObject(ScrollStateManager.shared) // 注入全局滚动状态管理器
        }
    }
}
