// BaseLoginViewController.swift
import UIKit
@preconcurrency import WebKit

class BaseLoginViewController: UIViewController {
    var initialURL: URL?
    var settingsManager: SettingsManager!
    var cookieManager: CookieManager!

    // 共同的UI组件
    var webView: WKWebView!
    var activityIndicator: UIActivityIndicatorView! // 用于页面初始加载
    var isLoginProcessReported = false
    var webViewUserAgent: String?

    // CloudFlare验证状态相关
    var overlayLoadingView: UIView?
    var overlayActivityIndicator: UIActivityIndicatorView?
    var overlayLabel: UILabel?
    var overlayShownDueToVerification = false // 用于跟踪是否显示了遮罩层
    
    // 常量
    private enum Constants {
        static let defaultUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        setupWebView()
        setupNavigationBar()
        setupActivityIndicator() // 页面加载菊花
        setupOverlayLoadingView() // 覆盖层，它的约束会参考 webView
        startLoadingAnimation() // 进入视图立刻显示菊花
        loadInitialPage()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        getWebViewUserAgent()
    }

    // MARK: - 导航处理模板方法
    
    /// 处理导航完成的模板方法，子类可以重写 processSpecificNavigation 进行特定处理
    /// - Parameter url: 当前导航的 URL
    func handleNavigationFinish(url: URL?) {
        // 先停止页面加载菊花
        stopLoadingAnimation()
        
        // 检查 CloudFlare URL 状态
        if let url = url {
            let cfStatus = checkCloudFlareURL(url)
            if cfStatus.isCloudFlare {
                let isHomePage = isMainSiteHomepage(url)
                
                print("【调试】BaseLoginVC: didFinish: 检测到CloudFlare URL - 标准URL: \(cfStatus.isStandardKey), 主页: \(isHomePage)")
                
                if isHomePage && cfStatus.isStandardKey {
                    if overlayLoadingView?.isHidden ?? true {
                        showBackgroundVerificationOverlay()
                    }
                } else {
                    hideBackgroundVerificationOverlay()
                }
                return
            }
        }
        
        // 调用子类特定处理
        processSpecificNavigation(url: url)
    }
    
    /// 子类可以重写此方法处理特定导航逻辑
    /// - Parameter url: 当前导航的 URL
    func processSpecificNavigation(url: URL?) {
        // 基类默认实现为空
    }

    // MARK: - UI设置方法
    
    /// 设置 WebView - 子类必须重写此方法
    func setupWebView() {
        // 这是一个虚方法，子类必须重写
        fatalError("子类必须重写 setupWebView 方法")
    }

    func setupNavigationBar() {
        navigationItem.title = "登录"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel, target: self,
            action: #selector(cancelTapped))
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .refresh, target: self,
            action: #selector(refreshTapped))
    }

    func setupActivityIndicator() {
        activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator.center = view.center
        activityIndicator.hidesWhenStopped = true
        activityIndicator.autoresizingMask = [
            .flexibleLeftMargin, .flexibleRightMargin, .flexibleTopMargin,
            .flexibleBottomMargin,
        ]
        view.addSubview(activityIndicator)
        view.bringSubviewToFront(activityIndicator)
    }

    func setupOverlayLoadingView() {
        let overlay = UIView()
        overlay.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        overlay.translatesAutoresizingMaskIntoConstraints = false

        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .white
        indicator.startAnimating()

        let label = UILabel()
        label.text = "正在准备您的账户..." // 默认文本
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 0

        let stackView = UIStackView(arrangedSubviews: [indicator, label])
        stackView.axis = .vertical
        stackView.alignment = .center
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false

        overlay.addSubview(stackView)
        NSLayoutConstraint.activate([
            stackView.centerXAnchor.constraint(equalTo: overlay.centerXAnchor),
            stackView.centerYAnchor.constraint(equalTo: overlay.centerYAnchor),
            stackView.leadingAnchor.constraint(greaterThanOrEqualTo: overlay.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(lessThanOrEqualTo: overlay.trailingAnchor, constant: -20)
        ])

        overlay.isHidden = true
        self.view.addSubview(overlay)
        NSLayoutConstraint.activate([
            overlay.topAnchor.constraint(equalTo: webView.topAnchor),
            overlay.bottomAnchor.constraint(equalTo: webView.bottomAnchor),
            overlay.leadingAnchor.constraint(equalTo: webView.leadingAnchor),
            overlay.trailingAnchor.constraint(equalTo: webView.trailingAnchor)
        ])

        self.overlayLoadingView = overlay
        self.overlayActivityIndicator = indicator
        self.overlayLabel = label
    }

    // MARK: - 动画控制
    func startLoadingAnimation() {
        DispatchQueue.main.async { 
            self.activityIndicator?.startAnimating() 
        }
    }
    
    func stopLoadingAnimation() {
        DispatchQueue.main.async { 
            self.activityIndicator?.stopAnimating() 
        }
    }

    // MARK: - 覆盖层控制
    func showBackgroundVerificationOverlay(
        mainMessage: String = "正在验证账户...",
        subMessage: String? = "（取决于您的网络和设备性能）"
    ) {
        DispatchQueue.main.async {
            if self.activityIndicator?.isAnimating ?? false {
                print("【调试】BaseLoginVC: showBackgroundVerificationOverlay - 停止页面菊花，准备显示覆盖层。")
                self.stopLoadingAnimation()
            }
            
            print("【调试】BaseLoginVC: 显示后台验证覆盖层。主消息: \(mainMessage), 副消息: \(subMessage ?? "无")")
            
            var fullMessage = mainMessage
            if let sub = subMessage, !sub.isEmpty {
                fullMessage += "\n" + sub
            }
            self.overlayLabel?.text = fullMessage

            self.overlayLoadingView?.isHidden = false
            if let overlay = self.overlayLoadingView {
                self.view.bringSubviewToFront(overlay)
            }
            self.webView?.isUserInteractionEnabled = false
            self.overlayActivityIndicator?.startAnimating()
            
            self.overlayShownDueToVerification = true
        }
    }

    func hideBackgroundVerificationOverlay() {
        DispatchQueue.main.async {
            if !(self.overlayLoadingView?.isHidden ?? true) {
                 print("【调试】BaseLoginVC: 隐藏后台验证覆盖层。")
            }
            self.overlayLoadingView?.isHidden = true
            self.webView?.isUserInteractionEnabled = true
            self.overlayShownDueToVerification = false
        }
    }

    // MARK: - 辅助方法
    func isIntermediateURL(_ url: URL?) -> Bool {
        guard let url = url else { return false }
        let urlString = url.absoluteString
        return urlString == "about:blank" || urlString.hasPrefix("data:") || urlString.hasPrefix("about:srcdoc")
    }
    
    /// 判断URL是否为不重要导航（不需要记录日志或特殊处理的URL）
    /// - Parameter url: 需要判断的URL
    /// - Returns: 如果是不重要的URL返回true，否则返回false
    func isUnimportantURL(_ url: URL?) -> Bool {
        guard let url = url else { return false }
        return url.absoluteString == "about:blank" || 
               url.scheme == "data" ||
               url.host?.contains("gstatic.com") == true ||
               url.host?.contains("google.com") == true ||
               url.host?.contains("stripe") == true
    }

    func checkCloudFlareURL(_ url: URL?) -> (isCloudFlare: Bool, isStandardKey: Bool) {
        guard let url = url, let urlString = url.absoluteString as String? else { return (false, false) }
        
        // 检查是否是CloudFlare相关URL
        let isCloudFlare = urlString.contains("cloudflare") && (urlString.contains("challenge") || urlString.contains("turnstile"))
        
        // 如果不是CloudFlare URL，直接返回
        if !isCloudFlare {
            return (false, false)
        }
        
        // 检查是否包含标准的site-cf-key
        if urlString.contains(settingsManager.siteCFKey) {
            return (true, true)
        }
        
        // 提取URL中的site-cf-key值
        if urlString.contains("cloudflare.com") && urlString.contains("challenge-platform") {
            // 查找包含"0x"开头的组件
            for component in urlString.components(separatedBy: "/") {
                if component.hasPrefix("0x") {
                    return (true, component == settingsManager.siteCFKey)
                }
            }
        }
        
        // 是CloudFlare URL但没有找到标准key
        return (true, false)
    }

    func isMainSiteHomepage(_ url: URL?) -> Bool {
        guard let url = url,
              let mainSiteURLString = settingsManager?.website,
              let mainSiteURL = URL(string: mainSiteURLString),
              let mainSiteHost = mainSiteURL.host
        else {
            return false
        }
        // 检查 host 是否匹配，并且 path 是否为 "/" (首页)
        return url.host == mainSiteHost && url.path == "/"
    }

    func isMainFrameNavigation(_ navigationAction: WKNavigationAction) -> Bool {
        return navigationAction.targetFrame?.isMainFrame ?? false
    }

    // MARK: - 错误处理
    func handleError(_ error: Error) {
        let nsError = error as NSError
        print("【调试】WebView 加载错误: \(nsError.localizedDescription), Code: \(nsError.code)")
        
        DispatchQueue.main.async {
            self.hideBackgroundVerificationOverlay() // 错误时隐藏覆盖层

            guard !self.isLoginProcessReported else { return }

            guard !self.isBeingDismissed, self.presentingViewController != nil else {
                 if !self.isLoginProcessReported {
                    self.reportResultAndDismiss(success: false, cookies: nil, userAgent: self.webViewUserAgent)
                 }
                 return
             }
             
            let alert = UIAlertController(
                title: "加载错误", message: error.localizedDescription,
                preferredStyle: .alert)
            alert.addAction(
                UIAlertAction(title: "好的", style: .default) { _ in
                    if !self.isLoginProcessReported {
                        self.reportResultAndDismiss(
                            success: false, cookies: nil,
                            userAgent: self.webViewUserAgent)
                    }
                })
                
            if self.presentedViewController == nil {
                self.present(alert, animated: true)
            } else if !self.isLoginProcessReported {
                self.reportResultAndDismiss(success: false, cookies: nil, userAgent: self.webViewUserAgent)
            }
        }
    }

    // MARK: - 用户代理获取
    func getWebViewUserAgent() {
        webView.evaluateJavaScript("navigator.userAgent") { result, error in
            if let userAgent = result as? String {
                self.webViewUserAgent = userAgent
                print("【调试】✅ BaseLoginVC: 从 WebView 获取到 User-Agent: \(String(describing: userAgent.prefix(30)))...")
            } else if let error = error {
                print("【调试】❌ BaseLoginVC: 获取 WebView User-Agent 失败: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - 公共方法
    func retrieveAllCurrentWebViewCookies(completion: @escaping ([HTTPCookie]) -> Void) {
        print("【调试】BaseLoginVC: 被外部请求获取当前所有 WebView Cookies。")
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { cookies in
            DispatchQueue.main.async {
                completion(cookies)
            }
        }
    }

    // MARK: - 需要子类实现的方法
    @objc func cancelTapped() {
        print("【调试】用户点击取消按钮。")
        reportResultAndDismiss(success: false, cookies: nil, userAgent: webViewUserAgent)
    }

    @objc func refreshTapped() {
        print("【调试】用户点击刷新按钮，重新加载 WebView。")
        isLoginProcessReported = false // 允许重新报告
        startLoadingAnimation()
        webView.reload()
    }

    func loadInitialPage() {
        // 子类必须实现
        fatalError("子类必须实现loadInitialPage方法")
    }

    func reportResultAndDismiss(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
        // 子类必须实现
        fatalError("子类必须实现reportResultAndDismiss方法")
    }
}

// MARK: - WKNavigationDelegate
extension BaseLoginViewController: WKNavigationDelegate {
    
    /// 处理URL导航的通用方法
    /// - Parameters:
    ///   - url: 正在导航的URL
    ///   - stage: 导航阶段的描述（用于日志）
    ///   - isMainFrame: 是否是主框架导航
    ///   - currentURL: 当前显示的URL（通常是webView.url）
    /// - Returns: 是否已处理（如果返回true，调用方可以提前返回）
    private func handleNavigationCommon(url: URL?, stage: String, isMainFrame: Bool = true, currentURL: URL? = nil) -> Bool {
        guard let url = url else { return false }
        
        // 日志记录
        if !isUnimportantURL(url) {
            let mainFrameLog = isMainFrame ? "" : ", isMainFrame: \(isMainFrame)"
            print("【调试】BaseLoginVC: \(stage) - URL: \(url.absoluteString)\(mainFrameLog)")
        }
        
        // 检查CloudFlare URL状态
        let cfStatus = checkCloudFlareURL(url)
        if cfStatus.isCloudFlare {
            let targetURL = currentURL ?? url
            let isHomePage = isMainSiteHomepage(targetURL)
            
            print("【调试】BaseLoginVC: \(stage): 检测到CloudFlare URL - 标准URL: \(cfStatus.isStandardKey), 主页: \(isHomePage)")
            
            // 触发CloudFlare URL处理钩子，供子类重写实现特定逻辑（如启动Cookie验证）
            handleCloudFlareURL(url: url, isStandardKey: cfStatus.isStandardKey, isHomePage: isHomePage)
            
            // 根据条件显示或隐藏遮罩层
            if isHomePage && cfStatus.isStandardKey {
                if overlayLoadingView?.isHidden ?? true {
                    showBackgroundVerificationOverlay()
                }
            } else {
                hideBackgroundVerificationOverlay()
            }
            
            return true
        }
        
        return false
    }
    
    /// 处理CloudFlare URL的钩子方法，子类可重写实现特定逻辑
    /// - Parameters:
    ///   - url: CloudFlare URL
    ///   - isStandardKey: 是否是标准CloudFlare Key
    ///   - isHomePage: 当前是否在主页
    @objc open func handleCloudFlareURL(url: URL, isStandardKey: Bool, isHomePage: Bool) {
        // 基类提供空实现，子类可以根据需要重写
    }
    
    /// 处理导航错误的通用方法
    /// - Parameters:
    ///   - error: 错误对象
    ///   - stage: 错误发生的阶段（用于日志）
    private func handleNavigationError(error: Error, stage: String) {
        print("【调试】BaseLoginVC: \(stage) - Error: \(error.localizedDescription)")
        stopLoadingAnimation()
        hideBackgroundVerificationOverlay()
        handleError(error)
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        guard let url = navigationAction.request.url else {
            decisionHandler(.allow)
            return
        }
        
        let isMainFrameNav = isMainFrameNavigation(navigationAction)
        
        // 调用通用处理方法
        if handleNavigationCommon(url: url, stage: "decidePolicyFor", isMainFrame: isMainFrameNav, currentURL: webView.url) {
            decisionHandler(.allow)
            return
        }

        // 如果是主站点主页，并且是主框架导航，默认不显示遮罩层（等CloudFlare URL触发时再决定）
        if isMainSiteHomepage(url) && isMainFrameNav {
            print("【调试】BaseLoginVC: decidePolicyFor: 检测到主站点主页导航")
            decisionHandler(.allow)
            return
        }

        if isLoginProcessReported {
            print("【调试】BaseLoginVC: 登录已报告，直接允许")
            decisionHandler(.allow)
            return
        }
        
        // 子资源请求不应触发覆盖层
        if !isMainFrameNav {
            if !isIntermediateURL(url) {
                print("【调试】BaseLoginVC: 子资源请求，不影响页面状态")
            }
            decisionHandler(.allow)
            return
        }
        
        // 如果不是主页，覆盖层没显示，考虑页面菊花
        if !isMainSiteHomepage(url) {
            if activityIndicator?.isAnimating == false && (overlayLoadingView?.isHidden ?? true) {
                print("【调试】BaseLoginVC: 启动页面菊花")
                startLoadingAnimation()
            }
        }
        
        decisionHandler(.allow)
    }

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        // 调用通用处理方法
        if handleNavigationCommon(url: webView.url, stage: "didStartProvisionalNavigation") {
            return
        }

        // 如果覆盖层已经显示，则确保页面菊花是停止的
        if overlayShownDueToVerification || !(overlayLoadingView?.isHidden ?? true) {
            if activityIndicator?.isAnimating ?? false {
                print("【调试】BaseLoginVC: didStartProvisionalNavigation: 覆盖层已显示或即将显示，停止页面菊花")
                stopLoadingAnimation()
            }
            return
        }

        // 如果是非主页且非中间状态URL，显示菊花
        if let url = webView.url, !isMainSiteHomepage(url) && !isIntermediateURL(url) {
            if activityIndicator?.isAnimating == false {
                print("【调试】BaseLoginVC: 启动页面菊花")
                startLoadingAnimation()
            }
        }
    }

    func webView(_ webView: WKWebView, didReceiveServerRedirectForProvisionalNavigation navigation: WKNavigation!) {
        // 仅调用通用处理方法记录日志
        _ = handleNavigationCommon(url: webView.url, stage: "didReceiveServerRedirect")
    }
    
    func webView(_ webView: WKWebView, didCommit navigation: WKNavigation!) {
        // 仅调用通用处理方法记录日志
        _ = handleNavigationCommon(url: webView.url, stage: "didCommit")
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // 调用通用处理方法记录日志
        _ = handleNavigationCommon(url: webView.url, stage: "didFinish")
        
        // 使用模板方法处理导航完成
        handleNavigationFinish(url: webView.url)
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        handleNavigationError(error: error, stage: "didFail")
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        handleNavigationError(error: error, stage: "didFailProvisionalNavigation")
    }

    func webView(_ webView: WKWebView, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        completionHandler(.performDefaultHandling, nil)
    }
} 