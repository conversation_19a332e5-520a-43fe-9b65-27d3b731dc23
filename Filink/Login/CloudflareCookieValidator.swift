import Foundation
import WebKit

/// 负责验证 Cloudflare 保护的站点的 Cookie 的类，特别是 cf_clearance cookie 的变化
class CloudflareCookieValidator {
    // MARK: - 属性
    
    /// WebView 实例，用于获取 Cookie
    private weak var webView: WKWebView?
    
    /// 存储当前 cf_clearance cookie 的值，用于检测变化
    private var currentCfClearanceValue: String?
    
    /// 记录 cf_clearance 值变化的次数
    private var cfClearanceChangeCount = 0
    
    /// 是否应该持续验证
    private var shouldContinueValidation = false
    
    /// 是否正在进行验证（公开属性，供外部检查状态）
    private(set) var isValidating = false
    
    /// 要检查的 Cookie 名称
    private enum CookieNames {
        static let cfClearance = "cf_clearance"
        static let tCookie = "_t"
    }
    
    /// 验证配置
    private enum ValidationConfig {
        /// 检查 Cookie 间隔时间
        static let validationInterval: TimeInterval = 1.0
        
        /// 需要检测到的 cf_clearance 值变化次数 - 从无到有需要 2 次变化，已有值需要 3 次变化
        /// 但如果已经同时存在 _t 和 cf_clearance，则只需要 1 次变化
        static let requiredChangesWhenStartsEmpty = 2
        static let requiredChangesWhenStartsWithValue = 3
        static let requiredChangesWhenStartsWithBothCookies = 1
        
        /// 超时时间（秒）
        static let validationTimeout: TimeInterval = 30.0
    }
    
    /// 验证计时器
    private var validationTimer: Timer?
    
    /// 超时计时器
    private var timeoutTimer: Timer?
    
    /// 成功回调
    private var successCallback: (([HTTPCookie]) -> Void)?
    
    /// 失败回调
    private var failureCallback: (([HTTPCookie]) -> Void)?
    
    /// 是否需要检查 _t cookie
    private var shouldCheckTCookie = false
    
    /// 存储验证开始时是否已有 cf_clearance cookie
    private var hadInitialCfClearanceCookie = false
    
    /// 存储验证开始时是否已有 _t cookie
    private var hadInitialTCookie = false
    
    /// 是否是游客模式
    private var isGuestMode = false
    
    // MARK: - 初始化
    
    /// 初始化验证器
    /// - Parameter webView: 要验证的 WebView
    init(webView: WKWebView) {
        self.webView = webView
        print("【调试】CloudflareCookieValidator: 初始化，WebView DataStore 类型: \(webView.configuration.websiteDataStore == .default() ? "默认(持久)" : "非持久")")
    }
    
    deinit {
        stopValidation()
        print("【调试】CloudflareCookieValidator DEINIT")
    }
    
    // MARK: - 公共方法
    
    /// 开始 Cookie 验证
    /// - Parameters:
    ///   - checkTCookie: 是否需要检查 _t cookie（登录状态）
    ///   - onSuccess: 验证成功回调
    ///   - onFailure: 验证失败回调
    func startValidation(checkTCookie: Bool, onSuccess: @escaping ([HTTPCookie]) -> Void, onFailure: @escaping ([HTTPCookie]) -> Void) {
        guard let webView = webView else {
            print("【调试】Cookie验证: WebView 为 nil，无法启动验证")
            return
        }
        
        // 如果已经在验证，避免重复启动
        if isValidating {
            print("【调试】Cookie验证: 已经在进行验证，跳过本次启动")
            return
        }
        
        // 保存回调
        self.successCallback = onSuccess
        self.failureCallback = onFailure
        self.shouldCheckTCookie = checkTCookie
        self.isGuestMode = !checkTCookie // 根据是否检查 _t cookie 判断是否为游客模式
        
        // 重置状态
        self.shouldContinueValidation = true
        self.isValidating = true
        self.cfClearanceChangeCount = 0
        self.currentCfClearanceValue = nil
        self.hadInitialCfClearanceCookie = false
        self.hadInitialTCookie = false
        
        // 检查当前是否已有 cf_clearance cookie 和 _t cookie
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { [weak self] cookies in
            guard let self = self else { return }
            
            let cfCookie = cookies.first(where: { $0.name == CookieNames.cfClearance })
            let tCookie = cookies.first(where: { $0.name == CookieNames.tCookie })
            
            if let cfCookie = cfCookie {
                self.hadInitialCfClearanceCookie = true
                self.currentCfClearanceValue = cfCookie.value
                print("【调试】Cookie验证: 启动时已有 cf_clearance cookie，值为: \(cfCookie.value.prefix(5))...")
            } else {
                self.hadInitialCfClearanceCookie = false
                print("【调试】Cookie验证: 启动时没有 cf_clearance cookie")
            }
            
            if let tCookie = tCookie, !tCookie.value.isEmpty {
                self.hadInitialTCookie = true
                print("【调试】Cookie验证: 启动时已有 _t cookie，值为: \(tCookie.value.prefix(5))...")
            } else {
                self.hadInitialTCookie = false
                print("【调试】Cookie验证: 启动时没有 _t cookie 或值为空")
            }
            
            // 如果已经同时存在 _t 和 cf_clearance，打印日志
            if self.hadInitialCfClearanceCookie && self.hadInitialTCookie && self.shouldCheckTCookie {
                print("【调试】Cookie验证: 启动时已同时存在 cf_clearance 和 _t cookie，只需要 cf_clearance 变化一次即可成功")
            }
            
            // 打印是否是游客模式
            print("【调试】Cookie验证: 当前模式: \(self.isGuestMode ? "游客模式" : "正常登录模式")")
            
            // 启动验证计时器
            self.startValidationTimer()
            
            // 启动超时计时器
            self.startTimeoutTimer()
        }
    }
    
    /// 停止验证
    func stopValidation() {
        print("【调试】Cookie验证: 停止验证")
        shouldContinueValidation = false
        isValidating = false
        invalidateTimers()
    }
    
    // MARK: - 私有方法
    
    /// 启动验证计时器
    private func startValidationTimer() {
        // 先清除之前的计时器
        validationTimer?.invalidate()
        
        print("【调试】Cookie验证: 正在设置验证计时器，间隔: \(ValidationConfig.validationInterval)秒")
        
        // 创建新的计时器
        validationTimer = Timer.scheduledTimer(
            timeInterval: ValidationConfig.validationInterval,
            target: self,
            selector: #selector(checkCookies),
            userInfo: nil,
            repeats: true
        )
        
        // 确保计时器被添加到当前运行循环
        RunLoop.current.add(validationTimer!, forMode: .common)
        print("【调试】Cookie验证: 验证计时器已启动并添加到RunLoop")
    }
    
    /// 启动超时计时器
    private func startTimeoutTimer() {
        // 先清除之前的计时器
        timeoutTimer?.invalidate()
        
        print("【调试】Cookie验证: 正在设置超时计时器，时长: \(ValidationConfig.validationTimeout)秒")
        
        // 创建新的计时器
        timeoutTimer = Timer.scheduledTimer(
            timeInterval: ValidationConfig.validationTimeout,
            target: self,
            selector: #selector(handleTimeout),
            userInfo: nil,
            repeats: false
        )
        
        // 确保计时器被添加到当前运行循环
        RunLoop.current.add(timeoutTimer!, forMode: .common)
        print("【调试】Cookie验证: 超时计时器已启动并添加到RunLoop")
    }
    
    /// 清除所有计时器
    private func invalidateTimers() {
        validationTimer?.invalidate()
        validationTimer = nil
        
        timeoutTimer?.invalidate()
        timeoutTimer = nil
    }
    
    /// 检查 Cookie 的变化
    @objc private func checkCookies() {
        guard shouldContinueValidation, let webView = webView else { 
            print("【调试】Cookie验证: checkCookies被调用，但shouldContinueValidation=false或webView=nil")
            return 
        }
        
        print("【调试】Cookie验证: 正在检查Cookie变化...")
        
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { [weak self] cookies in
            guard let self = self, self.shouldContinueValidation else { return }
            
            print("【调试】Cookie验证: 获取到 \(cookies.count) 个Cookie")
            
            // 检查并打印所有cookie名称，帮助调试
            if !cookies.isEmpty {
                let cookieNames = cookies.map { $0.name }.joined(separator: ", ")
                print("【调试】Cookie验证: 当前Cookie名称: \(cookieNames)")
            }
            
            // 检查 cf_clearance cookie
            let cfCookie = cookies.first(where: { $0.name == CookieNames.cfClearance })
            
            if let cfCookie = cfCookie {
                print("【调试】Cookie验证: 当前cf_clearance值: \(cfCookie.value.prefix(10))...")
                
                // 如果 cf_clearance 值变化，增加计数
                if cfCookie.value != self.currentCfClearanceValue {
                    self.cfClearanceChangeCount += 1
                    print("【调试】Cookie验证: cf_clearance 值变化 (\(self.cfClearanceChangeCount)): \(self.currentCfClearanceValue?.prefix(5) ?? "nil") -> \(cfCookie.value.prefix(5))...")
                    self.currentCfClearanceValue = cfCookie.value
                    
                    // 确定需要的变化次数
                    let requiredChanges: Int
                    if self.hadInitialCfClearanceCookie && self.hadInitialTCookie && self.shouldCheckTCookie {
                        // 如果同时存在 _t 和 cf_clearance，且需要检查 _t，则只需要变化一次
                        requiredChanges = ValidationConfig.requiredChangesWhenStartsWithBothCookies
                    } else if self.hadInitialCfClearanceCookie {
                        // 如果只有 cf_clearance 存在，则需要变化三次
                        requiredChanges = ValidationConfig.requiredChangesWhenStartsWithValue
                    } else {
                        // 如果从无到有，则需要变化两次
                        requiredChanges = ValidationConfig.requiredChangesWhenStartsEmpty
                    }
                    
                    print("【调试】Cookie验证: 当前变化次数: \(self.cfClearanceChangeCount), 需要变化次数: \(requiredChanges)")
                    
                    // 检查是否达到要求的变化次数
                    if self.cfClearanceChangeCount >= requiredChanges {
                        // 如果需要检查 _t cookie，确保它存在
                        if self.shouldCheckTCookie {
                            if cookies.contains(where: { $0.name == CookieNames.tCookie && !$0.value.isEmpty }) {
                                print("【调试】Cookie验证: 验证成功! cf_clearance 变化次数: \(self.cfClearanceChangeCount)，_t cookie 存在")
                                self.stopValidation()
                                self.successCallback?(cookies)
                            } else {
                                // 继续等待，直到 _t cookie 也出现
                                print("【调试】Cookie验证: cf_clearance 变化次数已满足，但 _t cookie 尚未出现，继续等待...")
                            }
                        } else {
                            // 不需要检查 _t cookie（游客模式），直接报告成功
                            print("【调试】Cookie验证: 验证成功! cf_clearance 变化次数: \(self.cfClearanceChangeCount)，游客模式无需检查 _t cookie")
                            self.stopValidation()
                            self.successCallback?(cookies)
                        }
                    }
                } else {
                    print("【调试】Cookie验证: cf_clearance 值未变化，保持监控")
                }
            } else {
                print("【调试】Cookie验证: 未找到 cf_clearance cookie")
            }
        }
    }
    
    /// 处理超时
    @objc private func handleTimeout() {
        guard shouldContinueValidation, let webView = webView else { 
            print("【调试】Cookie验证: handleTimeout被调用，但shouldContinueValidation=false或webView=nil")
            return 
        }
        
        print("【调试】Cookie验证: 验证超时 (\(ValidationConfig.validationTimeout)秒)，准备停止验证")
        
        // 获取当前 cookie 并调用失败回调
        webView.configuration.websiteDataStore.httpCookieStore.getAllCookies { [weak self] cookies in
            guard let self = self else { return }
            
            print("【调试】Cookie验证: 超时时有 \(cookies.count) 个Cookie")
            if !cookies.isEmpty {
                let cookieNames = cookies.map { $0.name }.joined(separator: ", ")
                print("【调试】Cookie验证: 超时时Cookie名称: \(cookieNames)")
            }
            
            self.stopValidation()
            self.failureCallback?(cookies)
        }
    }
} 