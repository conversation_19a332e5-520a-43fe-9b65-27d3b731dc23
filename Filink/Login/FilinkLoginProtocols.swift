// FilinkLoginProtocols.swift
import Foundation
import WebKit

// MARK: - 通用登录委托协议（基础）
/// 所有登录委托的基础协议
protocol FilinkLoginDelegate: AnyObject {
    /// 获取 Cookie 和 UserAgent
    /// - Parameters:
    ///   - success: 是否成功
    ///   - cookies: 获取到的 Cookie
    ///   - userAgent: 获取到的 UserAgent
    func didFinishCookieOperation(success: Bool, cookies: [HTTPCookie]?, userAgent: String?)
}

// MARK: - 登录委托协议
/// 正常登录的委托协议
protocol FilinkLoginViewControllerDelegate: FilinkLoginDelegate {
    /// 登录完成
    /// - Parameters:
    ///   - success: 是否登录成功
    ///   - cookies: 获取到的 Cookie
    ///   - userAgent: 获取到的 UserAgent
    func didFinishLogin(success: Bool, cookies: [HTTPCookie]?, userAgent: String?)
}

// MARK: - 游客访问委托协议
/// 游客访问的委托协议
protocol FilinkGuestViewControllerDelegate: FilinkLoginDelegate {
    /// 游客访问完成
    /// - Parameters:
    ///   - success: 是否获取成功
    ///   - cookies: 获取到的 Cookie
    ///   - userAgent: 获取到的 UserAgent
    func didFinishGuestAccess(success: Bool, cookies: [HTTPCookie]?, userAgent: String?)
}

// MARK: - 默认实现扩展
extension FilinkLoginViewControllerDelegate {
    func didFinishLogin(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
        didFinishCookieOperation(success: success, cookies: cookies, userAgent: userAgent)
    }
}

extension FilinkGuestViewControllerDelegate {
    func didFinishGuestAccess(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
        didFinishCookieOperation(success: success, cookies: cookies, userAgent: userAgent)
    }
} 