// GuestViewController.swift
import UIKit
@preconcurrency import WebKit

// 协议已移至FilinkLoginProtocols.swift
class GuestViewController: BaseLoginViewController {
    weak var delegate: FilinkGuestViewControllerDelegate?
    
    // 新增 Cookie 验证器
    private var cookieValidator: CloudflareCookieValidator?
    
    // 常量
    private enum Constants {
        static let cookieCheckDelay: TimeInterval = 1.0
        static let defaultUserAgent: String = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
    }

    // deinit 用于调试
    deinit {
        cookieValidator?.stopValidation()
        cookieValidator = nil
        print("【调试】GuestViewController DEINIT - 实例已销毁。isLoginProcessReported: \(isLoginProcessReported)")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 创建 Cookie 验证器
        cookieValidator = CloudflareCookieValidator(webView: webView)
    }
    
    // 重写 setupWebView 方法，使用非持久化的数据存储
    override func setupWebView() {
        // 使用非持久化的数据存储，以实现 WebView 隔离
        // Cookie 的持久化将通过 CookieManager 手动管理
        let guestDataStore = WKWebsiteDataStore.nonPersistent()
        
        let configuration = WKWebViewConfiguration()
        configuration.websiteDataStore = guestDataStore // 将独立的 DataStore 分配给配置
        
        webView = WKWebView(frame: .zero, configuration: configuration)
        webView.translatesAutoresizingMaskIntoConstraints = false
        webView.navigationDelegate = self
        webView.customUserAgent = Constants.defaultUserAgent
        webView.isOpaque = false
        webView.backgroundColor = .clear
        webView.scrollView.backgroundColor = .clear
        
        view.addSubview(webView)
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }

    override func setupNavigationBar() {
        navigationItem.title = "游客访问" // 设置特定标题
        super.setupNavigationBar() // 调用基类方法完成其余设置
    }

    override func loadInitialPage() {
        guard let url = initialURL else {
            print("【调试】GuestVC: initialURL 为 nil，无法加载页面。")
            return
        }
        
        // 初始化Cookie验证器，但不立即启动验证，等待检测到CloudFlare URL时启动
        if cookieValidator == nil {
            cookieValidator = CloudflareCookieValidator(webView: webView)
            print("【调试】GuestVC: 初始化 CloudflareCookieValidator，等待检测到CloudFlare URL时启动")
        }
        
        print("【调试】GuestVC: 加载初始页面: \(url.absoluteString)")
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    override func refreshTapped() {
        print("【调试】用户点击刷新按钮，重新加载 WebView。")
        isLoginProcessReported = false // 允许重新报告
        cookieValidator?.stopValidation() // 停止当前的验证
        startLoadingAnimation()
        
        // 重置Cookie验证器，让它在下一次检测到CloudFlare URL时重新启动
        cookieValidator = CloudflareCookieValidator(webView: webView)
        print("【调试】GuestVC: 重置 CloudflareCookieValidator，等待检测到CloudFlare URL时启动")
        
        webView.reload()
    }

    override func reportResultAndDismiss(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
        DispatchQueue.main.async {
            // 检查是否已经报告过
            guard !self.isLoginProcessReported else {
                print("【调试】GuestVC: reportResultAndDismiss - 已经报告过，检查是否需要隐藏 overlay。")
                if !(self.overlayLoadingView?.isHidden ?? true) {
                    self.hideBackgroundVerificationOverlay()
                }
                return
            }

            // 在调用 delegate 之前，确保覆盖层是隐藏的
            self.hideBackgroundVerificationOverlay() // 确保隐藏
            self.stopLoadingAnimation() // 停止页面初始加载菊花
            self.cookieValidator?.stopValidation() // 停止验证

            self.isLoginProcessReported = true
            print(
                "【调试】GuestVC: 调用 delegate.didFinishGuestAccess(success: \(success), cookies: \(cookies?.count ?? 0)个)。"
            )
            self.delegate?.didFinishGuestAccess(
                success: success, cookies: cookies, userAgent: self.webViewUserAgent)
        }
    }
    
    override func handleError(_ error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.handleError(error)
    }
    
    // MARK: - 模板方法实现
    override func processSpecificNavigation(url: URL?) {
        guard let url = url else { return }
        
        // 如果是主站点主页，确保遮罩层显示
        if isMainSiteHomepage(url) {
            print("【调试】GuestVC: didFinish: 已导航到主站点主页")
            if overlayLoadingView?.isHidden ?? true {
                showBackgroundVerificationOverlay(mainMessage: "正在验证账户...", 
                                                 subMessage: "（云盾验证中，请稍候）")
            }
            return
        }
        
        // 如果是主网站的其他页面，确保遮罩层隐藏
        if isMainWebsite(url) {
            print("【调试】GuestVC: didFinish: 检测到主网站非主页，确保遮罩层隐藏")
            hideBackgroundVerificationOverlay()
            return
        }
    }
    
    /// 判断给定的 URL 是否为主网站（而不是其他外部页面）
    func isMainWebsite(_ url: URL?) -> Bool {
        guard let url = url,
              let mainSiteURLString = settingsManager?.website,
              let mainSiteURL = URL(string: mainSiteURLString),
              let mainSiteHost = mainSiteURL.host
        else {
            return false
        }
        
        // 检查host是否匹配主网站
        return url.host == mainSiteHost
    }
    
    // MARK: - CloudFlare URL处理
    
    /// 重写基类方法，在检测到CloudFlare URL时启动Cookie验证
    override func handleCloudFlareURL(url: URL, isStandardKey: Bool, isHomePage: Bool) {
        super.handleCloudFlareURL(url: url, isStandardKey: isStandardKey, isHomePage: isHomePage)
        
        // 如果验证器已经在运行，不需要再次启动
        if cookieValidator?.isValidating ?? false {
            print("【调试】GuestVC: handleCloudFlareURL - Cookie验证器已在运行，跳过启动")
            return
        }
        
        print("【调试】GuestVC: 检测到CloudFlare URL，开始启动Cookie验证")
        startCookieValidation()
    }

    /// 启动 Cookie 验证
    private func startCookieValidation() {
        print("【调试】GuestVC: 准备启动 CloudflareCookieValidator 验证")
        
        // 确保在主线程上启动验证
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            print("【调试】GuestVC: 启动 CloudflareCookieValidator 验证 (主线程)")
            self.cookieValidator?.startValidation(
                checkTCookie: false, // 游客模式不需要检查 _t cookie
                onSuccess: { [weak self] cookies in
                    guard let self = self else { return }
                    print("【调试】GuestVC: CloudflareCookieValidator 验证成功，cf_clearance 变化达到要求次数")
                    self.reportResultAndDismiss(success: true, cookies: cookies, userAgent: self.webViewUserAgent)
                },
                onFailure: { [weak self] cookies in
                    guard let self = self else { return }
                    print("【调试】GuestVC: CloudflareCookieValidator 验证失败")
                    self.reportResultAndDismiss(success: false, cookies: cookies, userAgent: self.webViewUserAgent)
                }
            )
        }
    }
}

// MARK: - WKNavigationDelegate 特定实现
extension GuestViewController {
    // 覆盖基类方法，改为使用模板方法
    override func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // 调用基类方法，它会调用我们的 processSpecificNavigation
        super.webView(webView, didFinish: navigation)
    }
    
    override func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.webView(webView, didFail: navigation, withError: error)
    }

    override func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.webView(webView, didFailProvisionalNavigation: navigation, withError: error)
    }
}
