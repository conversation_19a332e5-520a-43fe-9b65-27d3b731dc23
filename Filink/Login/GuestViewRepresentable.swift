// GuestViewRepresentable.swift
import SwiftUI
import WebKit

struct GuestViewRepresentable: UIViewControllerRepresentable {
    @EnvironmentObject var settingsManager: SettingsManager
    @EnvironmentObject var cookieManager: CookieManager
    @EnvironmentObject var networkService: NetworkServiceImpl
    @EnvironmentObject var userService: UserServiceImpl

    var isPresented: Binding<Bool>
    var url: URL?  // 期望的初始加载URL，这里是 website 根目录

    // 游客登录只提供一个明确的初始化器，避免歧义
    init(isPresented: Binding<Bool>, url: URL?) {
        self.isPresented = isPresented
        self.url = url
    }

    func makeUIViewController(context: Context) -> UINavigationController {
        let guestVC = GuestViewController()
        // 将 guestVC 实例赋值给 Coordinator，以便 Coordinator 后续调用
        context.coordinator.guestViewController = guestVC

        guestVC.delegate = context.coordinator
        guestVC.settingsManager = settingsManager
        guestVC.cookieManager = cookieManager

        // 设置初始 URL 为 settingsManager.website
        // 确保这里传递的是 settingsManager.website 的 URL，而不是 LoginViewController 的 loginPath
        guestVC.initialURL = URL(string: settingsManager.website)
        print("【调试】GuestViewRepresentable: makeUIViewController - initialURL 设置为: \(guestVC.initialURL?.absoluteString ?? "nil")")

        let navController = UINavigationController(rootViewController: guestVC)
        return navController
    }

    func updateUIViewController(
        _ uiViewController: UINavigationController, context: Context
    ) {
        // 通常不需要更新 GuestViewController 的属性
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // MARK: - Coordinator
    class Coordinator: NSObject, FilinkGuestViewControllerDelegate {
        var parent: GuestViewRepresentable
        weak var guestViewController: GuestViewController?  // 持有对 GuestVC 的弱引用

        init(_ parent: GuestViewRepresentable) {
            self.parent = parent
        }

        func didFinishGuestAccess(
            success: Bool, cookies initialCookiesSnapshot: [HTTPCookie]?,
            userAgent: String?
        ) {
            print("【调试】Coordinator: 收到 didFinishGuestAccess - success=\(success), 初始Cookies数量=\(initialCookiesSnapshot?.count ?? 0), userAgent=\(userAgent?.prefix(30) ?? "nil")...")

            // 1. 更新 User-Agent (如果存在)
            if let receivedUserAgent = userAgent, !receivedUserAgent.isEmpty {
                Task { @MainActor in
                    self.parent.networkService.updateUserAgent(receivedUserAgent)
                    print("【调试】Coordinator: 已调用 networkService.updateUserAgent 更新 User-Agent。")
                }
            }

            // 2. 检查初步游客登录是否成功以及初始 Cookie 是否有效
            guard success, let cookies = initialCookiesSnapshot, !cookies.isEmpty else {
                print("【调试】Coordinator: GuestVC 报告失败或无有效 Cookie。关闭 WebView。")
                DispatchQueue.main.async {
                    self.parent.isPresented.wrappedValue = false
                }
                return
            }
            
            // 3. 保存游客 Cookie 到 CookieManager
            let guestIdentifier = "guest_session" // 使用固定标识符表示游客会话
            let domain = URL(string: self.parent.settingsManager.website)?.host ?? ""
            
            // 设置游客用户名
            let guestUsername = "游客" // 固定的游客用户名
            
            // 保存 Cookie 到 CookieManager
            print("【调试】Coordinator: 保存游客 Cookie 到 CookieManager，标识符: \(guestIdentifier), 用户名: \(guestUsername)")
            self.parent.cookieManager.updateOrAddUser(
                identifier: guestIdentifier,
                cookies: cookies,
                domain: domain,
                username: guestUsername
            )
            
            // 切换到游客用户
            self.parent.cookieManager.switchToUser(identifier: guestIdentifier)
            
            print("【调试】Coordinator: 游客登录成功。Cookie 已保存并设置为活动用户。关闭 WebView。")
            
            // 4. 关闭视图
            DispatchQueue.main.async {
                self.parent.isPresented.wrappedValue = false
            }
        }
        
        // 实现基础协议方法
        func didFinishCookieOperation(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
            // 这个方法通常不会直接调用，因为我们使用了特定的 didFinishGuestAccess 方法
            print("【调试】Coordinator: 收到 didFinishCookieOperation 基础方法调用")
        }
    }
}
