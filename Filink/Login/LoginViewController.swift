// LoginViewController.swift
import UIKit
@preconcurrency import WebKit

// 协议已移至FilinkLoginProtocols.swift
class LoginViewController: BaseLoginViewController {
    weak var delegate: FilinkLoginViewControllerDelegate?
    
    // 新增 Cookie 验证器
    private var cookieValidator: CloudflareCookieValidator?
    
    // 常量
    private enum Constants {
        static let loginSuccessCheckDelay: TimeInterval = 5.0
        static let tCookieName = "_t" // 登录需要检查的会话cookie
        static let defaultUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }

    // deinit 用于调试
    deinit {
        cookieValidator?.stopValidation()
        cookieValidator = nil
        print("【调试】LoginViewController DEINIT - 实例已销毁。isLoginProcessReported: \(isLoginProcessReported)")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 创建 Cookie 验证器
        cookieValidator = CloudflareCookieValidator(webView: webView)
    }
    
    // 重写 setupWebView 方法，使用默认（持久化）的数据存储
    override func setupWebView() {
        // 使用默认（持久化）的数据存储
        // 这样可以保存登录状态
        let configuration = WKWebViewConfiguration()
        configuration.websiteDataStore = .default() // 明确使用默认（持久化）的数据存储
        
        webView = WKWebView(frame: .zero, configuration: configuration)
        webView.translatesAutoresizingMaskIntoConstraints = false
        webView.navigationDelegate = self
        webView.customUserAgent = Constants.defaultUserAgent
        webView.isOpaque = false
        webView.backgroundColor = .clear
        webView.scrollView.backgroundColor = .clear
        
        view.addSubview(webView)
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }

    override func setupNavigationBar() {
        navigationItem.title = "登录" // 设置特定标题
        super.setupNavigationBar() // 调用基类方法完成其余设置
    }

    override func loadInitialPage() {
        guard let url = initialURL else {
            print("【调试】LoginVC: initialURL 为 nil，无法加载页面。")
            return
        }
        print("【调试】LoginVC: 加载初始页面: \(url.absoluteString)")
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    override func refreshTapped() {
        print("【调试】用户点击刷新按钮，重新加载 WebView。")
        isLoginProcessReported = false // 允许重新报告
        cookieValidator?.stopValidation() // 停止当前的验证
        startLoadingAnimation()
        webView.reload()
    }

    override func reportResultAndDismiss(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
        DispatchQueue.main.async {
            // 检查是否已经报告过
            guard !self.isLoginProcessReported else {
                print("【调试】LoginVC: reportResultAndDismiss - 已经报告过，检查是否需要隐藏 overlay。")
                if !(self.overlayLoadingView?.isHidden ?? true) {
                    self.hideBackgroundVerificationOverlay()
                }
                return
            }

            // 在调用 delegate 之前，确保覆盖层是隐藏的
            self.hideBackgroundVerificationOverlay() // 确保隐藏
            self.stopLoadingAnimation() // 停止页面初始加载菊花
            self.cookieValidator?.stopValidation() // 停止验证

            self.isLoginProcessReported = true
            print(
                "【调试】LoginVC: 调用 delegate.didFinishLogin(success: \(success), cookies: \(cookies?.count ?? 0)个)。"
            )
            self.delegate?.didFinishLogin(
                success: success, cookies: cookies, userAgent: self.webViewUserAgent)
        }
    }
    
    override func handleError(_ error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.handleError(error)
    }
    
    // MARK: - 模板方法实现
    override func processSpecificNavigation(url: URL?) {
        guard let url = url else { return }
        
        // 如果是登录页面，确保遮罩层隐藏
        if isLoginPage(url) {
            print("【调试】LoginVC: didFinish: 检测到登录页面，确保遮罩层隐藏")
            hideBackgroundVerificationOverlay()
            cookieValidator?.stopValidation() // 停止验证
            return
        }
        
        // 如果是主站点主页，启动 Cookie 验证
        if isMainSiteHomepage(url) {
            print("【调试】LoginVC: didFinish: 已导航到主站点主页。启动 Cookie 验证...")
            startCookieValidation()
            return
        }
        
        // 如果是主网站的其他页面，确保遮罩层隐藏
        if isMainWebsite(url) {
            print("【调试】LoginVC: didFinish: 检测到主网站非主页，确保遮罩层隐藏")
            hideBackgroundVerificationOverlay()
            return
        }
    }

    // 登录特有的辅助方法
    /// 判断给定的 URL 是否为登录页面
    func isLoginPage(_ url: URL?) -> Bool {
        guard let url = url,
              let mainSiteURLString = settingsManager?.website,
              let loginPath = settingsManager?.loginPath,
              let mainSiteURL = URL(string: mainSiteURLString)
        else {
            return false
        }
        
        // 构建完整的登录页面URL进行比较
        var fullLoginPath = loginPath
        if !fullLoginPath.hasPrefix("/") {
            fullLoginPath = "/" + fullLoginPath
        }
        
        // 检查是否是登录页面路径
        return url.host == mainSiteURL.host && url.path.contains(fullLoginPath)
    }

    /// 判断给定的 URL 是否为主网站（而不是登录页面或其他外部页面）
    func isMainWebsite(_ url: URL?) -> Bool {
        guard let url = url,
              let mainSiteURLString = settingsManager?.website,
              let mainSiteURL = URL(string: mainSiteURLString),
              let mainSiteHost = mainSiteURL.host
        else {
            return false
        }
        
        // 检查host是否匹配主网站
        return url.host == mainSiteHost && !isLoginPage(url)
    }
    
    /// 检查是否所有必需的Cookie都存在
    private func checkRequiredCookies(_ cookies: [HTTPCookie]) -> Bool {
        return cookieManager.requiredCookieNames.allSatisfy { requiredName in
            cookies.contains { $0.name == requiredName && !$0.value.isEmpty }
        }
    }
    
    /// 启动 Cookie 验证
    private func startCookieValidation() {
        print("【调试】LoginVC: 启动 CloudflareCookieValidator 验证")
        cookieValidator?.startValidation(
            checkTCookie: true, // 正常登录需要检查 _t cookie
            onSuccess: { [weak self] cookies in
                guard let self = self else { return }
                print("【调试】LoginVC: CloudflareCookieValidator 验证成功，cf_clearance 变化达到要求次数且 _t cookie 存在")
                // 验证通过，检查其他必需的 Cookie
                if self.checkRequiredCookies(cookies) {
                    print("【调试】LoginVC: 所有必需的 Cookie 都存在，登录成功")
                    self.reportResultAndDismiss(success: true, cookies: cookies, userAgent: self.webViewUserAgent)
                } else {
                    print("【调试】LoginVC: cf_clearance 验证通过，但缺少其他必需的 Cookie，继续等待")
                    // 继续等待，不报告结果
                }
            },
            onFailure: { [weak self] cookies in
                guard let self = self else { return }
                print("【调试】LoginVC: CloudflareCookieValidator 验证失败")
                self.reportResultAndDismiss(success: false, cookies: cookies, userAgent: self.webViewUserAgent)
            }
        )
    }
}

// MARK: - WKNavigationDelegate 特定实现
extension LoginViewController {
    override func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        guard let url = navigationAction.request.url else {
            decisionHandler(.allow)
            return
        }
        
        // 如果是登录页面，隐藏覆盖层
        if isLoginPage(url) && isMainFrameNavigation(navigationAction) {
            print("【调试】LoginVC: decidePolicyFor: 检测到登录页面导航，确保覆盖层隐藏")
            hideBackgroundVerificationOverlay()
            cookieValidator?.stopValidation() // 停止验证
            decisionHandler(.allow)
            return
        }
        
        // 其他情况调用基类实现
        super.webView(webView, decidePolicyFor: navigationAction, decisionHandler: decisionHandler)
    }
    
    override func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        // 如果是登录页面，确保遮罩层隐藏
        if let url = webView.url, isLoginPage(url) {
            print("【调试】LoginVC: didStartProvisionalNavigation: 检测到登录页面，确保遮罩层隐藏")
            hideBackgroundVerificationOverlay()
            cookieValidator?.stopValidation() // 停止验证
            return
        }
        
        // 其他情况调用基类实现
        super.webView(webView, didStartProvisionalNavigation: navigation)
    }
    
    // 覆盖基类方法，改为使用模板方法
    override func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // 调用基类方法，它会调用我们的 processSpecificNavigation
        super.webView(webView, didFinish: navigation)
    }
    
    override func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.webView(webView, didFail: navigation, withError: error)
    }

    override func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        cookieValidator?.stopValidation() // 停止验证
        super.webView(webView, didFailProvisionalNavigation: navigation, withError: error)
    }
}
