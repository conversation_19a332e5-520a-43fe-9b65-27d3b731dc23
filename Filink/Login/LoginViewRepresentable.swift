// LoginViewRepresentable.swift
import SwiftUI
import WebKit
import CoreData

struct LoginViewRepresentable: UIViewControllerRepresentable {
    @EnvironmentObject var settingsManager: SettingsManager
    @EnvironmentObject var cookieManager: CookieManager
    @EnvironmentObject var networkService: NetworkServiceImpl
    @EnvironmentObject var userService: UserServiceImpl

    var isPresented: Binding<Bool>
    var url: URL?  // 期望的初始加载URL，通常是登录页

    // init 方法保持不变
    init(isPresented: Binding<Bool>, url: URL?) {
        self.isPresented = isPresented
        self.url = url
    }
    init(isPresented: Binding<Bool>) {  // 便利初始化器
        self.isPresented = isPresented
        // 如果便利初始化器被调用，确保 url 有一个合理的默认值，或从 settingsManager 构建
        // 直接获取非可选的 website 和 loginPath
        let loginBase = SettingsManager.shared.website
        let loginPath = SettingsManager.shared.loginPath
        if !loginBase.isEmpty, let baseURL = URL(string: loginBase) {
            self.url = baseURL.appendingPathComponent(
                loginPath.starts(with: "/")
                    ? String(loginPath.dropFirst()) : loginPath)
            // print("【调试】LoginViewRepresentable: 便利初始化器，构建的登录URL: \(self.url?.absoluteString ?? "nil")")
        } else {
            self.url = nil  // 或者一个错误提示URL
            print("【调试】LoginViewRepresentable: 便利初始化器，无法构建登录URL。")
        }
    }

    func makeUIViewController(context: Context) -> UINavigationController {
        let loginVC = LoginViewController()
        // 将 loginVC 实例赋值给 Coordinator，以便 Coordinator 后续调用
        context.coordinator.loginViewController = loginVC  // ***** 关键赋值 *****

        loginVC.delegate = context.coordinator
        loginVC.settingsManager = settingsManager
        loginVC.cookieManager = cookieManager

        // 设置初始 URL (如果 self.url 为 nil, loginVC 内部会处理)
        loginVC.initialURL = self.url
        // print("【调试】LoginViewRepresentable: makeUIViewController - initialURL 设置为: \(self.url?.absoluteString ?? "nil")")

        // successCookieCheck 闭包不再需要，LoginVC的逻辑已简化
        // loginVC.successCookieCheck = { ... }

        let navController = UINavigationController(rootViewController: loginVC)
        return navController
    }

    func updateUIViewController(
        _ uiViewController: UINavigationController, context: Context
    ) {
        // 通常不需要更新 LoginViewController 的属性，除非 url 改变且需要强制重新加载
        // 如果 url 是 @State 并且可能在外部改变，这里可能需要处理
        // 例如:
        // if let loginVC = uiViewController.viewControllers.first as? LoginViewController,
        //    loginVC.initialURL != self.url {
        //     loginVC.initialURL = self.url
        //     loginVC.loadInitialPage() // 需要一个公共方法来触发重新加载
        // }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)  // Coordinator 的 init 已修改或将在创建后设置 loginVC
    }

    // MARK: - Coordinator
    class Coordinator: NSObject, FilinkLoginViewControllerDelegate {
        var parent: LoginViewRepresentable
        weak var loginViewController: LoginViewController?  // 持有对 LoginVC 的弱引用

        init(_ parent: LoginViewRepresentable) {
            self.parent = parent
        }

        // LoginViewRepresentable.swift - Coordinator 类内部

        func didFinishLogin(
            success: Bool, cookies initialCookiesSnapshot: [HTTPCookie]?,
            userAgent: String?
        ) {
            // print("【调试】Coordinator: 收到 didFinishLogin - success=\(success), 初始Cookies数量=\(initialCookiesSnapshot?.count ?? 0), userAgent=\(userAgent?.prefix(30) ?? "nil")...")

            // 1. 更新 User-Agent (如果存在)
            if let receivedUserAgent = userAgent, !receivedUserAgent.isEmpty {
                Task { @MainActor in  // 确保在主 Actor 上下文执行 UI 相关或 ObservableObject 更新
                    self.parent.networkService.updateUserAgent(receivedUserAgent)
                    // print("【调试】Coordinator: 已调用 networkService.updateUserAgent 更新 User-Agent。")
                }
            }

            // 2. 检查初步登录是否成功以及初始 Cookie 是否有效
            guard success, let receivedInitialCookies = initialCookiesSnapshot,
                !receivedInitialCookies.isEmpty
            else {
                print("【调试】Coordinator: LoginVC 报告失败或未收到初始 Cookies。关闭 WebView。")
                DispatchQueue.main.async {  // 确保在主线程关闭
                    self.parent.isPresented.wrappedValue = false
                }
                return
            }

            // print("【调试】Coordinator: 收到 didFinishLogin - success=\(success)...")

            // 1. 立即尝试显示覆盖层 (如果初步成功)
            //    我们将showOverlay移到Task内部的开头，确保在任何await之前。
            //    或者，如果User-Agent更新很快，可以保留在这里，但要意识到它的异步性。

            // 2. 处理 User-Agent 更新 (可以并行或在显示覆盖层之后)
            if let receivedUserAgent = userAgent, !receivedUserAgent.isEmpty {
                Task { @MainActor in
                    self.parent.networkService.updateUserAgent(receivedUserAgent)
                    // print("【调试】Coordinator: 已调用 networkService.updateUserAgent。")
                }
            }

            // 3. 检查初步登录是否成功
            guard success, let receivedInitialCookies = initialCookiesSnapshot, !receivedInitialCookies.isEmpty else {
                print("【调试】Coordinator: LoginVC 报告失败或未收到初始 Cookies。关闭 WebView。")
                DispatchQueue.main.async {
                    self.parent.isPresented.wrappedValue = false
                }
                return
            }

            // 4. 启动后台任务获取 Profile，并在 Task 内部最开始显示覆盖层
            print("【调试】Coordinator: (BEFORE Task start for fetchProfile) loginViewController is \(self.loginViewController == nil ? "NIL" : "VALID")")

            Task { @MainActor [weak self, weak capturedLoginVC = self.loginViewController] in
                guard let self = self else { return }
                guard let actualLoginVC = capturedLoginVC else {
                    print("【调试】Coordinator: ❌ Task 开始时 loginVC 已丢失。无法显示覆盖层或获取最终Cookie。使用初始Cookie。")
                    // 这里可能需要根据业务逻辑决定是否直接关闭，或者尝试用初始Cookie（如果之前没有关闭）
                    // 如果是这种罕见情况，可能直接关闭更安全
                    if !receivedInitialCookies.isEmpty { // 假设 receivedInitialCookies 仍然从外部作用域捕获
                         // 尝试用初始Cookie保存，但这可能不是预期
                        // self.saveCookiesAndDismiss(cookiesToSave: receivedInitialCookies, ...)
                    }
                    self.parent.isPresented.wrappedValue = false
                    return
                }

                // ***** 在 Task 内部，任何 await 之前，立即显示覆盖层 *****
                print("【调试】Coordinator: Task STARTED. 即将调用 showBackgroundVerificationOverlay。")
                actualLoginVC.showBackgroundVerificationOverlay( // Coordinator 调用时，通常是"正在验证账户"
                    mainMessage: "正在验证账户，请稍等...",
                    subMessage: "（取决于您的网络和设备性能）"
                )

                // print("【调试】Coordinator: 开始尝试获取用户 Profile (使用初始 Cookie 快照)...")

                // 准备用于 API 请求的 Cookie 字典
                let cookiesForProfileFetch = receivedInitialCookies.reduce(
                    into: [:]) { $0[$1.name] = $1.value }
                var fetchedProfile: (username: String, id: Int)? = nil
                var retryCount = 0
                let maxRetries = 3

                // print("【调试】Coordinator: 开始尝试获取用户 Profile (使用初始 Cookie 快照)...")
                while retryCount < maxRetries {
                    // 在 await 之前再次检查 self.loginViewController (当前 Coordinator 持有的) 和 capturedLoginVC (Task 启动时捕获的)
                    print(
                        "【调试】Coordinator: (BEFORE await fetchProfile - Retry \(retryCount + 1)) self.loginVC is \(self.loginViewController == nil ? "NIL" : "VALID"), capturedLoginVC is \(capturedLoginVC == nil ? "NIL" : "VALID")"
                    )

                    // 构建Cookie字符串，确保包含所有必要的Cookie
                    let cookieString = cookiesForProfileFetch.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
                    print("【调试】Coordinator: 使用Cookie请求用户Profile")
                    print("【调试】Coordinator: Cookie数量: \(cookiesForProfileFetch.count)")
                    print("【调试】Coordinator: Cookie内容: \(cookieString.prefix(200))...")

                    // 检查是否包含必要的Cookie
                    let hasRequiredCookies = cookiesForProfileFetch.keys.contains("_t") && cookiesForProfileFetch.keys.contains("cf_clearance")
                    print("【调试】Coordinator: 包含必要Cookie(_t, cf_clearance): \(hasRequiredCookies)")

                    // 构建完整的请求头，包括必要的认证信息
                    var requestHeaders: [String: String] = [
                        "Cookie": cookieString,
                        "X-Requested-With": "XMLHttpRequest",
                        "Accept": "application/json, text/javascript, */*; q=0.01"
                    ]

                    // 添加Referer头
                    if let websiteURL = URL(string: self.parent.settingsManager.website) {
                        requestHeaders["Referer"] = websiteURL.absoluteString
                    }

                    // 使用 NetworkService 获取当前用户资料，手动设置完整的请求头
                    let userServiceResult = await self.parent.networkService.requestData(
                        endpoint: "session/current.json",
                        method: .get,
                        parameters: nil,
                        headers: requestHeaders
                    )
                    
                    switch userServiceResult {
                    case .success(let data):
                        do {
                            // 解析 JSON 数据
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                               let currentUser = json["current_user"] as? [String: Any],
                               let username = currentUser["username"] as? String,
                               let userId = currentUser["id"] as? Int {
                                fetchedProfile = (username: username, id: userId)

                                // 保存完整用户信息到 Core Data
                                await MainActor.run {
                                    self.saveUserToCoreData(from: currentUser, userId: Int64(userId))
                                }

                                print("【调试】Coordinator: 成功获取用户Profile")
                            }
                        } catch {
                            print("【调试】Coordinator: 解析用户数据失败: \(error)")
                        }
                    case .failure(let error):
                        print("【调试】Coordinator: 获取用户数据失败: \(error)")
                    }

                    // 在 await 之后再次检查
                    print(
                        "【调试】Coordinator: (AFTER await fetchProfile - Retry \(retryCount + 1)) self.loginVC is \(self.loginViewController == nil ? "NIL" : "VALID"), capturedLoginVC is \(capturedLoginVC == nil ? "NIL" : "VALID")"
                    )
                    print(
                        "【调试】Coordinator: 获取 Profile 尝试 \(retryCount + 1) - 用户名: \(fetchedProfile?.username ?? "nil"), ID: \(fetchedProfile?.id ?? -1)"
                    )

                    if fetchedProfile?.username != nil {
                        break  // 成功获取到用户名，跳出重试循环
                    }
                    retryCount += 1
                    if retryCount < maxRetries {
                        try? await Task.sleep(nanoseconds: 1_000_000_000)  // 延迟1秒再重试
                    }
                }

                // 4. 检查 Profile 获取结果
                guard let finalUsername = fetchedProfile?.username,
                    let userId = fetchedProfile?.id
                else {
                    print(
                        "【调试】Coordinator: ⚠️ 经过 \(maxRetries) 次重试，仍未获取到用户 Profile。登录失败。关闭 WebView。"
                    )
                    self.parent.isPresented.wrappedValue = false  // 确保在主 actor 上下文
                    return
                }
                // print("【调试】Coordinator: ✅ 成功获取到用户 Profile: \(finalUsername) (ID: \(userId))。")

                // 5. 关键：Profile 获取成功后，尝试通过捕获的 loginViewController 引用再次从 WebView 获取最新的所有 Cookie
                guard let actualLoginVC = capturedLoginVC else {
                    // 如果 Task 启动时捕获的 loginViewController 引用已丢失
                    // （这可能意味着在 fetchProfile 开始前或期间，loginVC 就被释放了）
                    print(
                        "【调试】Coordinator: ❌ (在Task内部检查) loginViewController 引用 (capturedLoginVC) 已丢失，无法获取最终 Cookie。将使用初始 Cookie 快照进行保存。"
                    )
                    self.saveCookiesAndDismiss(
                        cookiesToSave: receivedInitialCookies,
                        userId: userId.description, username: finalUsername)
                    return
                }

                // 如果捕获的 loginViewController 引用仍然有效
                // print("【调试】Coordinator: 正在从 WebView (通过 capturedLoginVC) 请求最新的所有 Cookie...")
                actualLoginVC.retrieveAllCurrentWebViewCookies {
                    [weak self] finalCookiesSnapshot in
                    // 这个回调是从 retrieveAllCurrentWebViewCookies 来的，它内部已经确保了在主线程
                    guard let self = self else {
                        print(
                            "【调试】Coordinator: retrieveAllCurrentWebViewCookies 回调中 self 已释放。"
                        )
                        return
                    }
                    // print("【调试】Coordinator: ✅ 成功从 WebView 获取到最终 Cookie 快照 (数量: \(finalCookiesSnapshot.count))。")
                    self.saveCookiesAndDismiss(
                        cookiesToSave: finalCookiesSnapshot,
                        userId: userId.description, username: finalUsername)
                }
            }
        }
        
        // 实现基础协议方法
        func didFinishCookieOperation(success: Bool, cookies: [HTTPCookie]?, userAgent: String?) {
            // 这个方法通常不会直接调用，因为我们使用了特定的 didFinishLogin 方法
            print("【调试】Coordinator: 收到 didFinishCookieOperation 基础方法调用")
        }

        // 辅助方法，用于保存Cookie和关闭视图，避免重复代码
        @MainActor
        private func saveCookiesAndDismiss(
            cookiesToSave: [HTTPCookie], userId: String, username: String
        ) {
            guard
                let domain = URL(string: self.parent.settingsManager.website)?
                    .host
            else {
                print(
                    "【调试】Coordinator: ⚠️ 无法从 settingsManager.website 获取域名，无法保存 Cookies。"
                )
                self.parent.isPresented.wrappedValue = false
                return
            }

            // print("【调试】Coordinator: 准备使用最终获取的 Cookies 更新 CookieManager。用户ID: \(userId), 用户名: \(username), Domain: \(domain)")
            self.parent.cookieManager.updateOrAddUser(
                identifier: userId,  // 使用获取到的用户ID
                cookies: cookiesToSave,
                domain: domain,
                username: username
            )

            // print("【调试】Coordinator: Cookie 保存完毕，用户资料已保存到 Core Data，所有流程完成。关闭 WebView。")
            self.parent.isPresented.wrappedValue = false
        }

        // 保存用户信息到 Core Data
        @MainActor
        private func saveUserToCoreData(from userData: [String: Any], userId: Int64) {
            let viewContext = CoreDataManager.shared.container.viewContext

            do {
                // 查找或创建用户
                let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %lld", userId)
                fetchRequest.fetchLimit = 1

                let users = try viewContext.fetch(fetchRequest)
                let user: User

                if let existingUser = users.first {
                    user = existingUser
                    print("【调试】Coordinator: 更新现有用户 ID=\(userId)")
                } else {
                    user = User(context: viewContext)
                    user.id = userId
                    print("【调试】Coordinator: 创建新用户 ID=\(userId)")
                }

                // 更新用户属性
                user.username = userData["username"] as? String
                user.name = userData["name"] as? String ?? userData["username"] as? String
                user.displayUsername = userData["display_username"] as? String ?? userData["name"] as? String ?? userData["username"] as? String
                user.usertitle = userData["title"] as? String ?? ""
                user.usersignature = userData["bio_raw"] as? String ?? userData["title"] as? String ?? ""

                // 处理头像URL - 保存完整的头像模板路径
                if let avatarTemplate = userData["avatar_template"] as? String {
                    user.avatar = avatarTemplate  // 保存完整模板路径
                    print("【调试】Coordinator: 设置用户头像模板: \(avatarTemplate)")
                }

                // 保存到 Core Data
                try viewContext.save()
                print("【调试】Coordinator: ✅ 用户信息已保存到 Core Data - 用户名: \(user.username ?? "无"), 头像: \(user.avatar ?? "无")")

            } catch {
                print("【调试】Coordinator: ❌ 保存用户信息到 Core Data 失败: \(error)")
            }
        }
    }
}
