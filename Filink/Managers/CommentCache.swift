// Managers/CommentCache.swift
import Foundation

class CommentCache {
    static let shared = CommentCache()

    // 存储所有评论ID的有序列表，按 postId 区分
    private var allCommentIdsByPost: [String: [Int64]] = [:]

    private init() {
        // 私有初始化器，确保单例
    }

    /// 缓存某个帖子的所有评论ID。
    /// - Parameters:
    ///   - postId: 帖子ID。
    ///   - commentIds: 该帖子的所有评论ID的有序列表。
    func cacheAllCommentIds(postId: String, commentIds: [Int64]) {
        // 检查是否已缓存相同的评论ID列表
        if let existingIds = allCommentIdsByPost[postId], existingIds == commentIds {
            // 如果已存在相同的评论ID列表，不需要再次缓存
            return
        }
        
        allCommentIdsByPost[postId] = commentIds
        print("【调试】✅ CommentCache: 缓存了帖子 \(postId) 的所有评论ID。总数: \(commentIds.count)")
    }

    /// 获取某个帖子的所有评论ID。
    /// - Parameter postId: 帖子ID。
    /// - Returns: 该帖子的所有评论ID的有序列表，如果不存在则返回空数组。
    func getAllCommentIds(postId: String) -> [Int64] {
        let ids = allCommentIdsByPost[postId] ?? []
        print("【调试】ℹ️ CommentCache: 获取帖子 \(postId) 的所有评论ID。总数: \(ids.count)")
        return ids
    }

    /// 获取某个帖子的下一页评论ID。
    /// - Parameters:
    ///   - postId: 帖子ID。
    ///   - startIndex: 开始索引。
    ///   - pageSize: 每页大小。
    /// - Returns: 下一页评论的ID列表。
    func getNextPageCommentIds(postId: String, startIndex: Int, pageSize: Int) -> [Int64] {
        guard let allIds = allCommentIdsByPost[postId] else {
            print("【调试】⚠️ CommentCache: 帖子 \(postId) 没有缓存的评论ID。")
            return []
        }
        
        // 确保allIds不为空
        if allIds.isEmpty {
            print("【调试】⚠️ CommentCache: 帖子 \(postId) 的评论ID列表为空。")
            return []
        }

        let endIndex = min(startIndex + pageSize, allIds.count)
        guard startIndex < endIndex else {
            print("【调试】ℹ️ CommentCache: 帖子 \(postId) 没有更多评论ID可加载。startIndex=\(startIndex), allIds.count=\(allIds.count)")
            return []
        }

        let nextPageIds = Array(allIds[startIndex..<endIndex])
        
        // 过滤掉0值，这些可能是无效的评论ID
        let filteredIds = nextPageIds.filter { $0 > 0 }
        if filteredIds.count < nextPageIds.count {
            print("【调试】⚠️ CommentCache: 过滤掉了 \(nextPageIds.count - filteredIds.count) 个无效评论ID（值为0）")
        }
        
        print("【调试】ℹ️ CommentCache: 获取帖子 \(postId) 的下一页评论ID。从索引 \(startIndex) 到 \(endIndex-1)。数量: \(filteredIds.count)")
        return filteredIds
    }

    // MARK: - 废弃/移除的方法 (根据文档要求，这些方法将被移除或标记为废弃)
    // 以下是文档中提到需要移除或标记为废弃的示例，实际项目中应根据具体情况处理。
    // 为了符合文档要求，我将它们注释掉，表示它们不再被使用或推荐使用。

    /*
    // 移除或标记为废弃 commentCache: [String: [Int64: String]]
    // private var commentCache: [String: [Int64: String]] = [:]

    // 移除或标记为废弃 commentIdsByPost: [String: [Int64]]
    // private var commentIdsByPost: [String: [Int64]] = [:]

    /// 移除或标记为废弃 cacheComment(postId:comment:CommentDTO)
    // func cacheComment(postId: String, comment: CommentDTO) {
    //     // 逻辑应仅限于更新 allCommentIdsByPost (如果需要)
    //     print("【调试】⚠️ CommentCache: cacheComment 方法已废弃，不应再缓存完整 CommentDTO。")
    // }

    /// 移除或标记为废弃 cacheComments(postId:comments:[CommentDTO])
    // func cacheComments(postId: String, comments: [CommentDTO]) {
    //     // 逻辑应仅限于更新 allCommentIdsByPost (如果需要)
    //     print("【调试】⚠️ CommentCache: cacheComments 方法已废弃，不应再缓存完整 CommentDTO。")
    // }

    /// 移除或标记为废弃 getCachedComments()
    // func getCachedComments() -> [CommentDTO] {
    //     print("【调试】⚠️ CommentCache: getCachedComments 方法已废弃，应直接查询 Core Data。")
    //     return []
    // }

    /// 移除或标记为废弃 getComment()
    // func getComment(postId: String, commentId: Int64) -> CommentDTO? {
    //     print("【调试】⚠️ CommentCache: getComment 方法已废弃，应直接查询 Core Data。")
    //     return nil
    // }

    /// 移除或标记为废弃 getBatchComments()
    // func getBatchComments(postId: String, commentIds: [Int64]) -> [CommentDTO] {
    //     print("【调试】⚠️ CommentCache: getBatchComments 方法已废弃，应直接查询 Core Data。")
    //     return []
    // }
    */
}
