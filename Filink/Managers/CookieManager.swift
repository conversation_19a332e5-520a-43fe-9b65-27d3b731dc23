// CookieManager.swift
import Foundation
import Combine
import WebKit // Needed for HTTPCookie type

// --- 1. 定义存储每个用户数据的结构 ---
// 重命名 AccountCookieData -> UserCookieData
struct UserCookieData: Codable {
    var cookies: [String: String] // Cookie 键值对
    var domain: String            // Cookie 所属的域
    var username: String?         // 可选：存储从 API 获取的用户名
    // 可以根据需要添加其他与用户相关的信息
    var createdAt: Date = Date()  // 添加创建时间字段，默认为当前时间
}

final class CookieManager: ObservableObject {

    // MARK: - UserDefaults Keys
    private enum DefaultsKeys {
        // 重命名 Key
        static let savedUsersData = "cookieManager_savedUsersData_v2" // 原 savedAccountsData
        static let lastActiveUserIdentifier = "cookieManager_lastActiveUserIdentifier_v2" // 原 lastActiveAccountIdentifier
    }

    // MARK: - Constants
    // 将 sessionCookieName 改为 requiredCookieNames 数组
    let requiredCookieNames = ["_t", "cf_clearance"] // 包含所有核心 Cookie 名称

    // MARK: - Published State (用于 SwiftUI 视图更新)

    // 重命名 activeAccountIdentifier -> activeUserIdentifier
    @Published private(set) var activeUserIdentifier: String? = nil {
        didSet {
            updateActiveState()
            // 使用新的 Key
            UserDefaults.standard.set(activeUserIdentifier, forKey: DefaultsKeys.lastActiveUserIdentifier)
        }
    }

    // 重命名 storedAccounts -> storedUsers, AccountCookieData -> UserCookieData
    @Published private(set) var storedUsers: [String: UserCookieData] = [:] {
        didSet {
            DispatchQueue.main.async {
                // 重命名 availableAccountIdentifiers -> availableUserIdentifiers
                self.availableUserIdentifiers = self.storedUsers.keys.sorted()
            }
            saveUsersToDefaults() // 重命名内部调用
        }
    }

    // 重命名 availableAccountIdentifiers -> availableUserIdentifiers
    @Published private(set) var availableUserIdentifiers: [String] = []

    // 当前活动用户的 Cookie 字典
    @Published private(set) var activeCookies: [String: String] = [:]
    // 当前活动用户的域名
    @Published private(set) var activeDomain: String? = nil
    // 当前是否有活动用户且包含会话 Cookie (表示已登录)
    @Published private(set) var isCurrentlyLoggedIn: Bool = false
    // 当前活动用户的用户名 (从 UserCookieData 获取)
    @Published private(set) var activeUsername: String? = nil


    // MARK: - Initialization
    init() {
        print("【调试】🍪 CookieManager initializing...")
        // 1. 加载用户数据
        loadUsersFromDefaults() // 重命名内部调用
        // 2. 恢复上次活动用户
        // 使用新的 Key 和属性名
        if let lastActiveId = UserDefaults.standard.string(forKey: DefaultsKeys.lastActiveUserIdentifier),
           storedUsers[lastActiveId] != nil {
             self.activeUserIdentifier = lastActiveId
        } else {
            // 移除自动选择第一个用户作为默认用户的行为
            self.activeUserIdentifier = nil
            print("【调试】🍪 CookieManager: No last active user found or valid. Starting with no active user.")
        }
        // 3. 更新活动状态
        updateActiveState()
    }

    // MARK: - Public Methods for User Management

    /**
     * 更新或添加一个用户的 Cookie 数据。
     * 重命名 updateOrAddAccount -> updateOrAddUser
     * 参数 identifier 含义改为用户标识符
     * 参数 username 含义不变
     */
    func updateOrAddUser(identifier userIdentifier: String, cookies httpCookies: [HTTPCookie], domain: String, username: String? = nil) {
        // 如果没有cookie，不创建用户
        if httpCookies.isEmpty {
            print("【调试】🍪 CookieManager: 没有提供cookie，不创建或更新用户")
            return
        }
        
        // 验证用户名有效性 - 如果提供了username但为空字符串，不创建或更新用户
        if let providedUsername = username, providedUsername.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("【调试】🍪 CookieManager: 提供的用户名为空，不创建或更新用户")
            return
        }
        
        var newCookieDict: [String: String] = [:]
        print("【调试】🍪 CookieManager: Processing \(httpCookies.count) cookies for user '\(userIdentifier)' on domain '\(domain)'...")

        // 处理 cookie 信息，过滤并存储
        for cookie in httpCookies {
            let cookieDomain = cookie.domain
            let isRelevantDomain = cookieDomain == domain ||
                                  (cookieDomain.hasPrefix(".") && domain.hasSuffix(cookieDomain)) ||
                                  domain.hasSuffix("." + cookieDomain)
            let isExpired = cookie.expiresDate != nil && cookie.expiresDate! < Date()

            // 检查是否是必需的 Cookie
            let requiredCookiesForThisUser = getRequiredCookieNames(for: userIdentifier)
            if requiredCookiesForThisUser.contains(cookie.name) {
                newCookieDict[cookie.name] = cookie.value
                print("【调试】🍪 强制保存必需的 cookie: \(cookie.name) = \(cookie.value.prefix(5))...")
            } else if isRelevantDomain && !isExpired {
                newCookieDict[cookie.name] = cookie.value
                print("【调试】🍪 保存有效 cookie: \(cookie.name) = \(cookie.value.prefix(5))...")
            }
        }

        // 获取该用户类型所需的必需 cookies
        let requiredCookiesForThisUser = getRequiredCookieNames(for: userIdentifier)
        
        // 检查是否包含该用户类型所需的必需 cookies
        let containsRequiredCookies = requiredCookiesForThisUser.allSatisfy { requiredName in
            newCookieDict[requiredName] != nil && !(newCookieDict[requiredName]?.isEmpty ?? true)
        }

        if newCookieDict.isEmpty || !containsRequiredCookies {
            print("【调试】🍪 CookieManager: 过滤后没有必需的cookie，不创建或更新用户")
            // 打印缺少的必需 cookie
            let missingCookies = requiredCookiesForThisUser.filter { requiredName in
                newCookieDict[requiredName] == nil || (newCookieDict[requiredName]?.isEmpty ?? true)
            }
            if !missingCookies.isEmpty {
                print("【调试】🍪 CookieManager: 缺少必需的 cookie: \(missingCookies.joined(separator: ", "))")
            }
            return
        }

        // 获取现有用户数据或创建新的
        var userData = self.storedUsers[userIdentifier] ?? UserCookieData(cookies: [:], domain: domain, username: username)
        
        // 更新 cookie 数据
        userData.cookies = newCookieDict
        userData.domain = domain
        
        // 只有在提供新用户名时才更新
        if username != nil { 
            userData.username = username 
            print("【调试】🍪 CookieManager: 用户 '\(userIdentifier)' 更新用户名为 '\(username ?? "nil")'")
        }

        // 保存用户数据
        // 检查是否有username，如果是新用户且没有username，则不保存
        if self.storedUsers[userIdentifier] == nil && userData.username == nil {
            print("【调试】🍪 CookieManager: 新用户 '\(userIdentifier)' 没有提供用户名，不保存")
            return
        }
        
        self.storedUsers[userIdentifier] = userData
        self.activeUserIdentifier = userIdentifier // 这会触发 didSet，进而调用 updateActiveState()
        
        print("【调试】🍪 CookieManager: 用户 '\(userIdentifier)' 已更新。Cookie数量: \(userData.cookies.count), 用户名: \(userData.username ?? "未设置")")
    }

    /**
     * 切换到指定的用户。
     * 重命名 switchToAccount -> switchToUser
     * 参数 identifier 含义改为用户标识符
     */
    func switchToUser(identifier userIdentifier: String) {
        // 使用新的属性名
        guard storedUsers[userIdentifier] != nil else {
            print("【调试】❌ CookieManager: Cannot switch to non-existent user '\(userIdentifier)'.")
            return
        }
        DispatchQueue.main.async {
            // 使用新的属性名
            if self.activeUserIdentifier != userIdentifier {
                self.activeUserIdentifier = userIdentifier
                print("【调试】🍪 CookieManager: Switched active user to '\(userIdentifier)'.")
            } else {
                print("【调试】ℹ️ CookieManager: User '\(userIdentifier)' is already active.")
            }
        }
    }

    /**
     * 登出当前活动的用户。
     */
    func logout() {
        DispatchQueue.main.async {
            // 使用新的属性名
            if let currentUserId = self.activeUserIdentifier {
                print("【调试】🍪 CookieManager: Logging out user '\(currentUserId)'. Clearing active state.")
                self.activeUserIdentifier = nil
            } else {
                print("【调试】ℹ️ CookieManager: No active user to log out.")
            }
        }
    }

    /**
     * 删除指定用户的所有数据。
     * 重命名 deleteAccount -> deleteUser
     * 参数 identifier 含义改为用户标识符
     */
    func deleteUser(identifier userIdentifier: String) {
        DispatchQueue.main.async {
            print("【调试】🍪 CookieManager: Attempting to delete user '\(userIdentifier)'...")
            // 使用新的属性名
            if self.storedUsers.removeValue(forKey: userIdentifier) != nil {
                print("【调试】✅ CookieManager: User '\(userIdentifier)' deleted successfully.")
                // 使用新的属性名
                if self.activeUserIdentifier == userIdentifier {
                    print("【调试】ℹ️ CookieManager: Deleted user was the active one. Setting active user to nil (logout).")
                    self.activeUserIdentifier = nil
                }
            } else {
                print("【调试】⚠️ CookieManager: User '\(userIdentifier)' not found for deletion.")
            }
        }
    }

    /**
     * 清除所有已保存的用户数据和活动状态。
     */
    func clearAllData() {
        DispatchQueue.main.async {
            print("【调试】⚠️🍪 CookieManager: Clearing ALL user data and active state!")
            // 使用新的属性名和 Key
            self.storedUsers = [:]
            self.activeUserIdentifier = nil
            UserDefaults.standard.removeObject(forKey: DefaultsKeys.savedUsersData)
            UserDefaults.standard.removeObject(forKey: DefaultsKeys.lastActiveUserIdentifier)
            print("【调试】✅ CookieManager: All data cleared.")
        }
    }

    /**
     * 验证当前活跃用户的登录状态
     * 这个方法会检查当前用户的Cookie是否仍然有效，但不会删除用户数据
     */
    func validateCurrentUser() -> Bool {
        guard let currentUserId = activeUserIdentifier,
              let userData = storedUsers[currentUserId] else {
            print("【调试】🍪 CookieManager: 验证失败 - 没有活跃用户")
            return false
        }

        // 获取该用户类型所需的必需 cookies
        let requiredCookies = getRequiredCookieNames(for: currentUserId)
        let hasAllRequiredCookies = requiredCookies.allSatisfy { requiredName in
            userData.cookies[requiredName] != nil && !(userData.cookies[requiredName]?.isEmpty ?? true)
        }

        // 验证用户名有效性
        let hasValidUsername = userData.username != nil &&
                              !(userData.username?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true)

        let isValid = hasAllRequiredCookies && hasValidUsername

        if isValid {
            print("【调试】✅ CookieManager: 用户 '\(currentUserId)' 验证成功")
        } else {
            print("【调试】❌ CookieManager: 用户 '\(currentUserId)' 验证失败")
            if !hasValidUsername {
                print("【调试】   - 用户名无效")
            }
            if !hasAllRequiredCookies {
                let missingCookies = requiredCookies.filter { requiredName in
                    userData.cookies[requiredName] == nil || (userData.cookies[requiredName]?.isEmpty ?? true)
                }
                print("【调试】   - 缺少必需的 cookie: \(missingCookies.joined(separator: ", "))")
            }
        }

        return isValid
    }


    /**
     * 更新指定用户的用户名。
     * 重命名 updateUsernameForAccount -> updateUsernameForUser
     * 参数 identifier 含义改为用户标识符
     */
    func updateUsernameForUser(identifier userIdentifier: String, username: String?) {
        DispatchQueue.main.async {
            // 使用新的属性名
            guard var userData = self.storedUsers[userIdentifier] else {
                print("【调试】⚠️ CookieManager: Cannot update username for non-existent user '\(userIdentifier)'.")
                return
            }

            if userData.username != username {
                userData.username = username
                // 使用新的属性名
                self.storedUsers[userIdentifier] = userData
                print("【调试】🍪 CookieManager: Updated username for user '\(userIdentifier)' to '\(username ?? "nil")'.")

                // 使用新的属性名
                if self.activeUserIdentifier == userIdentifier {
                    self.activeUsername = username
                    print("【调试】ℹ️ CookieManager: Active username updated immediately.")
                }
            } else {
                 print("【调试】ℹ️ CookieManager: Username for user '\(userIdentifier)' is already '\(username ?? "nil")'. No update needed.")
            }
        }
    }

    // MARK: - Internal State Update Logic

    /**
     * 根据当前的 `activeUserIdentifier` 更新活动状态。
     */
    private func updateActiveState() {
        // 移除 DispatchQueue.main.async 块，让赋值同步执行
        if let currentUserId = self.activeUserIdentifier, let userData = self.storedUsers[currentUserId] {
            self.activeCookies = userData.cookies
            self.activeDomain = userData.domain
            self.activeUsername = userData.username
            
            // 获取该用户类型所需的必需 cookies
            let requiredCookies = getRequiredCookieNames(for: currentUserId)
            let hasAllRequiredCookies = requiredCookies.allSatisfy { requiredName in
                userData.cookies[requiredName] != nil && !(userData.cookies[requiredName]?.isEmpty ?? true)
            }
            
            // 验证用户数据 - 只有在有有效用户名时才设置为已登录
            if let username = userData.username, !username.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                // 确保游客用户至少有 cf_clearance，或者普通用户有所有必需的 cookies
                if hasAllRequiredCookies {
                    self.isCurrentlyLoggedIn = true
                    print("【调试】ℹ️ CookieManager: 活动状态已更新 - 用户'\(username)'已登录")
                    
                    // 打印是否是游客模式
                    if currentUserId == "guest_session" {
                        print("【调试】ℹ️ CookieManager: 当前用户是游客模式")
                    }
                } else {
                    self.isCurrentlyLoggedIn = false
                    print("【调试】⚠️ CookieManager: 用户 '\(currentUserId)' 缺少必需的 cookies，标记为未登录")
                    
                    // 打印缺少的必需 cookie
                    let missingCookies = requiredCookies.filter { requiredName in
                        userData.cookies[requiredName] == nil || (userData.cookies[requiredName]?.isEmpty ?? true)
                    }
                    if !missingCookies.isEmpty {
                        print("【调试】🍪 CookieManager: 缺少必需的 cookie: \(missingCookies.joined(separator: ", "))")
                    }
                }
            } else {
                self.isCurrentlyLoggedIn = false
                print("【调试】⚠️ CookieManager: 用户 '\(currentUserId)' 没有有效用户名，标记为未登录")
                // 可以选择在这里触发删除该用户
                DispatchQueue.main.async {
                    self.deleteUser(identifier: currentUserId)
                }
            }
        } else {
            self.activeCookies = [:]
            self.activeDomain = nil
            self.activeUsername = nil
            self.isCurrentlyLoggedIn = false
            print("【调试】ℹ️ CookieManager: 活动状态已更新 - 无活动用户")
        }
    }

    // MARK: - Persistence Helpers (UserDefaults + Codable)

    /**
     * 根据用户类型获取必需的 cookie 名称
     * - Parameter userIdentifier: 用户标识符
     * - Returns: 该用户类型必需的 cookie 名称数组
     */
    func getRequiredCookieNames(for userIdentifier: String) -> [String] {
        if userIdentifier == "guest_session" {
            return ["cf_clearance"] // 游客只需要 cf_clearance
        } else {
            return requiredCookieNames // 正常用户需要所有必需的 cookies
        }
    }

    /**
     * 将 `storedUsers` 字典编码为 Data 并保存到 UserDefaults。
     * 重命名 saveAccountsToDefaults -> saveUsersToDefaults
     */
    private func saveUsersToDefaults() {
        do {
            let encoder = JSONEncoder()
            // 使用新的属性名和 Key
            let data = try encoder.encode(storedUsers)
            UserDefaults.standard.set(data, forKey: DefaultsKeys.savedUsersData)
        } catch {
            print("【调试】❌ CookieManager: Failed to encode and save users data: \(error)")
        }
    }

    /**
     * 从 UserDefaults 加载 Data 并解码为 `storedUsers` 字典。
     * 重命名 loadAccountsFromDefaults -> loadUsersFromDefaults
     */
    private func loadUsersFromDefaults() {
        // 使用新的 Key
        guard let data = UserDefaults.standard.data(forKey: DefaultsKeys.savedUsersData) else {
            print("【调试】ℹ️ CookieManager: No saved users data found in UserDefaults for key '\(DefaultsKeys.savedUsersData)'. Starting with empty users.")
            self.storedUsers = [:]
            return
        }

        print("【调试】💾 CookieManager: Found saved users data (\(data.count) bytes). Attempting to decode...")
        do {
            let decoder = JSONDecoder()
            // 使用新的属性名和类型 UserCookieData
            self.storedUsers = try decoder.decode([String: UserCookieData].self, from: data)
            self.availableUserIdentifiers = self.storedUsers.keys.sorted()
        } catch {
            print("【调试】❌ CookieManager: Failed to decode users data: \(error). Clearing potentially corrupt data.")
            // 使用新的属性名和 Key
            self.storedUsers = [:]
            self.availableUserIdentifiers = []
            UserDefaults.standard.removeObject(forKey: DefaultsKeys.savedUsersData)
            UserDefaults.standard.removeObject(forKey: DefaultsKeys.lastActiveUserIdentifier)
        }
    }

    // MARK: - Singleton
    static let shared = CookieManager()
}
