// Persistence.swift
import CoreData

struct CoreDataManager {
    // MARK: - 1. 单例 (用于 App 运行时)
    static let shared = CoreDataManager()

    // MARK: - 2. 预览控制器 (用于 SwiftUI 预览)
    static var preview: CoreDataManager = {
        let result = CoreDataManager(inMemory: true) // 使用内存数据库
        let viewContext = result.container.viewContext
        // --- 创建更完整的预览数据 ---
//        for i in 0..<5 { // 创建 5 条预览消息
//            let newItem = Item(context: viewContext) // 使用 Core Data 生成的 Item 类
//            newItem.timestamp = Calendar.current.date(byAdding: .minute, value: -i * 15, to: Date())! // 时间递减
//            newItem.name = "预览消息 \(i + 1)" // 设置 name
//            newItem.isRead = (i % 2 == 0) // 设置 isRead (交替)
//        }
        // --- 预览数据创建结束 ---
        do {
            try viewContext.save() // 保存预览数据到内存数据库
        } catch {
            // 预览环境下的错误处理
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }() // 立即执行的闭包

    // MARK: - 3. Core Data 容器
    let container: NSPersistentContainer

    // MARK: - 4. 初始化器
    init(inMemory: Bool = false) {
        // --- !!! 关键检查点 !!! ---
        // 确保 "YourDataModelName" 与你的 .xcdatamodeld 文件名一致！
        // 例如: 如果文件名是 "Model.xcdatamodeld"，则用 "Model"
        let dataModelName = "Filink" // <--- !!! 修改这里，替换成你的 .xcdatamodeld 文件名 !!!
        container = NSPersistentContainer(name: dataModelName)
        // --- 检查结束 ---

        if inMemory {
            // 配置内存存储
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
            print("【调试】Core Data: Using in-memory store.")
        } else {
            // 打印数据库文件路径 (可选，用于调试)
            // print("【调试】Core Data: Store URL: \(container.persistentStoreDescriptions.first?.url?.absoluteString ?? "N/A")")
        }

        // 加载持久化存储
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error loading Core Data store: \(error), \(error.userInfo)")
            }
        })
        // 自动合并更改
        container.viewContext.automaticallyMergesChangesFromParent = true
        // 设置合并策略，解决冲突时以内存中的对象为准
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        print("【调试】Core Data: Persistent store loaded successfully.")
    }
}
