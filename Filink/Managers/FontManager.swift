import Foundation
import UIKit
import CoreText

// MARK: - FontScope
public enum FontScope: String, CaseIterable {
    case feed = "feed"
    case detail = "detail"
    case comment = "comment"
    case settings = "settings"

    var displayName: String {
        switch self {
        case .feed: return "首页"
        case .detail: return "详情页"
        case .comment: return "评论区"
        case .settings: return "设置页"
        }
    }
}

// MARK: - 错误定义
enum FontError: LocalizedError {
    case fontAlreadyExists
    case failedToCopyFont
    case failedToRegisterFont
    case failedToGetFontName
    case unsupportedFontType

    var errorDescription: String? {
        switch self {
        case .fontAlreadyExists:
            return "字体文件已存在。"
        case .failedToCopyFont:
            return "无法复制字体文件到应用目录。"
        case .failedToRegisterFont:
            return "无法注册字体。"
        case .failedToGetFontName:
            return "无法从文件中获取字体名称。"
        case .unsupportedFontType:
            return "不支持的字体文件格式，请选择 .ttf 或 .otf 文件。"
        }
    }
}

// MARK: - FontManager
class FontManager {
    static let shared = FontManager()

    private let fontNameKey = "selectedCustomFontName"
    private let fontScopeSettingsKey = "fontScopeSettings"
    
    // 字体文件存储在应用的 Documents/Fonts 目录下
    private var fontsDirectory: URL {
        let documents = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let fontsURL = documents.appendingPathComponent("Fonts")
        
        // 如果目录不存在，则创建它
        if !FileManager.default.fileExists(atPath: fontsURL.path) {
            try? FileManager.default.createDirectory(at: fontsURL, withIntermediateDirectories: true, attributes: nil)
        }
        return fontsURL
    }

    private init() {}

    // MARK: - Public API

    /// 在应用启动时加载并注册所有存储在沙盒中的字体
    func loadCustomFonts() {
        guard let fontFiles = try? FileManager.default.contentsOfDirectory(at: fontsDirectory, includingPropertiesForKeys: nil) else {
            return
        }

        for fontURL in fontFiles {
            // 过滤掉非字体文件
            if fontURL.pathExtension.lowercased() == "ttf" || fontURL.pathExtension.lowercased() == "otf" {
                var error: Unmanaged<CFError>?
                if !CTFontManagerRegisterFontsForURL(fontURL as CFURL, .process, &error) {
                    print("注册字体失败: \(fontURL.lastPathComponent), error: \(String(describing: error))")
                }
            }
        }
    }

    /// 从用户选择的文件URL保存字体
    /// - Parameters:
    ///   - url: 用户通过 UIDocumentPicker 选择的字体文件URL
    ///   - completion: 操作完成后的回调，成功时返回字体名称，失败时返回错误
    func saveFont(from url: URL, completion: @escaping (Result<String, Error>) -> Void) {
        let fileExtension = url.pathExtension.lowercased()
        guard fileExtension == "ttf" || fileExtension == "otf" else {
            completion(.failure(FontError.unsupportedFontType))
            return
        }
        
        let destinationURL = fontsDirectory.appendingPathComponent(url.lastPathComponent)

        // 检查文件是否已存在
        if FileManager.default.fileExists(atPath: destinationURL.path) {
            // 如果文件已存在，先尝试获取字体名称并返回
            if let fontName = getFontName(from: destinationURL) {
                completion(.success(fontName))
            } else {
                completion(.failure(FontError.fontAlreadyExists))
            }
            return
        }

        // 将文件从临时目录复制到沙盒
        do {
            try FileManager.default.copyItem(at: url, to: destinationURL)
            
            // 尝试从新保存的文件中获取字体名称
            guard let fontName = getFontName(from: destinationURL) else {
                completion(.failure(FontError.failedToGetFontName))
                return
            }
            
            // 动态注册新字体，以便立即在某些地方预览（如果需要）
            var error: Unmanaged<CFError>?
            if !CTFontManagerRegisterFontsForURL(destinationURL as CFURL, .process, &error) {
                print("动态注册新字体失败: \(String(describing: error))")
                // 即使注册失败，文件也已保存，下次启动时会重新尝试
            }
            
            completion(.success(fontName))
            
        } catch {
            print("复制字体文件失败: \(error)")
            completion(.failure(FontError.failedToCopyFont))
        }
    }
    
    /// 设置当前应用的自定义字体
    /// - Parameter fontName: 要设置的字体 PostScript 名称
    func setCurrentFont(name fontName: String) {
        UserDefaults.standard.set(fontName, forKey: fontNameKey)
    }

    /// 获取当前设置的自定义字体名称
    /// - Returns: 保存的字体名称，如果未设置则返回 nil
    func getCurrentFontName() -> String? {
        return UserDefaults.standard.string(forKey: fontNameKey)
    }
    
    /// 移除当前字体设置，恢复为系统默认
    func resetToDefaultFont() {
        UserDefaults.standard.removeObject(forKey: fontNameKey)
        UserDefaults.standard.removeObject(forKey: fontScopeSettingsKey)
    }

    // MARK: - Scope Management

    /// 设置特定作用域的字体是否启用
    func setFontEnabled(_ isEnabled: Bool, for scope: FontScope) {
        var settings = UserDefaults.standard.dictionary(forKey: fontScopeSettingsKey) as? [String: Bool] ?? [:]
        settings[scope.rawValue] = isEnabled
        UserDefaults.standard.set(settings, forKey: fontScopeSettingsKey)
    }

    /// 检查特定作用域的字体是否启用
    /// - Parameter scope: 要检查的作用域
    /// - Returns: 如果启用则返回 true，否则返回 false。默认为 true。
    func isFontEnabled(for scope: FontScope) -> Bool {
        let settings = UserDefaults.standard.dictionary(forKey: fontScopeSettingsKey) as? [String: Bool] ?? [:]
        return settings[scope.rawValue] ?? true // 默认启用
    }

    /// 根据作用域获取字体名称
    /// 如果该作用域启用了自定义字体，则返回全局字体名称；否则返回 nil。
    func getFontName(for scope: FontScope) -> String? {
        guard isFontEnabled(for: scope) else {
            return nil
        }
        return getCurrentFontName()
    }

    // MARK: - Private Helpers

    /// 从字体文件中解析出 PostScript 名称
    private func getFontName(from url: URL) -> String? {
        guard let dataProvider = CGDataProvider(url: url as CFURL),
              let cgFont = CGFont(dataProvider),
              let postScriptName = cgFont.postScriptName else {
            return nil
        }
        return postScriptName as String
    }
}
