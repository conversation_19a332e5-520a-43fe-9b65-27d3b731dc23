import UIKit

/// 图像文件管理器，用于将图像缓存到本地文件系统
class ImageFileManager {
    static let shared = ImageFileManager() // 单例模式
    
    private let fileManager = FileManager.default
    private let cacheDirectory: URL // 缓存目录URL
    
    private init() {
        // 获取应用的Caches目录，用于存储缓存文件
        // Caches目录是存放应用支持文件的地方，系统可能会在存储空间不足时删除这些文件
        if let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first {
            cacheDirectory = cachesDirectory.appendingPathComponent("ImageCache")
            print("【调试】ImageFileManager: Full ImageCache directory path: \(cacheDirectory.path)") // 打印完整的ImageCache目录
            // 如果缓存目录不存在，则创建它
            if !fileManager.fileExists(atPath: cacheDirectory.path) {
                do {
                    try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
                    print("【调试】ImageFileManager: Created cache directory at: \(cacheDirectory.path)") // 添加日志
                } catch {
                    print("【调试】ImageFileManager: Error creating image cache directory: \(error)")
                }
            }
        } else {
            fatalError("Could not find caches directory.")
        }
    }
    
    /// 根据键获取图像文件路径
    /// - Parameter key: 图像的唯一标识符
    /// - Returns: 图像在文件系统中的完整路径URL
    private func filePath(forKey key: String) -> URL {
        return cacheDirectory.appendingPathComponent(key)
    }
    
    /// 将图像保存到文件系统
    /// - Parameters:
    ///   - image: 要保存的UIImage对象
    ///   - key: 图像的唯一标识符
    func saveImage(_ image: UIImage, forKey key: String) {
        // 将图像转换为PNG数据，如果需要透明度，PNG是更好的选择
        // 如果对文件大小有严格要求，可以考虑JPEG，但会损失透明度
        if let data = image.pngData() {
            let url = filePath(forKey: key)
            do {
                try data.write(to: url)
            } catch {
                print("【调试】ImageFileManager: Error saving image to file system: \(error)")
            }
        }
    }
    
    /// 从文件系统加载图像
    /// - Parameter key: 图像的唯一标识符
    /// - Returns: 加载的UIImage对象，如果不存在则为nil
    func loadImage(forKey key: String) -> UIImage? {
        let url = filePath(forKey: key)
        guard fileManager.fileExists(atPath: url.path) else {
            // print("【调试】ImageFileManager: File does not exist at path: \(url.path)") // 添加日志
            return nil // 文件不存在
        }
        
        do {
            let data = try Data(contentsOf: url)
            return UIImage(data: data)
        } catch {
            print("【调试】ImageFileManager: Error loading image from file system: \(error)")
            return nil
        }
    }
    
    /// 从文件系统删除图像
    /// - Parameter key: 图像的唯一标识符
    func deleteImage(forKey key: String) {
        let url = filePath(forKey: key)
        guard fileManager.fileExists(atPath: url.path) else {
            return // 文件不存在
        }
        do {
            try fileManager.removeItem(at: url)
            print("【调试】ImageFileManager: Image deleted from file system: \(url.path)") // 打印完整路径
        } catch {
            print("【调试】ImageFileManager: Error deleting image from file system: \(error)")
        }
    }
    
    /// 清空所有缓存图像
    func clearCache() {
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil, options: [])
            for fileURL in fileURLs {
                try fileManager.removeItem(at: fileURL)
            }
            print("【调试】ImageFileManager: All image cache cleared.")
        } catch {
            print("【调试】ImageFileManager: Error clearing image cache: \(error)")
        }
    }
    
    /// 计算图像缓存的总大小
    /// - Returns: 缓存的总大小（MB）
    static func calculateCacheSize() -> Double {
        var totalSize: Double = 0.0 // 字节
        let fileManager = FileManager.default
        
        let cacheDirectory = ImageFileManager.shared.cacheDirectory
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey], options: [])
            for fileURL in fileURLs {
                let attributes = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = attributes.fileSize {
                    totalSize += Double(fileSize)
                }
            }
        } catch {
            print("【错误】ImageFileManager: 计算图片缓存大小失败: \(error.localizedDescription)")
        }
        
        return totalSize / (1024 * 1024) // 转换为 MB
    }
}
