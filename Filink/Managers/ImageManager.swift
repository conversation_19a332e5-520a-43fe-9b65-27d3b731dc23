import UIKit
import CryptoKit

// MARK: - Image Loading Error
enum ImageLoadingError: Error, LocalizedError {
    case invalidURL
    case noData
    case invalidImageData
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL provided"
        case .noData:
            return "No data received from server"
        case .invalidImageData:
            return "Invalid image data"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Cache Policy
struct CachePolicy {
    let maxAge: TimeInterval
    let maxMemorySize: Int
    let maxDiskSize: Int
    let maxItemCount: Int
    
    static let `default` = CachePolicy(
        maxAge: 24 * 60 * 60, // 24 hours
        maxMemorySize: 50 * 1024 * 1024, // 50MB
        maxDiskSize: 200 * 1024 * 1024, // 200MB
        maxItemCount: 1000
    )
}

// MARK: - Cache Metrics
struct CacheMetrics {
    var hitCount: Int = 0
    var missCount: Int = 0
    var evictionCount: Int = 0
    var currentMemorySize: Int = 0
    var currentDiskSize: Int = 0
    var currentItemCount: Int = 0
    
    var hitRate: Double {
        let total = hitCount + missCount
        return total > 0 ? Double(hitCount) / Double(total) : 0.0
    }
}

// MARK: - Image Manager
@MainActor
class ImageManager: ObservableObject {
    static let shared = ImageManager()
    
    let memoryCache = NSCache<NSString, UIImage>()
    private let placeholderCache = NSCache<NSString, UIImage>()
    private let diskCache: DiskImageCache
    private let policy: CachePolicy
    private var loadingTasks: [URL: Task<UIImage, Error>] = [:]
    
    @Published private(set) var metrics = CacheMetrics()
    
    private init(policy: CachePolicy = .default) {
        self.policy = policy
        self.diskCache = DiskImageCache(policy: policy)
        configureMemoryCache()
    }
    
    private func configureMemoryCache() {
        memoryCache.countLimit = policy.maxItemCount
        memoryCache.totalCostLimit = policy.maxMemorySize
        
        placeholderCache.countLimit = 50
        placeholderCache.totalCostLimit = 10 * 1024 * 1024 // 10MB
    }
    
    // MARK: - Public Interface
    func getCachedImage(for url: URL) -> UIImage? {
        let key = generateCacheKey(for: url)
        
        if let image = memoryCache.object(forKey: key as NSString) {
            metrics.hitCount += 1
            return image
        }
        
        metrics.missCount += 1
        return nil
    }
    
    func loadImage(from url: URL) async throws -> UIImage {
        // Check if already loading
        if let existingTask = loadingTasks[url] {
            return try await existingTask.value
        }
        
        // Create new loading task
        let task = Task<UIImage, Error> {
            defer { loadingTasks.removeValue(forKey: url) }
            return try await performImageLoad(from: url)
        }
        
        loadingTasks[url] = task
        return try await task.value
    }
    
    func getPlaceholderImage(size: CGSize, text: String) -> UIImage {
        let key = generatePlaceholderKey(size: size, text: text)
        
        if let cachedPlaceholder = placeholderCache.object(forKey: key as NSString) {
            return cachedPlaceholder
        }
        
        let placeholder = createPlaceholderImage(size: size, text: text)
        placeholderCache.setObject(placeholder, forKey: key as NSString)
        return placeholder
    }
    
    func clearCache() {
        memoryCache.removeAllObjects()
        placeholderCache.removeAllObjects()
        diskCache.clearCache()
        metrics = CacheMetrics()
    }
    
    func clearExpiredCache() async {
        await diskCache.clearExpiredItems()
    }
    
    // MARK: - Cache Size Calculation
    func calculateCacheSize() async -> Double {
        return await diskCache.calculateCacheSize()
    }
    
    func getCacheMetrics() -> CacheMetrics {
        return metrics
    }
    
    // MARK: - Synchronous Cache Methods (for compatibility)
    func saveImageToCache(_ image: UIImage, forKey key: String) {
        // Save to memory cache immediately
        memoryCache.setObject(image, forKey: key as NSString)
        
        // Save to disk cache asynchronously
        Task {
            await diskCache.storeImage(image, forKey: key)
        }
    }
    
    func loadImageFromCache(forKey key: String) -> UIImage? {
        // First check memory cache
        if let image = memoryCache.object(forKey: key as NSString) {
            return image
        }
        
        // Note: For disk cache, we would need async, but for compatibility
        // we'll just return nil and let the async loading handle it
        return nil
    }

    func isImageCached(forKey key: String) -> Bool {
        // 1. Check memory cache
        if memoryCache.object(forKey: key as NSString) != nil {
            return true
        }
        
        // 2. Check if file exists on disk
        if diskCache.fileExists(forKey: key) {
            return true
        }
        
        return false
    }
    
    // MARK: - Private Methods
    private func performImageLoad(from url: URL) async throws -> UIImage {
        let key = generateCacheKey(for: url)
        
        // Check disk cache first
        if let diskImage = await diskCache.retrieveImage(forKey: key) {
            // Store in memory cache
            memoryCache.setObject(diskImage, forKey: key as NSString)
            metrics.hitCount += 1
            return diskImage
        }
        
        // Download from network
        let image = try await downloadImage(from: url)
        
        // Store in both caches
        memoryCache.setObject(image, forKey: key as NSString)
        await diskCache.storeImage(image, forKey: key)
        
        updateMetrics(for: image)
        return image
    }
    
    private func downloadImage(from url: URL) async throws -> UIImage {
        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ImageLoadingError.networkError(URLError(.badServerResponse))
        }

        guard !data.isEmpty else {
            throw ImageLoadingError.noData
        }

        guard let image = UIImage(data: data) else {
            throw ImageLoadingError.invalidImageData
        }

        print("🌐 ImageManager: 下载图片成功，URL: \(url.absoluteString), 原始尺寸: \(image.size.width)x\(image.size.height)px")

        return image
    }
    
    private func generateCacheKey(for url: URL) -> String {
        return url.absoluteString.sha256
    }
    
    private func generatePlaceholderKey(size: CGSize, text: String) -> String {
        return "placeholder_\(Int(size.width))x\(Int(size.height))_\(text.hashValue)"
    }
    
    private func updateMetrics(for image: UIImage) {
        metrics.currentItemCount += 1
        if let imageData = image.pngData() {
            metrics.currentMemorySize += imageData.count
        }
    }
    
    private func createPlaceholderImage(size: CGSize, text: String) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { ctx in
            let context = ctx.cgContext
            
            // Determine placeholder style
            let isLoading = text.contains("加载中") || text.contains("loading")
            let isError = text.contains("失败") || text.contains("error") || text.contains("failed")
            
            // Background gradient
            let colors: [CGColor]
            if isError {
                colors = [
                    UIColor(red: 1.0, green: 0.95, blue: 0.95, alpha: 1.0).cgColor,
                    UIColor(red: 1.0, green: 0.9, blue: 0.9, alpha: 1.0).cgColor
                ]
            } else if isLoading {
                colors = [
                    UIColor(red: 0.95, green: 0.97, blue: 1.0, alpha: 1.0).cgColor,
                    UIColor(red: 0.9, green: 0.95, blue: 1.0, alpha: 1.0).cgColor
                ]
            } else {
                colors = [UIColor.systemGray6.cgColor, UIColor.systemGray5.cgColor]
            }
            
            if let gradient = CGGradient(
                colorsSpace: CGColorSpaceCreateDeviceRGB(),
                colors: colors as CFArray,
                locations: [0.0, 1.0]
            ) {
                context.drawLinearGradient(
                    gradient,
                    start: CGPoint(x: 0, y: 0),
                    end: CGPoint(x: size.width, y: size.height),
                    options: []
                )
            }
            
            // Border
            let cornerRadius: CGFloat = min(8, size.width * 0.05)
            let roundedRect = CGRect(origin: .zero, size: size).insetBy(dx: 1.5, dy: 1.5)
            let bezierPath = UIBezierPath(roundedRect: roundedRect, cornerRadius: cornerRadius)
            
            if isError {
                UIColor.systemRed.withAlphaComponent(0.3).setStroke()
            } else if isLoading {
                UIColor.systemBlue.withAlphaComponent(0.3).setStroke()
            } else {
                UIColor.systemGray4.setStroke()
            }
            
            bezierPath.lineWidth = 1.0
            bezierPath.stroke()
            
            // Icon
            let iconSize = min(size.width * 0.3, size.height * 0.3, 40)
            if iconSize >= 20 {
                let iconRect = CGRect(
                    x: (size.width - iconSize) / 2,
                    y: size.height * 0.35 - iconSize / 2,
                    width: iconSize,
                    height: iconSize
                )
                
                let symbolConfig = UIImage.SymbolConfiguration(pointSize: iconSize * 0.7, weight: .light)
                let symbolName: String
                let symbolColor: UIColor
                
                if isError {
                    symbolName = "exclamationmark.triangle"
                    symbolColor = UIColor.systemRed.withAlphaComponent(0.8)
                } else if isLoading {
                    symbolName = "arrow.triangle.2.circlepath"
                    symbolColor = UIColor.systemBlue.withAlphaComponent(0.8)
                } else {
                    symbolName = "photo"
                    symbolColor = UIColor.systemGray.withAlphaComponent(0.8)
                }
                
                if let symbolImage = UIImage(systemName: symbolName, withConfiguration: symbolConfig) {
                    symbolImage.withTintColor(symbolColor).draw(in: iconRect)
                }
            }
            
            // Text
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            paragraphStyle.lineBreakMode = .byTruncatingTail
            
            let fontSize = max(10, min(14, size.height * 0.13, size.width * 0.18))
            let font = UIFont.systemFont(ofSize: fontSize, weight: .medium)
            
            let textColor = isError ? 
                UIColor.systemRed.withAlphaComponent(0.8) : 
                isLoading ? UIColor.systemBlue.withAlphaComponent(0.8) : 
                UIColor.darkGray
            
            let attrs: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor,
                .paragraphStyle: paragraphStyle,
                .strokeWidth: -1.0,
                .strokeColor: UIColor.white.withAlphaComponent(0.3)
            ]
            
            let textInset: CGFloat = max(4, size.width * 0.06)
            let textHeight = font.lineHeight
            let textY = iconSize >= 20 ? size.height * 0.65 : (size.height - textHeight) / 2
            
            let stringRect = CGRect(
                x: textInset,
                y: textY,
                width: size.width - 2 * textInset,
                height: textHeight
            )
            
            (text as NSString).draw(
                with: stringRect,
                options: [.usesLineFragmentOrigin, .truncatesLastVisibleLine],
                attributes: attrs,
                context: nil
            )
        }
    }
}

// MARK: - Disk Cache
private class DiskImageCache {
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    private let policy: CachePolicy
    
    func fileExists(forKey key: String) -> Bool {
        let fileURL = cacheDirectory.appendingPathComponent(key)
        return fileManager.fileExists(atPath: fileURL.path)
    }
    
    init(policy: CachePolicy) {
        self.policy = policy
        
        let cacheDir = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        self.cacheDirectory = cacheDir.appendingPathComponent("ImageCache")
        
        createCacheDirectoryIfNeeded()
    }
    
    private func createCacheDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
    }
    
    func storeImage(_ image: UIImage, forKey key: String) async {
        guard let data = image.pngData() else { return }
        
        let fileURL = cacheDirectory.appendingPathComponent(key)
        try? data.write(to: fileURL)
    }
    
    func retrieveImage(forKey key: String) async -> UIImage? {
        let fileURL = cacheDirectory.appendingPathComponent(key)
        
        guard fileManager.fileExists(atPath: fileURL.path) else { return nil }
        
        // Check if file is expired
        if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
           let creationDate = attributes[.creationDate] as? Date,
           Date().timeIntervalSince(creationDate) > policy.maxAge {
            try? fileManager.removeItem(at: fileURL)
            return nil
        }
        
        guard let data = try? Data(contentsOf: fileURL) else { return nil }
        return UIImage(data: data)
    }
    
    func clearCache() {
        try? fileManager.removeItem(at: cacheDirectory)
        createCacheDirectoryIfNeeded()
    }
    
    func clearExpiredItems() async {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.creationDateKey]) else { return }
        
        for case let fileURL as URL in enumerator {
            if let resourceValues = try? fileURL.resourceValues(forKeys: [.creationDateKey]),
               let creationDate = resourceValues.creationDate,
               Date().timeIntervalSince(creationDate) > policy.maxAge {
                try? fileManager.removeItem(at: fileURL)
            }
        }
    }
    
    func calculateCacheSize() async -> Double {
        var totalSize: Double = 0.0
        
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0.0
        }
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = resourceValues.fileSize {
                    totalSize += Double(fileSize)
                }
            } catch {
                print("ImageManager: Error calculating file size for \(fileURL.path): \(error)")
            }
        }
        
        return totalSize / (1024 * 1024) // 转换为MB
    }
}

// MARK: - String Extension for SHA256
extension String {
    var sha256: String {
        let data = Data(self.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
}
