import Foundation

enum JSONError: Error {
    case fileNotFound(String)
    case decodingFailed(Error)
    case invalidData
}

class JSONDataLoader {
    /// 从指定名称的 JSON 文件中加载并解码数据。
    /// 文件应作为资源文件添加到项目中。
    /// - Parameters:
    ///   - filename: JSON 文件的名称（不包含扩展名）。
    ///   - bundle: 包含 JSON 文件的 Bundle，默认为 .main。
    /// - Returns: 解码后的 Codable 对象。
    /// - Throws: 如果文件未找到、数据无效或解码失败，则抛出 JSONError。
    static func load<T: Codable>(_ filename: String, as type: T.Type, in bundle: Bundle = .main) throws -> T {
        guard let url = bundle.url(forResource: filename, withExtension: "json") else {
            throw JSONError.fileNotFound("在 \(bundle.bundlePath) 中找不到文件: \(filename).json")
        }

        let data: Data
        do {
            data = try Data(contentsOf: url)
        } catch {
            throw JSONError.invalidData // 可能是文件损坏或无法读取
        }

        let decoder = JSONDecoder()
        do {
            return try decoder.decode(type, from: data)
        } catch {
            throw JSONError.decodingFailed(error)
        }
    }
}
