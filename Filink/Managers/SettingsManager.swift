// SettingsManager.swift
import Foundation
import Combine

final class SettingsManager: ObservableObject {

    // MARK: - UserDefaults Keys
    private enum SettingKeys {
        static let website = "settings_website_url"
        static let loginPath = "settings_login_path"
        static let testApiPath = "settings_test_api_path"
        static let siteCFKey = "settings_site_cf_key"
        static let isGuestLoginActive = "settings_is_guest_login_active" // 新增：游客登录状态
        static let showTagsFooter = "settings_show_tags_footer" // 新增：显示tags底部栏
        static let showPinnedPosts = "settings_show_pinned_posts" // 新增：显示置顶帖子
        static let enableAutoSave = "settings_enable_auto_save" // 新增：启用自动保存
        static let showWordCount = "settings_show_word_count" // 新增：显示字数统计
        static let autoSaveDelay = "settings_auto_save_delay" // 新增：自动保存延迟
        static let postContentFontScale = "settings_post_content_font_scale" // 新增：帖子内容字号缩放
    }

    // MARK: - Published Properties (Bindable & Persistent)
    @Published var website: String {
        didSet { UserDefaults.standard.set(website, forKey: SettingKeys.website) }
    }
    @Published var loginPath: String {
        didSet { UserDefaults.standard.set(loginPath, forKey: SettingKeys.loginPath) }
    }
    @Published var testApiPath: String {
        didSet { UserDefaults.standard.set(testApiPath, forKey: SettingKeys.testApiPath) }
    }
    @Published var siteCFKey: String {
        didSet { UserDefaults.standard.set(siteCFKey, forKey: SettingKeys.siteCFKey) }
    }
    @Published var isGuestLoginActive: Bool { // 新增：游客登录状态
        didSet { UserDefaults.standard.set(isGuestLoginActive, forKey: SettingKeys.isGuestLoginActive) }
    }
    @Published var showTagsFooter: Bool { // 新增：显示tags底部栏
        didSet { UserDefaults.standard.set(showTagsFooter, forKey: SettingKeys.showTagsFooter) }
    }
    @Published var showPinnedPosts: Bool { // 新增：显示置顶帖子
        didSet {
            UserDefaults.standard.set(showPinnedPosts, forKey: SettingKeys.showPinnedPosts)
            print("【设置更新】showPinnedPosts 更新为: \(showPinnedPosts)")
        }
    }
    @Published var enableAutoSave: Bool { // 新增：启用自动保存
        didSet { UserDefaults.standard.set(enableAutoSave, forKey: SettingKeys.enableAutoSave) }
    }
    @Published var showWordCount: Bool { // 新增：显示字数统计
        didSet { UserDefaults.standard.set(showWordCount, forKey: SettingKeys.showWordCount) }
    }
    @Published var autoSaveDelay: Double { // 新增：自动保存延迟（秒）
        didSet { UserDefaults.standard.set(autoSaveDelay, forKey: SettingKeys.autoSaveDelay) }
    }
    @Published var postContentFontScale: Double { // 新增：帖子内容字号缩放
        didSet { UserDefaults.standard.set(postContentFontScale, forKey: SettingKeys.postContentFontScale) }
    }
    // 用户名现在直接从 CookieManager 获取，不再直接持久化
    @Published private(set) var username: String? { // 改为 private(set)
        didSet {
            // 不再直接保存到 UserDefaults，而是由 CookieManager 管理
            print("【调试】SettingsManager: Username updated to '\(username ?? "nil")' (from CookieManager sync)")
        }
    }

    // 用于存储 Combine 订阅的集合
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        // Load initial values from UserDefaults or set defaults
        self.website = UserDefaults.standard.string(forKey: SettingKeys.website) ?? "https://linux.do" // 默认值设为test
        self.loginPath = UserDefaults.standard.string(forKey: SettingKeys.loginPath) ?? "/login"
        self.testApiPath = UserDefaults.standard.string(forKey: SettingKeys.testApiPath) ?? "/api/test"
        self.siteCFKey = UserDefaults.standard.string(forKey: SettingKeys.siteCFKey) ?? "0x4AAAAAAAc2BQjPEms9tKlM"
        // 确保 isGuestLoginActive 在所有其他存储属性之后初始化，因为它是一个新的 Published 属性
        self.isGuestLoginActive = UserDefaults.standard.bool(forKey: SettingKeys.isGuestLoginActive) // 从 UserDefaults 加载或默认 false
        self.showTagsFooter = UserDefaults.standard.object(forKey: SettingKeys.showTagsFooter) as? Bool ?? true // 从 UserDefaults 加载或默认 true
        self.showPinnedPosts = UserDefaults.standard.object(forKey: SettingKeys.showPinnedPosts) as? Bool ?? true // 从 UserDefaults 加载或默认 true
        self.enableAutoSave = UserDefaults.standard.object(forKey: SettingKeys.enableAutoSave) as? Bool ?? true // 从 UserDefaults 加载或默认 true
        self.showWordCount = UserDefaults.standard.object(forKey: SettingKeys.showWordCount) as? Bool ?? true // 从 UserDefaults 加载或默认 true
        self.autoSaveDelay = UserDefaults.standard.object(forKey: SettingKeys.autoSaveDelay) as? Double ?? 3.0 // 从 UserDefaults 加载或默认 3.0 秒
        self.postContentFontScale = UserDefaults.standard.object(forKey: SettingKeys.postContentFontScale) as? Double ?? 1.0 // 从 UserDefaults 加载或默认 1.0
        
        // username 属性的初始化依赖于 CookieManager，应在所有其他存储属性初始化之后
        self.username = CookieManager.shared.activeUsername // 初始化时从 CookieManager 获取当前用户名
        print("【调试】SettingsManager Initialized. Username: '\(username ?? "nil")'")

        // 订阅 CookieManager 的 activeUsername 变化
        CookieManager.shared.$activeUsername
            .receive(on: DispatchQueue.main) // 确保在主线程更新 UI
            .sink { [weak self] newUsername in
                self?.username = newUsername
            }
            .store(in: &cancellables)
    }

    // Optional: Add a reset function if needed
    func resetToDefaults() {
        self.website = "https://linux.do" // 设置为与初始化相同的默认值
        self.loginPath = "/login"
        self.testApiPath = "/api/test"
        self.siteCFKey = "0x4AAAAAAAc2BQjPEms9tKlM"
        self.showTagsFooter = true // 重置为默认值
        self.showPinnedPosts = true // 重置为默认值
        self.enableAutoSave = true // 重置为默认值
        self.showWordCount = true // 重置为默认值
        self.autoSaveDelay = 3.0 // 重置为默认值
        self.postContentFontScale = 1.0 // 重置为默认值
        // 重置时，不直接设置 username 为 nil，而是依赖 CookieManager 的登出操作
        // self.username = nil // 这会触发 didSet 并从 UserDefaults 移除
        print("【调试】SettingsManager reset to defaults.")
    }

    // --- 5. 添加一个专门用于清除用户名的方法 (用于登出) ---
    // 此方法现在应该触发 CookieManager 的登出，而不是直接清除本地用户名
    func clearUsername() {
        // 触发 CookieManager 的登出，CookieManager 会自动更新 activeUsername 为 nil
        CookieManager.shared.logout()
        print("【调试】SettingsManager: Triggered CookieManager logout. Username will be cleared via sync.")
    }
    
    // MARK: - App Constants
    
    /// 应用程序全局常量
    struct AppConstants {
        /// 服务器基础URL
        static var baseURL: String {
            return shared.website.isEmpty ? "https://linux.do" : shared.website
        }
        
        /// 头像相关常量
        struct Avatar {
            /// 默认头像图片名称
            static let defaultImage = "person.circle.fill"
            
            /// 头像尺寸枚举
            enum Size: String {
                case small = "48"     // 评论区
                case medium = "72"    // 发帖用户
                case large = "128"    // 设置页面
            }
            
            /// 构建完整的头像URL（使用完整的avatar template）
            /// - Parameters:
            ///   - avatarTemplate: 完整的头像模板路径（如 "/letter_avatar/username/{size}/filename.png"）
            ///   - size: 头像尺寸
            /// - Returns: 完整的头像URL
            static func buildAvatarURL(fromTemplate avatarTemplate: String, size: Size) -> URL? {
                // 如果头像模板为空，则返回nil，触发默认头像逻辑
                guard !avatarTemplate.isEmpty else {
                    return nil
                }

                // 替换模板中的 {size} 占位符
                let processedTemplate = avatarTemplate.replacingOccurrences(of: "{size}", with: size.rawValue)

                // 拼接主网站链接
                let urlString = "\(AppConstants.baseURL)\(processedTemplate)"
                return URL(string: urlString)
            }

            /// 构建完整的头像URL（兼容旧方法，用于文件名格式）
            /// - Parameters:
            ///   - username: 用户名
            ///   - avatarFilename: 头像文件名
            ///   - size: 头像尺寸
            /// - Returns: 完整的头像URL
            static func buildAvatarURL(forUsername username: String, avatarFilename: String, size: Size) -> URL? {
                // 如果头像文件名为空，则返回nil，触发默认头像逻辑
                guard !avatarFilename.isEmpty else {
                    return nil
                }

                // 构建传统格式的模板路径
                let avatarTemplate = "/user_avatar/linux.do/\(username)/{\(size.rawValue)}/\(avatarFilename)"

                // 使用新的模板方法
                return buildAvatarURL(fromTemplate: avatarTemplate, size: size)
            }
            
            /// 为User对象构建头像URL
            /// - Parameters:
            ///   - user: 用户对象
            ///   - size: 头像尺寸
            /// - Returns: 完整的头像URL
            static func buildAvatarURL(for user: User?, size: Size) -> URL? {
                guard let user = user,
                      let username = user.username,
                      let avatarFilename = user.avatar else {
                    // 如果用户、用户名或头像文件名不存在，则返回nil
                    // print("【调试】SettingsManager.AppConstants.Avatar.buildAvatarURL: 用户对象、用户名或头像文件名缺失，返回nil以显示默认头像。")
                    return nil
                }
                // print("【调试】SettingsManager.AppConstants.Avatar.buildAvatarURL: 为用户 '\(username)' 构建头像URL，文件名: '\(avatarFilename)', 尺寸: '\(size.rawValue)'")
                return buildAvatarURL(forUsername: username, avatarFilename: avatarFilename, size: size)
            }
        }
    }
    
    // MARK: - Singleton
    static let shared = SettingsManager()
}
