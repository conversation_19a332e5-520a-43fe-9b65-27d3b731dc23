import SwiftUI
import UIKit

// 内容类型枚举
public enum ContentItem: Identifiable, Equatable {
    case text(String, isBold: Bool = false, isItalic: Bool = false, isUnderline: Bool = false)
    case link(text: String, url: URL)
    case image(url: URL, altText: String, width: CGFloat? = nil, height: CGFloat? = nil, linkURL: URL? = nil, isEmoji: Bool = false)
    case newline // 专门的换行类型
    case heading1(String) // H1 标题
    case heading2(String) // H2 标题
    case heading3(String) // H3 标题
    case listItem(text: String, level: Int, isOrdered: Bool, number: Int? = nil) // 列表项
    case footnoteReference(index: String, refText: String) // 脚注引用，例如 [1]你好
    case footnoteContent(index: String, content: String) // 脚注内容，例如 1. some text
    case spoiler(text: String, isRevealed: Bool = false) // 剧透内容
    case horizontalRule // 新增：水平分割线
    case poll(PollItem) // 新增：投票

    // 【优化】使用更稳定的ID生成方法
    public var id: String {
        switch self {
        case .text(let text, let isBold, let isItalic, let isUnderline):
            // 对于文本，使用其内容的哈希值和粗体/斜体/下划线状态生成ID
            return "text_\(text.hashValue)_\(isBold)_\(isItalic)_\(isUnderline)"
        case .link(let text, let url):
            // 对于链接，组合文本哈希和URL绝对字符串的哈希
            return "link_\(text.hashValue)_\(url.absoluteString.hashValue)"
        case .image(let url, _, let width, let height, let linkURL, let isEmoji):
            // 对于图片，组合URL哈希和指定的宽高（nil也作为一种状态）
            let widthStr = width.map { String(describing: $0) } ?? "nil"
            let heightStr = height.map { String(describing: $0) } ?? "nil"
            let linkStr = linkURL?.absoluteString.hashValue.description ?? "nil"
            let emojiStr = isEmoji ? "emoji" : "notEmoji"
            return "image_\(url.absoluteString.hashValue)_\(widthStr)_\(heightStr)_\(linkStr)_\(emojiStr)"
        case .newline:
            // 【核心优化】换行符使用静态ID，确保内容不变时ID也不变，避免不必要的视图更新
            return "newline_static_identifier"
        case .heading1(let text):
            // 对于H1标题，使用文本内容的哈希值
            return "heading1_\(text.hashValue)"
        case .heading2(let text):
            // 对于H2标题，使用文本内容的哈希值
            return "heading2_\(text.hashValue)"
        case .heading3(let text):
            // 对于H3标题，使用文本内容的哈希值
            return "heading3_\(text.hashValue)"
        case .listItem(let text, let level, let isOrdered, let number):
            // 对于列表项，组合文本哈希、层级、列表类型和序号
            let numberStr = number.map { String($0) } ?? "nil"
            return "listItem_\(text.hashValue)_\(level)_\(isOrdered)_\(numberStr)"
        case .footnoteReference(let index, let refText):
            return "footnoteRef_\(index)_\(refText.hashValue)"
        case .footnoteContent(let index, let content):
            return "footnoteContent_\(index)_\(content.hashValue)"
        case .spoiler(let text, let isRevealed):
            return "spoiler_\(text.hashValue)_\(isRevealed)"
        case .horizontalRule:
            return "hr_\(UUID().uuidString)" // 水平线每次都认为是新的
        case .poll(let pollItem):
            return "poll_\(pollItem.id)"
        }
    }
    
    // 【优化】实现更精确的Equatable协议，直接比较内容值而不是依赖ID字符串的完美构造
    public static func == (lhs: ContentItem, rhs: ContentItem) -> Bool {
        switch (lhs, rhs) {
        case let (.text(lText, lBold, lItalic, lUnderline), .text(rText, rBold, rItalic, rUnderline)):
            return lText == rText && lBold == rBold && lItalic == rItalic && lUnderline == rUnderline
        case let (.link(lText, lUrl), .link(rText, rUrl)):
            return lText == rText && lUrl == rUrl
        case let (.image(lUrl, lAlt, lWidth, lHeight, lLinkURL, lEmoji), .image(rUrl, rAlt, rWidth, rHeight, rLinkURL, rEmoji)):
            return lUrl == rUrl && lAlt == rAlt && lWidth == rWidth && lHeight == rHeight && lLinkURL == rLinkURL && lEmoji == rEmoji
        case (.newline, .newline):
            return true // 所有 newline 实例在内容上被认为是相等的
        case let (.heading1(lText), .heading1(rText)):
            return lText == rText
        case let (.heading2(lText), .heading2(rText)):
            return lText == rText
        case let (.heading3(lText), .heading3(rText)):
            return lText == rText
        case let (.listItem(lText, lLevel, lOrdered, lNumber), .listItem(rText, rLevel, rOrdered, rNumber)):
            return lText == rText && lLevel == rLevel && lOrdered == rOrdered && lNumber == rNumber
        case let (.footnoteReference(lIndex, lRefText), .footnoteReference(rIndex, rRefText)):
            return lIndex == rIndex && lRefText == rRefText
        case let (.footnoteContent(lIndex, lContent), .footnoteContent(rIndex, rContent)):
            return lIndex == rIndex && lContent == rContent
        case let (.spoiler(lText, lRevealed), .spoiler(rText, rRevealed)):
            return lText == rText && lRevealed == rRevealed
        case (.horizontalRule, .horizontalRule):
            return false // 水平线总是被认为是不同的，以强制重绘
        case let (.poll(lhsItem), .poll(rhsItem)):
            return lhsItem == rhsItem
        default:
            return false // 不同类型自然不相等
        }
    }
}
