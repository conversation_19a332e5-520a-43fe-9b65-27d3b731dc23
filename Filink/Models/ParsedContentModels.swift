// Filename: ParsedContentModels.swift
// Purpose: 定义 HTML 解析后用于表示内容的 Swift 数据结构模型。

import SwiftUI // 引入 SwiftUI 以使用 CGFloat, Identifiable, Hashable 等
import UIKit   // 引入 UIKit 以在 ProcessedPart 中使用 NSAttributedString (如果需要渲染富文本)

// MARK: - 投票相关数据模型

/// 表示一个投票中的单个选项
public struct PollOption: Identifiable, Hashable {
    public let id: String
    public let label: String
}

/// 表示一个完整的投票模块
public struct PollItem: Identifiable, Hashable {
    public var id: String { name }
    
    let name: String
    let options: [PollOption]
    let type: String // e.g., "multiple"
    let minSelections: Int
    let maxSelections: Int
    let status: String // e.g., "open"
    let isPublic: Bool
    let resultsVisibility: String // e.g., "always"
    var voterCount: Int
    
    public static func == (lhs: PollItem, rhs: PollItem) -> Bool {
        return lhs.name == rhs.name &&
               lhs.options == rhs.options &&
               lhs.type == rhs.type &&
               lhs.minSelections == rhs.minSelections &&
               lhs.maxSelections == rhs.maxSelections &&
               lhs.status == rhs.status &&
               lhs.isPublic == rhs.isPublic &&
               lhs.resultsVisibility == rhs.resultsVisibility &&
               lhs.voterCount == rhs.voterCount
    }

    public func hash(into hasher: inout Hasher) {
        hasher.combine(name)
        hasher.combine(options)
        hasher.combine(type)
        hasher.combine(minSelections)
        hasher.combine(maxSelections)
        hasher.combine(status)
        hasher.combine(isPublic)
        hasher.combine(resultsVisibility)
        hasher.combine(voterCount)
    }
}


// --- 枚举：定义解析出的顶层元素类型 ---
// 每个 ParsedElement 会属于以下类型之一
enum ParsedElementType {
    /// 表示一个独立的图片块。
    /// - Parameters:
    ///   - url: 图片的 URL 地址。
    ///   - width: 图片的可选宽度 (来自 HTML <img> 标签的 width 属性)。
    ///   - height: 图片的可选高度 (来自 HTML <img> 标签的 height 属性)。
    case imageBlock(url: String, width: CGFloat?, height: CGFloat?)

    /// 表示一段富文本内容，由一个或多个 RichTextPart 组成。
    /// 通常对应 HTML 中的一个块级元素，如 <p>, <h3>, <li> 等。
    /// - Parameters:
    ///   - parts: 组成该富文本块的部件数组。
    case richText([RichTextPart])

    // 未来可以扩展，例如：
    // case codeBlock(language: String?, code: String) // 代码块
    // case blockquote([ParsedElement]) // 引用块，可以包含其他元素
    // case list(isOrdered: Bool, items: [ParsedElement]) // 列表
}

// --- 枚举：定义富文本块 (richText) 内部的部件类型 ---
// 用于表示文本、链接、行内图片等。
// 遵循 Identifiable 和 Hashable 以便在 SwiftUI 列表和集合中使用。
enum RichTextPart: Identifiable, Hashable {
    /// 表示一段纯文本。
    case plain(String)

    /// 表示一个超链接 (<a> 标签)。
    /// - Parameters:
    ///   - text: 链接显示的文本。
    ///   - url: 链接指向的 URL 地址。
    case hyperlink(text: String, url: String)

    /// 表示一个行内图片 (<img> 标签，不在独立的图片块中)。
    /// 注意：在给定的 HTML 示例中，图片主要是作为 imageBlock 处理。
    /// - Parameters:
    ///   - url: 图片的 URL 地址。
    ///   - width: 图片的可选宽度。
    ///   - height: 图片的可选高度。
    case inlineImage(url: String, width: CGFloat?, height: CGFloat?)

    // 未来可以扩展，例如：
    // case bold(String) // 加粗文本
    // case italic(String) // 斜体文本
    // case strikethrough(String) // 删除线文本

    // --- Identifiable 协议实现 ---
    // 提供一个稳定的标识符，用于 SwiftUI 等场景。
    // 注意：plain text 的 ID 基于内容的哈希值生成，可能在文本变化时改变。
    // Link 和 Image 的 ID 基于 URL，相对稳定。
    var id: String {
        switch self {
        case .plain(let text):
            // 为纯文本生成基于内容的哈希 ID
            var hasher = Hasher()
            hasher.combine("plain")
            hasher.combine(text)
            return "plain:\(hasher.finalize())"
        case .hyperlink(_, let url):
            // 链接 ID 基于 URL
            return "link:\(url)"
        case .inlineImage(let url, _, _):
            // 行内图片 ID 基于 URL
            return "img:\(url)"
        }
    }

    // --- Equatable 协议实现 (Hashable 自动合成，但显式提供更清晰) ---
    // 用于比较两个 RichTextPart 是否相等。
    static func == (lhs: RichTextPart, rhs: RichTextPart) -> Bool {
        switch (lhs, rhs) {
        case (.plain(let l), .plain(let r)):
            return l == r
        case (.hyperlink(let lt, let lu), .hyperlink(let rt, let ru)):
            return lt == rt && lu == ru
        case (.inlineImage(let lu, let lw, let lh), .inlineImage(let ru, let rw, let rh)):
            return lu == ru && lw == rw && lh == rh
        default:
            // 不同类型的 Part 不相等
            return false
        }
    }

    // --- Hashable 协议实现 ---
    // 为 Part 提供哈希值，以便在 Set 或 Dictionary 中使用。
    func hash(into hasher: inout Hasher) {
        switch self {
        case .plain(let text):
            hasher.combine("plain") // 结合类型标识
            hasher.combine(text)
        case .hyperlink(let text, let url):
            hasher.combine("hyperlink") // 结合类型标识
            hasher.combine(text)
            hasher.combine(url)
        case .inlineImage(let url, let width, let height):
            hasher.combine("inlineImage") // 结合类型标识
            hasher.combine(url)
            hasher.combine(width)
            hasher.combine(height)
        }
    }
}

// --- 结构体：表示一个解析后的顶层内容元素 ---
// 这是解析函数最终返回的数组中的基本单位。
// 遵循 Identifiable 以便在 SwiftUI 列表视图中使用。
struct ParsedElement: Identifiable {
    /// 自动生成的唯一 ID。
    let id = UUID()
    /// 该元素的类型 (图片块 或 富文本块)。
    let type: ParsedElementType
}


// --- 结构体：用于渲染处理后的部件 (可选) ---
// 这个结构体看起来是用于将 ParsedElement/RichTextPart 转换为渲染引擎
// (如 UIKit/CoreText 或 SwiftUI 的 Text) 更易于处理的格式，例如 NSAttributedString。
// 它不直接参与 HTML 解析过程，但可能在解析结果的后续处理中使用。
struct ProcessedPart: Identifiable {
    /// 自动生成的唯一 ID。
    let id = UUID()

    /// 定义渲染部件的具体内容类型。
    enum Content {
        /// 已转换为 NSAttributedString 的文本，可以包含各种样式。
        case attributed(NSAttributedString)
        /// 需要渲染的行内图片。
        case inlineImage(url: String, width: CGFloat?, height: CGFloat?)
        /// 表示一个显式的换行（可能来自 <br> 或需要插入的换行）。
        case lineBreak
    }
    /// 该渲染部件的具体内容。
    let content: Content
}
