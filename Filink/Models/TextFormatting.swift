import Foundation

/// 文本格式化工具类
/// 用于HTML解析过程中存储格式化信息
struct TextFormatting {
    /// 文本是否加粗
    var isBold: Bool = false
    
    /// 文本是否斜体
    var isItalic: Bool = false
    
    /// 文本是否带下划线
    var isUnderlined: Bool = false
    
    /// 文本是否为删除线
    var isStrikethrough: Bool = false
    
    /// 文本缩进级别
    var indentLevel: Int = 0
    
    /// 文本颜色
    var color: String? = nil
    
    /// 文本背景色
    var backgroundColor: String? = nil
    
    /// 文本字体大小
    var fontSize: Int? = nil
    
    /// 文本字体
    var fontFamily: String? = nil
    
    /// 初始化空的格式信息
    init() {}
    
    /// 使用指定参数初始化
    init(isBold: Bool = false, isItalic: Bool = false, isUnderlined: Bool = false, 
         isStrikethrough: Bool = false, indentLevel: Int = 0, color: String? = nil, 
         backgroundColor: String? = nil, fontSize: Int? = nil, fontFamily: String? = nil) {
        self.isBold = isBold
        self.isItalic = isItalic
        self.isUnderlined = isUnderlined
        self.isStrikethrough = isStrikethrough
        self.indentLevel = indentLevel
        self.color = color
        self.backgroundColor = backgroundColor
        self.fontSize = fontSize
        self.fontFamily = fontFamily
    }
    
    /// 创建新的格式信息，保留当前信息
    func with(isBold: Bool? = nil, isItalic: Bool? = nil, isUnderlined: Bool? = nil,
              isStrikethrough: Bool? = nil, indentLevel: Int? = nil, color: String? = nil,
              backgroundColor: String? = nil, fontSize: Int? = nil, fontFamily: String? = nil) -> TextFormatting {
        
        return TextFormatting(
            isBold: isBold ?? self.isBold,
            isItalic: isItalic ?? self.isItalic,
            isUnderlined: isUnderlined ?? self.isUnderlined,
            isStrikethrough: isStrikethrough ?? self.isStrikethrough,
            indentLevel: indentLevel ?? self.indentLevel,
            color: color ?? self.color,
            backgroundColor: backgroundColor ?? self.backgroundColor,
            fontSize: fontSize ?? self.fontSize,
            fontFamily: fontFamily ?? self.fontFamily
        )
    }
} 