import SwiftUI

// FeedFooterView 专门负责标签显示和选择，包含自己的显示逻辑
struct FeedFooterView: View {
    // 滚动相关属性
    var bottomInset: CGFloat
    var newOffset: CGFloat
    var oldOffset: CGFloat

    // 显示逻辑相关属性
    var isFooterLockedByLoader: Bool
    var isLoadingAreaVisible: Bool
    var isLoadingMore: Bool

    // 回调
    var onTagSelected: ((String?) -> Void)?

    @EnvironmentObject private var settingsManager: SettingsManager
    @StateObject private var tagLoader = TagDataLoader.shared
    @State private var selectedTagInfo: String?
    @State private var displayedTags: [Tag] = []
    @State private var selectedTagId: String?

    private let maxTagCount = 6

    /// Footer 显示逻辑 - 从 FeedView 移动过来
    private var shouldShow: Bool {
        // 首先检查用户是否开启了标签 Footer
        guard settingsManager.showTagsFooter else { return false }

        let result: Bool = {
            // 第一优先级：在顶部时显示
            if newOffset >= 0 {
                return true
            }

            // 第二优先级：如果被加载器锁定，强制隐藏
            if isFooterLockedByLoader {
                return false
            }

            // 第三优先级：如果加载区域可见或正在加载，强制隐藏
            if isLoadingAreaVisible || isLoadingMore {
                return false
            }

            // 第四优先级：使用滚动方向判断逻辑
            let isScrollingDown = newOffset < oldOffset

            // 向下滚动时隐藏，向上滚动时显示
            return !isScrollingDown
        }()

        return result
    }

    private var defaultTags: [Tag] {
        let targetTagIds = ["人工智能", "快问快答", "抽奖"]
        let allTags = tagLoader.allTags
        var result: [Tag] = []
        for tagId in targetTagIds {
            if let tag = allTags.first(where: { $0.id == tagId }) {
                result.append(tag)
            }
        }
        return result
    }

    private func addTagToDisplay(_ newTag: Tag) {
        displayedTags.removeAll { $0.id == newTag.id }
        displayedTags.insert(newTag, at: 0)
        if displayedTags.count > maxTagCount {
            displayedTags = Array(displayedTags.prefix(maxTagCount))
        }
        selectedTagId = newTag.id
    }

    // 移除内部的显示/隐藏逻辑，完全由外层的conditionalSafeAreaInset控制
    // 这样避免了双重控制导致的状态不一致问题

    private func initializeDisplayedTags() {
        let currentDefaultTags = defaultTags
        if displayedTags.isEmpty && !currentDefaultTags.isEmpty {
            displayedTags = currentDefaultTags
        }
    }

    var body: some View {
        Group {
            if shouldShow {
                VStack(spacing: 0) {
                    tagsView
                }
                .transition(
                    AnyTransition.opacity
                        .combined(with: .move(edge: .bottom))
                        .combined(with: .scale(scale: 0.95, anchor: .bottom))
                )
            }
        }
        .animation(
            .interactiveSpring(
                response: 0.35,
                dampingFraction: 0.82,
                blendDuration: 0.25
            ),
            value: shouldShow
        )
        .task {
            await tagLoader.loadTagsIfNeeded()
        }
        .onAppear {
            initializeDisplayedTags()
        }
        .onChange(of: tagLoader.tagGroups.count) { _ in
            if displayedTags.isEmpty {
                initializeDisplayedTags()
            }
        }
    }

    // 提取的标签视图
    @ViewBuilder
    private var tagsView: some View {
        HStack(spacing: 0) {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(displayedTags, id: \.id) { tag in
                        Button(action: {
                            if selectedTagId == tag.id {
                                selectedTagId = nil
                                onTagSelected?(nil)
                            } else {
                                selectedTagId = tag.id
                                onTagSelected?(tag.name)
                            }
                        }) {
                            HStack(spacing: 3) {
                                Image(systemName: "number")
                                    .font(.system(size: 16, weight: selectedTagId == tag.id ? .bold : .regular))
                                Text(tag.name)
                                    .font(.system(size: 16, weight: selectedTagId == tag.id ? .bold : .regular))
                                    .lineLimit(1)
                                    .fixedSize(horizontal: true, vertical: false)
                            }
                            .foregroundColor(.orange)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 6)
                            .background(Color.yellow.opacity(0.2))
                            .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }

            Menu {
                ForEach(tagLoader.tagGroups) { group in
                    Menu(group.name) {
                        ForEach(group.tags, id: \.id) { tag in
                            Button(action: {
                                selectedTagInfo = "已选: \(group.name) > \(tag.name)"
                                addTagToDisplay(tag)
                                onTagSelected?(tag.name)
                            }) {
                                HStack {
                                    if let iconName = tag.icon {
                                        Image(systemName: iconName)
                                    }
                                    Text(tag.text)
                                    Spacer()
                                    Text("\(tag.count)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }
            } label: {
                HStack(spacing: 3) {
                    Text("更多")
                        .font(.system(size: 16))
                    Image(systemName: "chevron.down")
                        .font(.system(size: 16))
                }
                .foregroundColor(.primary)
                .padding(.leading, 12)
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 10)
        .background(.regularMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 0))
    }
}


