import SwiftUI

// MARK: - PreferenceKey for visibility detection
struct VisibilityPreferenceKey: PreferenceKey {
    static var defaultValue: CGRect = .zero
    static func reduce(value: inout CGRect, nextValue: () -> CGRect) {
        value = nextValue()
    }
}

/// 专门负责显示各种加载状态的组件
/// 包括自动加载、空状态等
struct FeedLoadingView: View {
    // MARK: - Properties
    let posts: [Post]
    let isLoading: Bool
    let isLoadingMore: Bool
    let onLoadMore: () async -> Void
    let onBottomAreaVisibilityChanged: ((Bool) -> Void)?
    let bottomInset: CGFloat

    // MARK: - State
    @State private var hasTriggeredAutoLoad = false
    @State private var isIndicatorVisible = false
    @State private var lastPostsCount = 0
    @State private var lastFramePosition: CGRect = .zero
    @State private var isLayoutChanging = false

    // MARK: - Environment
    @EnvironmentObject private var settingsManager: SettingsManager
    
    // MARK: - Body
    var body: some View {
        Group {
            if !posts.isEmpty {
                // 有帖子时的加载更多区域
                loadMoreSection
            } else if isLoading {
                // 列表为空且正在加载时的状态
                emptyLoadingSection
            } else {
                // 列表为空且不在加载时，显示空状态
                emptyStateView
            }
        }
        .onChange(of: posts.count) { newCount in
            // 当帖子数量变化时，重置自动加载状态
            if newCount != lastPostsCount {
                if newCount > lastPostsCount {
                    // 帖子数量增加，说明加载了新数据，重置触发标志
                    hasTriggeredAutoLoad = false
                }
                lastPostsCount = newCount
            }
        }


        .onChange(of: settingsManager.showTagsFooter) { _ in
            // 当标签栏显示设置变化时，标记为布局变化状态
            markLayoutChanging(reason: "showTagsFooter设置变化")
        }
        .onChange(of: settingsManager.showPinnedPosts) { _ in
            // 当置顶帖子显示设置变化时，标记为布局变化状态
            markLayoutChanging(reason: "showPinnedPosts设置变化")
        }
    }
    
    // MARK: - Private Views
    @ViewBuilder
    private var loadMoreSection: some View {
        // 自动加载模式：根据加载状态显示不同内容
        Group {
            if isLoadingMore {
                // 正在加载时显示加载指示器
                unifiedLoadingIndicator
                    .background(visibilityDetectionView)
            } else {
                // 未加载时显示触发区域（透明，但可以检测可见性）
                Color.clear
                    .frame(height: 60)
                    .background(visibilityDetectionView)
            }
        }
        .onChange(of: isLoadingMore) { newValue in
            // 当加载完成时，重置可见性状态
            if !newValue {
                isIndicatorVisible = false
            }
        }
    }

    /// 统一的加载指示器组件
    @ViewBuilder
    private func loadingIndicator(scale: CGFloat = 0.9, showBackground: Bool = true) -> some View {
        VStack(spacing: 8) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(scale)

            Text("正在加载更多...")
                .font(.footnote)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 44) // 固定高度，与按钮保持一致
        .padding(.vertical, 8) // 统一外边距
        .background(showBackground ? Color(UIColor.systemBackground).opacity(0.8) : Color.clear)
    }

    // 统一的加载指示器 - 向后兼容
    @ViewBuilder
    private var unifiedLoadingIndicator: some View {
        loadingIndicator()
    }


    
    @ViewBuilder
    private var emptyLoadingSection: some View {
        ProgressView()
            .progressViewStyle(CircularProgressViewStyle())
            .frame(maxWidth: .infinity)
            .padding(.vertical, 24)
    }

    /// 统一的可见性检测视图
    @ViewBuilder
    private var visibilityDetectionView: some View {
        GeometryReader { indicatorGeometry in
            Color.clear
                .preference(key: VisibilityPreferenceKey.self,
                          value: indicatorGeometry.frame(in: .global))
        }
        .onPreferenceChange(VisibilityPreferenceKey.self) { frame in
            checkIndicatorVisibility(frame: frame)
        }
    }

    /// 空状态视图
    @ViewBuilder
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "moon.stars.fill")
                .font(.system(size: 48, weight: .light))
                .foregroundStyle(.secondary)
                .opacity(0.6)
                .padding(.bottom, 8)

            Text("这里空空如也")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)

            Text("尝试下拉刷新或切换模式看看")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 40)
        .padding(.vertical, 80)
        .multilineTextAlignment(.center)
        .transition(.opacity.animation(.easeInOut))
    }

    // MARK: - Private Methods

    /// 计算可见性相关的基础数据
    private func calculateVisibilityData(for frame: CGRect) -> (screenHeight: CGFloat, visibleHeight: CGFloat, isVisible: Bool, isNearBottom: Bool) {
        let screenHeight = UIScreen.main.bounds.height
        let visibleHeight = screenHeight - bottomInset  // 减去 TabBar 和安全区域高度
        let threshold: CGFloat = 10

        let isVisible = frame.minY < visibleHeight && frame.maxY > 0
        let isNearBottom = frame.minY < (visibleHeight + threshold)

        return (screenHeight, visibleHeight, isVisible, isNearBottom)
    }



    /// 检查指示器是否在可视区域内 - 用于自动加载模式
    private func checkIndicatorVisibility(frame: CGRect) {
        let visibilityData = calculateVisibilityData(for: frame)

        // 调试输出：验证自动加载模式的可见性检测

        // 通知父组件底部区域可见性变化
        onBottomAreaVisibilityChanged?(visibilityData.isNearBottom)

        // 检测是否是因为布局变化导致的位置改变
        let isSignificantLayoutChange = abs(frame.minY - lastFramePosition.minY) > 50 &&
                                       abs(frame.maxY - lastFramePosition.maxY) > 50

        // 更新位置记录
        lastFramePosition = frame

        // 只有当指示器从不可见变为可见时才触发检查
        if visibilityData.isVisible && !isIndicatorVisible {
            isIndicatorVisible = true

            // 如果是布局变化或者正在布局变化过程中，不触发自动加载
            if !(isLayoutChanging || isSignificantLayoutChange) {
                triggerAutoLoadIfNeeded()
            }
        } else if !visibilityData.isVisible && isIndicatorVisible {
            isIndicatorVisible = false
        }
    }

    private func triggerAutoLoadIfNeeded() {
        // 检查是否应该触发自动加载
        let shouldTrigger = !isLoading && !isLoadingMore && !posts.isEmpty && !hasTriggeredAutoLoad && isIndicatorVisible

        if shouldTrigger {
            hasTriggeredAutoLoad = true
            Task {
                await onLoadMore()
            }
        }
    }

    /// 标记布局正在变化，防止误触发自动加载
    private func markLayoutChanging(reason: String) {
        isLayoutChanging = true
        // 延迟重置布局变化状态，给布局足够时间稳定
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            self.isLayoutChanging = false
        }
    }
}


