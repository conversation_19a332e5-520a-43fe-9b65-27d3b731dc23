import SwiftUI
import CoreData
import Foundation



// MARK: - 帖子行视图已统一到UnifiedPostRowView
// OptimizedPostRowView已被UnifiedPostRowView替代


// MARK: - 帖子标签视图组件
struct PostTagsView: View, Equatable {
    let tags: [String]
    let categoryId: Int
    @EnvironmentObject var tagDataLoader: TagDataLoader

    private var selectedCategory: CategoryItem? {
        categories.first { $0.id == categoryId }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            CustomTagsView(
                category: selectedCategory,
                tags: tags
            )
            .equatable()
        }
        .frame(height: TagsLayoutHelper.calculateTagsHeight(
            tags: tags,
            category: selectedCategory,
            tagDataLoader: tagDataLoader
        ))
    }

    // MARK: - Equatable 实现
    static func == (lhs: PostTagsView, rhs: PostTagsView) -> Bool {
        return lhs.tags == rhs.tags &&
               lhs.categoryId == rhs.categoryId
    }
}

struct CustomTagsView: View, Equatable {
    let category: CategoryItem?
    let tags: [String]
    @Environment(\.colorScheme) var colorScheme
    @EnvironmentObject var tagDataLoader: TagDataLoader

    var body: some View {
        VStack(alignment: .leading, spacing: TagsLayoutHelper.verticalSpacing) {
            let rows = TagsLayoutHelper.createTagRows(
                tags: tags,
                category: category,
                tagDataLoader: tagDataLoader
            )
            ForEach(0..<rows.count, id: \.self) { rowIndex in
                HStack(spacing: TagsLayoutHelper.horizontalSpacing) {
                    ForEach(rows[rowIndex], id: \.self) { item in
                        tagView(for: item)
                    }
                    Spacer()
                }
            }
        }
        .padding(.vertical, 2)
    }
    
    private func tagView(for item: TagItem) -> some View {
        Group {
            switch item {
            case .category(let category):
                createTagBaseView(
                    icon: category.icon,
                    text: category.name,
                    textColor: Color.categoryColor(hex: category.color, for: colorScheme),
                    hasBackground: false
                )
            case .tag(let tagName):
                if let tag = tagDataLoader.getTag(by: tagName) {
                    createTagBaseView(
                        icon: tag.icon,
                        text: tag.text,
                        textColor: .secondary,
                        hasBackground: true
                    )
                } else {
                    createTagBaseView(
                        icon: nil,
                        text: tagName,
                        textColor: .secondary,
                        hasBackground: true
                    )
                }
            }
        }
        .fixedSize()
    }
    
    private func createTagBaseView(icon: String?, text: String, textColor: Color, hasBackground: Bool) -> some View {
        HStack(spacing: 4) {
            if let iconName = icon {
                Image(systemName: iconName)
                    .font(.system(size: TagsLayoutHelper.iconWidth))
                    .foregroundColor(textColor)
            }
            Text(text)
                .font(.subheadline)
                .foregroundColor(textColor)
                .lineLimit(1)
                .truncationMode(.tail)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(hasBackground ? (colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1)) : Color.clear)
        .cornerRadius(4)
        .frame(height: TagsLayoutHelper.tagHeight)
        .frame(maxWidth: TagsLayoutHelper.maxTagWidth)
    }
    
    // MARK: - Equatable 实现
    static func == (lhs: CustomTagsView, rhs: CustomTagsView) -> Bool {
        return lhs.tags == rhs.tags &&
               lhs.category?.id == rhs.category?.id &&
               lhs.category?.name == rhs.category?.name
    }
}

// MARK: - 标签布局辅助类
enum TagsLayoutHelper {
    // 布局常量
    static let horizontalSpacing: CGFloat = 8
    static let verticalSpacing: CGFloat = 8
    static let tagHeight: CGFloat = 28
    static let iconWidth: CGFloat = 14.0
    static let horizontalPadding: CGFloat = 16.0
    static let iconTextSpacing: CGFloat = 4.0
    static let verticalPadding: CGFloat = 6.0
    static let maxTagWidth: CGFloat = 150 // 添加最大标签宽度限制
    // 使用更保守的宽度计算，确保在各种布局中都能正常工作
    static let containerWidth: CGFloat = UIScreen.main.bounds.width - 100 // 更保守的宽度计算：头像(50) + 间距(50)
    
    @MainActor
    static func createTagRows(tags: [String], category: CategoryItem?, tagDataLoader: TagDataLoader) -> [[TagItem]] {
        let allItems = createAllTagItems(tags: tags, category: category)
        var rows: [[TagItem]] = [[]]
        var currentRowWidth: CGFloat = 0

        for item in allItems {
            let itemWidth = getItemWidth(item, tagDataLoader: tagDataLoader)
            let needsNewRow = !rows.last!.isEmpty && currentRowWidth + horizontalSpacing + itemWidth > containerWidth

            if needsNewRow {
                rows.append([item])
                currentRowWidth = itemWidth
            } else {
                rows[rows.count-1].append(item)
                currentRowWidth += rows.last!.count == 1 ? itemWidth : horizontalSpacing + itemWidth
            }
        }

        return rows.filter { !$0.isEmpty }
    }
    
    static func createAllTagItems(tags: [String], category: CategoryItem?) -> [TagItem] {
        var allItems: [TagItem] = []
        if let cat = category {
            allItems.append(.category(cat))
        }
        allItems.append(contentsOf: tags.map { TagItem.tag($0) })
        return allItems
    }
    
    @MainActor
    static func calculateTagsHeight(tags: [String], category: CategoryItem?, tagDataLoader: TagDataLoader) -> CGFloat {
        let rowCount = CGFloat(createTagRows(tags: tags, category: category, tagDataLoader: tagDataLoader).count)
        guard rowCount > 0 else { return 0 }
        return rowCount * tagHeight + max(0, rowCount - 1) * verticalSpacing + verticalPadding
    }
    
    @MainActor
    static func getItemWidth(_ item: TagItem, tagDataLoader: TagDataLoader) -> CGFloat {
        switch item {
        case .category(let cat):
            return estimateCategoryWidth(for: cat)
        case .tag(let tagName):
            return estimateTagWidth(for: tagName, tagDataLoader: tagDataLoader)
        }
    }
    
    static func estimateCategoryWidth(for category: CategoryItem) -> CGFloat {
        let text = category.name
        let font = UIFont.preferredFont(forTextStyle: .subheadline)
        let textAttributes = [NSAttributedString.Key.font: font]
        let textSize = (text as NSString).size(withAttributes: textAttributes)
        
        let calculatedWidth = textSize.width + iconWidth + iconTextSpacing + horizontalPadding
        return min(calculatedWidth, maxTagWidth) // 应用最大宽度限制
    }
    
    @MainActor
    static func estimateTagWidth(for tagName: String, tagDataLoader: TagDataLoader) -> CGFloat {
        let text = tagDataLoader.getTag(by: tagName)?.text ?? tagName
        let font = UIFont.preferredFont(forTextStyle: .subheadline)
        let textAttributes = [NSAttributedString.Key.font: font]
        let textSize = (text as NSString).size(withAttributes: textAttributes)
        
        let hasIcon = tagDataLoader.getTag(by: tagName)?.icon != nil
        let iconWidth: CGFloat = hasIcon ? Self.iconWidth : 0.0
        let iconTextSpacing: CGFloat = hasIcon ? Self.iconTextSpacing : 0.0
        
        let calculatedWidth = textSize.width + iconWidth + iconTextSpacing + horizontalPadding
        return min(calculatedWidth, maxTagWidth) // 应用最大宽度限制
    }
}

// MARK: - 新数据横幅组件
struct NewDataBannerView: View {
    @Environment(\.colorScheme) var colorScheme

    private var bannerColor: Color {
        colorScheme == .dark ? Color.blue.opacity(0.8) : Color.blue
    }

    private var backgroundColor: Color {
        colorScheme == .dark ? Color.blue.opacity(0.15) : Color.blue.opacity(0.1)
    }

    private var lineColor: Color {
        colorScheme == .dark ? Color.blue.opacity(0.4) : Color.blue.opacity(0.3)
    }

    var body: some View {
        HStack {
            // 左侧分割线
            Rectangle()
                .fill(lineColor)
                .frame(height: 1)

            // 中央标签
            HStack(spacing: 6) {
                Image(systemName: "sparkles")
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(bannerColor)

                Text("新帖子")
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(bannerColor)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(lineColor, lineWidth: 1)
                    )
            )
            .shadow(color: bannerColor.opacity(0.2), radius: 2, x: 0, y: 1)

            // 右侧分割线
            Rectangle()
                .fill(lineColor)
                .frame(height: 1)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(UIColor.systemBackground))
        .transition(.opacity.combined(with: .scale(scale: 0.95)))
    }
}

// MARK: - 帖子列表视图
struct FeedPostListView: View {
    let posts: [Post]
    let showPinnedPosts: Bool
    let lastPostIdBeforeLoadMore: Int64?
    let hasPerformedRefreshOrLoadMore: Bool
    let onScrollToBanner: () -> Void

    // 缓存横幅分析结果，避免重复计算
    @State private var cachedBannerAnalysis: (oldPosts: [Post], newPosts: [Post], shouldShow: Bool) = ([], [], false)
    @State private var lastAnalyzedPostsCount: Int = 0
    @State private var lastAnalyzedLastPostId: Int64? = nil
    @State private var lastAnalyzedRefreshFlag: Bool = false

    // 计算属性替代缓存，确保实时响应
    private var filteredPosts: [Post] {
        return showPinnedPosts ? Array(posts) : posts.filter { !$0.isPined }
    }

    private var pinnedPosts: [Post] {
        return posts.filter { $0.isPined }
    }

    private var normalPosts: [Post] {
        return posts.filter { !$0.isPined }
    }

    var body: some View {
        LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
            if !posts.isEmpty {
                postsContentView
            }
        }
        .onAppear {
            print("【调试】FeedPostListView.onAppear: posts.count=\(posts.count)")
            print("【调试】FeedPostListView.onAppear: showPinnedPosts=\(showPinnedPosts)")
            print("【调试】FeedPostListView.onAppear: pinnedPosts.count=\(pinnedPosts.count)")
            // 初始化时更新缓存
            updateBannerAnalysisCacheIfNeeded()
        }
        .onChange(of: posts) { newPosts in
            print("【调试】FeedPostListView.onChange(posts): newPosts.count=\(newPosts.count)")
            print("【调试】FeedPostListView.onChange(posts): 前几个帖子ID: \(newPosts.prefix(3).map { $0.id })")
            // 帖子变化时更新缓存，使用传入的newPosts参数
            updateBannerAnalysisCacheIfNeeded(with: newPosts)
        }
        .onChange(of: showPinnedPosts) { newValue in
            print("【调试】FeedPostListView.onChange(showPinnedPosts): \(newValue)")
            print("【调试】FeedPostListView.onChange(showPinnedPosts): 更新后 pinnedPosts.count=\(pinnedPosts.count)")
            // showPinnedPosts变化会影响normalPosts，需要更新缓存
            updateBannerAnalysisCacheIfNeeded()
        }
        .onChange(of: lastPostIdBeforeLoadMore) { newValue in
            // lastPostIdBeforeLoadMore变化时更新缓存
            print("【调试】FeedPostListView.onChange(lastPostIdBeforeLoadMore): \(newValue?.description ?? "nil")")
            // 🆕 使用传入的newValue而不是读取属性，避免SwiftUI更新时机问题
            updateBannerAnalysisCacheIfNeeded(withLastPostId: newValue)
        }
        .onChange(of: hasPerformedRefreshOrLoadMore) { _ in
            // hasPerformedRefreshOrLoadMore变化时更新缓存
            updateBannerAnalysisCacheIfNeeded()
        }
    }

    // MARK: - 子视图构建器
    @ViewBuilder
    private var postsContentView: some View {
        // 显示置顶帖子
        pinnedPostsView

        // 显示普通帖子（包含横幅分割）
        normalPostsView
    }

    @ViewBuilder
    private var pinnedPostsView: some View {
        ForEach(pinnedPosts, id: \.id) { post in
            postRowView(
                post: post,
                displayIndex: (pinnedPosts.firstIndex(of: post) ?? 0) + 1
            )
        }
    }

    @ViewBuilder
    private var normalPostsView: some View {
        // 🆕 简化逻辑：始终显示所有普通帖子，横幅作为可选项
        let allNormalPosts = normalPosts
        let shouldShowBanner = cachedBannerAnalysis.shouldShow
        let oldPosts = cachedBannerAnalysis.oldPosts
        let newPosts = cachedBannerAnalysis.newPosts

        if shouldShowBanner && !newPosts.isEmpty {
            // 有横幅时：显示分割的帖子
            ForEach(oldPosts, id: \.id) { post in
                postRowView(
                    post: post,
                    displayIndex: pinnedPosts.count + (oldPosts.firstIndex(of: post) ?? 0) + 1
                )
            }

            bannerView

            ForEach(newPosts, id: \.id) { post in
                postRowView(
                    post: post,
                    displayIndex: pinnedPosts.count + oldPosts.count + (newPosts.firstIndex(of: post) ?? 0) + 1
                )
            }
        } else {
            // 无横幅时：显示所有普通帖子
            ForEach(allNormalPosts, id: \.id) { post in
                postRowView(
                    post: post,
                    displayIndex: pinnedPosts.count + (allNormalPosts.firstIndex(of: post) ?? 0) + 1
                )
            }
        }
    }

    @ViewBuilder
    private var bannerView: some View {
        NewDataBannerView()
            .id("new-data-banner")
            .drawingGroup() // 性能优化：光栅化复杂视图
            .transition(.asymmetric(
                insertion: .opacity.combined(with: .scale(scale: 0.95)),
                removal: .opacity.combined(with: .scale(scale: 0.95))
            ))
            .animation(.interactiveSpring(response: 0.4, dampingFraction: 0.8), value: true)
            .onAppear {
                print("【调试】FeedPostListView: 🎉 横幅视图出现")
            }
    }


    /// 更新横幅分析缓存（仅在必要时）
    private func updateBannerAnalysisCacheIfNeeded(with newPosts: [Post]? = nil, withLastPostId overrideLastPostId: Int64? = nil) {
        // 🆕 确定要分析的帖子 - 始终使用完整的帖子列表进行分析
        let postsToAnalyze: [Post]
        if let newPosts = newPosts {
            // 如果传入了新帖子列表，使用过滤后的新帖子
            postsToAnalyze = newPosts.filter { !$0.isPined }
        } else {
            // 如果没有传入新帖子，使用完整的帖子列表（而不是normalPosts）
            postsToAnalyze = posts.filter { !$0.isPined }
        }
        let currentPostsCount = postsToAnalyze.count

        // 🆕 优先使用传入的lastPostId，避免SwiftUI更新时机问题
        let currentLastPostId = overrideLastPostId ?? lastPostIdBeforeLoadMore
        let currentRefreshFlag = hasPerformedRefreshOrLoadMore

        print("【调试】FeedPostListView.updateBannerAnalysisCacheIfNeeded: currentLastPostId=\(currentLastPostId?.description ?? "nil"), overrideLastPostId=\(overrideLastPostId?.description ?? "nil")")

        // 检查是否需要重新计算
        let needsUpdate = currentPostsCount != lastAnalyzedPostsCount ||
                         currentLastPostId != lastAnalyzedLastPostId ||
                         currentRefreshFlag != lastAnalyzedRefreshFlag

        if needsUpdate {
            // 重新计算横幅分析，传入正确的lastPostId
            let analysis = analyzePostsForBanner(postsToAnalyze, withLastPostId: currentLastPostId)
            cachedBannerAnalysis = (oldPosts: analysis.0, newPosts: analysis.1, shouldShow: analysis.2)

            // 🆕 添加调试信息
            print("【调试】FeedPostListView: 横幅分析更新 - shouldShow=\(analysis.2), oldPosts=\(analysis.0.count), newPosts=\(analysis.1.count)")

            // 更新缓存状态
            lastAnalyzedPostsCount = currentPostsCount
            lastAnalyzedLastPostId = currentLastPostId
            lastAnalyzedRefreshFlag = currentRefreshFlag
        }
    }

    /// 分析帖子列表，找出新旧数据的分界点
    private func analyzePostsForBanner(_ posts: [Post], withLastPostId overrideLastPostId: Int64? = nil) -> ([Post], [Post], Bool) {
        // 🆕 优先使用传入的lastPostId，避免SwiftUI更新时机问题
        let lastIdBeforeLoadMore = overrideLastPostId ?? lastPostIdBeforeLoadMore

        // 基础条件检查
        guard posts.count > 1,
              hasPerformedRefreshOrLoadMore,
              let lastIdBeforeLoadMore = lastIdBeforeLoadMore else {
            print("【调试】FeedPostListView: 横幅分析基础条件不满足 - posts.count=\(posts.count), hasPerformedRefreshOrLoadMore=\(hasPerformedRefreshOrLoadMore), lastIdBeforeLoadMore=\(lastIdBeforeLoadMore?.description ?? "nil"), overrideLastPostId=\(overrideLastPostId?.description ?? "nil")")
            return (posts, [], false)
        }

        print("【调试】FeedPostListView: 开始横幅分析 - lastIdBeforeLoadMore=\(lastIdBeforeLoadMore), posts.count=\(posts.count)")

        // 排序帖子（与FetchRequest保持一致）
        let sortedPosts = posts.sorted { post1, post2 in
            let time1 = post1.last_fetched ?? Date.distantPast
            let time2 = post2.last_fetched ?? Date.distantPast
            return time1 == time2 ? post1.id > post2.id : time1 < time2
        }

        print("【调试】FeedPostListView: 排序后前10个帖子ID: \(sortedPosts.prefix(10).map { $0.id })")
        print("【调试】FeedPostListView: 排序后后10个帖子ID: \(sortedPosts.suffix(10).map { $0.id })")

        // 找到分界点
        guard let splitIndex = sortedPosts.firstIndex(where: { $0.id == lastIdBeforeLoadMore }) else {
            print("【调试】FeedPostListView: ❌ 找不到分界点 - lastIdBeforeLoadMore=\(lastIdBeforeLoadMore)")
            print("【调试】FeedPostListView: 所有帖子ID: \(sortedPosts.map { $0.id })")
            return (sortedPosts, [], false)
        }

        // 分割数据
        let oldPosts = Array(sortedPosts[0...splitIndex])
        let newPosts = Array(sortedPosts[(splitIndex+1)...])

        print("【调试】FeedPostListView: 找到分界点 splitIndex=\(splitIndex), sortedPosts.count=\(sortedPosts.count)")
        print("【调试】FeedPostListView: oldPosts.count=\(oldPosts.count), newPosts.count=\(newPosts.count)")
        print("【调试】FeedPostListView: 分界点帖子ID=\(sortedPosts[splitIndex].id)")
        print("【调试】FeedPostListView: 分界点帖子时间=\(sortedPosts[splitIndex].last_fetched?.description ?? "nil")")
        if !newPosts.isEmpty {
            print("【调试】FeedPostListView: 新帖子ID: \(newPosts.map { $0.id })")
            print("【调试】FeedPostListView: 新帖子时间: \(newPosts.map { $0.last_fetched?.description ?? "nil" })")
        } else {
            print("【调试】FeedPostListView: ❌ 没有新帖子 - 可能所有帖子都在分界点之前")
        }

        return (oldPosts, newPosts, !newPosts.isEmpty)
    }

    @ViewBuilder
    private func postRowView(post: Post, displayIndex: Int) -> some View {
        UnifiedPostRowView(
            item: post,
            displayIndex: displayIndex
        )
        .transition(.asymmetric(
            insertion: .opacity.combined(with: .move(edge: .bottom)), // 新帖子从底部滑入
            removal: .opacity.combined(with: .move(edge: .leading))
        ))
    }

    
}
