import SwiftUI
import Combine



// MARK: - 搜索排序方式
enum SearchSortOrder: String, CaseIterable, Identifiable {
    case relevance = "相关性"
    case latest = "最新"
    case likes = "赞最多"
    case views = "浏览最多"
    case latest_topic = "最新话题"

    var id: String { self.rawValue }

    var queryValue: String? {
        switch self {
        case .relevance:
            return nil // "相关性"是默认值，不需要查询参数
        case .latest:
            return "latest"
        case .likes:
            return "likes"
        case .views:
            return "views"
        case .latest_topic:
            return "latest_topic"
        }
    }
}

// MARK: - 搜索结果数据模型
struct SearchResult: Codable {
    let posts: [SearchPost]
    let topics: [SearchTopic]
}

struct SearchPost: Codable, Identifiable {
    let id: Int
    let name: String?
    let username: String
    let avatar_template: String
    let created_at: String
    let blurb: String
    let topic_id: Int
    let last_posted_at: String?
}

struct SearchTopic: Codable, Identifiable {
    let id: Int
    let title: String
    let fancy_title: String
    let posts_count: Int
    let tags: [String]?
    let category_id: Int?
    let created_at: String
    let last_posted_at: String?
}

struct DisplayableResult: Identifiable {
    let topic: SearchTopic
    let post: SearchPost
    var id: Int { topic.id }
}

// MARK: - SearchViewModel
@MainActor
class SearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchResults: [DisplayableResult] = []
    @Published var isLoading = false
    @Published var searchPerformed = false
    @Published var errorMessage: String?
    @Published var sortOrder: SearchSortOrder = .relevance {
        didSet {
            if !searchText.trimmingCharacters(in: .whitespaces).isEmpty {
                performSearch()
            }
        }
    }

    private let postService: PostService

    init(postService: PostService) {
        self.postService = postService
    }

    func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespaces).isEmpty else { return }
        
        isLoading = true
        searchPerformed = true
        searchResults = []
        errorMessage = nil
        
        Task {
            do {
                let results = try await postService.fetchSearchResults(for: searchText, sortOrder: sortOrder)
                let displayableResults = mapResults(posts: results.posts, topics: results.topics)
                
                self.searchResults = displayableResults
                self.isLoading = false
            } catch {
                self.errorMessage = "搜索失败: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }

    private func mapResults(posts: [SearchPost], topics: [SearchTopic]) -> [DisplayableResult] {
        var displayableResults: [DisplayableResult] = []
        let topicMap = Dictionary(uniqueKeysWithValues: topics.map { ($0.id, $0) })
        
        for post in posts {
            if let topic = topicMap[post.topic_id] {
                displayableResults.append(DisplayableResult(topic: topic, post: post))
            }
        }
        
        return displayableResults
    }
}

// MARK: - 搜索视图
struct SearchView: View {
    @EnvironmentObject var viewModel: SearchViewModel
    @EnvironmentObject var postService: PostServiceImpl
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack {
            HStack {
                searchBar
                sortMenu
            }
            .padding()

            if viewModel.isLoading {
                ProgressView("正在搜索...")
            } else if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .padding()
            } else {
                if viewModel.searchResults.isEmpty && viewModel.searchPerformed {
                    Text("没有找到结果，请换个关键词试试。")
                        .foregroundColor(.gray)
                } else {
                    List(Array(viewModel.searchResults.enumerated()), id: \.element.id) { index, result in
                        UnifiedPostRowView(
                            item: result,
                            displayIndex: index + 1
                        )
                        .listRowInsets(EdgeInsets())
                        .listRowSeparator(.hidden)
                    }
                    .listStyle(PlainListStyle())
                }
            }
            Spacer()
        }
        .navigationTitle("搜索")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
            }
        }
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)

            TextField("输入搜索内容...", text: $viewModel.searchText)
                .onSubmit(viewModel.performSearch)

            if !viewModel.searchText.isEmpty {
                Button(action: {
                    viewModel.searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12))
        .background(colorScheme == .dark ? Color.black : Color(UIColor.systemGray6))
        .cornerRadius(10)
    }

    private var sortMenu: some View {
        Menu {
            Picker("排序方式", selection: $viewModel.sortOrder) {
                ForEach(SearchSortOrder.allCases) { order in
                    Text(order.rawValue).tag(order)
                }
            }
        } label: {
            Image(systemName: "arrow.up.arrow.down.circle")
                .font(.system(size: 22))
                .foregroundColor(.gray)
        }
    }
}

// MARK: - 搜索结果行视图已统一到UnifiedPostRowView
// SearchResultRowView已被UnifiedPostRowView替代
