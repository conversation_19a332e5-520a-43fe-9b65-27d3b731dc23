import SwiftUI
import CoreData

/// 优化的帖子列表视图 - 使用虚拟滚动技术
struct OptimizedFeedListView: View {
    // 保持与原有FeedPostListView相同的API
    let posts: [Post]
    let showPinnedPosts: Bool
    let lastPostIdBeforeLoadMore: Int64?
    let hasPerformedRefreshOrLoadMore: Bool
    let onScrollToBanner: () -> Void
    
    // 内部状态
    @State private var visibleRowRange: Range<Int> = 0..<0
    @State private var estimatedRowHeight: CGFloat = 150 // 估计的行高，可以根据实际情况调整
    @State private var containerHeight: CGFloat = 0
    @State private var isFastScrolling: Bool = false
    @State private var scrollPosition: CGPoint = .zero
    
    // 可见区域的额外缓冲区（前后各预加载多少个项目）
    private let bufferItemCount = 5
    
    // 从原有FeedPostListView复制的计算属性
    private var pinnedPosts: [Post] {
        return posts.filter { $0.isPined }
    }
    
    private var normalPosts: [Post] {
        return posts.filter { !$0.isPined }
    }
    
    // 计算真正需要显示的帖子
    private var visiblePosts: [Post] {
        let allPosts = showPinnedPosts ? posts : normalPosts
        
        // 如果帖子数量较少，直接显示全部
        if allPosts.count < 30 {
            return allPosts
        }
        
        // 否则只显示可见区域的帖子
        guard visibleRowRange.lowerBound < allPosts.count else {
            return []
        }
        
        let upperBound = min(visibleRowRange.upperBound, allPosts.count)
        return Array(allPosts[visibleRowRange.lowerBound..<upperBound])
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 使用GeometryReader获取容器大小
            GeometryReader { proxy in
                ScrollViewReader { scrollProxy in
                    ScrollView {
                        // 用来测量滚动位置的透明视图
                        GeometryReader { scrollGeometry in
                            Color.clear
                                .preference(key: ScrollOffsetPreferenceKey.self, 
                                            value: scrollGeometry.frame(in: .named("scrollView")).origin)
                        }
                        .frame(height: 0)
                        
                        // 虚拟滚动的关键：显示的是所有帖子的"骨架"，但只渲染可见区域的内容
                        LazyVStack(spacing: 0, pinnedViews: []) {
                            if !posts.isEmpty {
                                // 显示置顶帖子（置顶帖子很少，可以全部显示）
                                ForEach(pinnedPosts, id: \.id) { post in
                                    PostRowContainer(
                                        post: post,
                                        displayIndex: (pinnedPosts.firstIndex(of: post) ?? 0) + 1,
                                        isVisible: true // 置顶帖子总是可见
                                    )
                                    .id(post.id)
                                }
                                
                                // 为所有普通帖子创建容器，但只渲染可见的
                                ForEach(normalPosts, id: \.id) { post in
                                    let index = normalPosts.firstIndex(of: post) ?? 0
                                    let displayIndex = pinnedPosts.count + index + 1
                                    let isVisible = isPostVisible(post, index: index)
                                    
                                    PostRowContainer(
                                        post: post,
                                        displayIndex: displayIndex,
                                        isVisible: isVisible
                                    )
                                    .id(post.id)
                                }
                            }
                        }
                        .onAppear {
                            // 初始化容器高度
                            containerHeight = proxy.size.height
                            // 初始化可见区域
                            updateVisibleRange(containerHeight: proxy.size.height, scrollPosition: CGPoint.zero)
                        }
                    }
                    .coordinateSpace(name: "scrollView")
                    .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
                        // 更新滚动位置
                        scrollPosition = offset
                        
                        // 只在显著滚动时更新可见范围，减少不必要的状态更新
                        let significantScroll = abs(offset.y - scrollPosition.y) > 10
                        if significantScroll {
                            updateVisibleRange(containerHeight: containerHeight, scrollPosition: offset)
                        }
                    }
                }
            }
        }
    }
    
    // 根据当前滚动位置更新可见行范围
    private func updateVisibleRange(containerHeight: CGFloat, scrollPosition: CGPoint) {
        // 计算可见区域的开始和结束索引
        let scrollY = max(0, scrollPosition.y)
        let startIndex = max(0, Int(scrollY / estimatedRowHeight) - bufferItemCount)
        let visibleCount = Int(containerHeight / estimatedRowHeight) + bufferItemCount * 2
        let endIndex = min(normalPosts.count, startIndex + visibleCount)
        
        // 只有当范围真正变化时才更新状态，减少重绘
        let newRange = startIndex..<endIndex
        if newRange != visibleRowRange {
            visibleRowRange = newRange
        }
    }
    
    // 判断帖子是否在可见范围内
    private func isPostVisible(_ post: Post, index: Int) -> Bool {
        return visibleRowRange.contains(index)
    }
}

// 用于传递滚动位置的PreferenceKey
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGPoint = .zero
    
    static func reduce(value: inout CGPoint, nextValue: () -> CGPoint) {
        value = nextValue()
    }
}

/// 帖子行容器 - 根据可见性条件渲染真实内容或占位符
struct PostRowContainer: View {
    let post: Post
    let displayIndex: Int
    let isVisible: Bool
    
    // 用于提高性能的缓存机制
    @State private var cachedHeight: CGFloat? = nil
    
    var body: some View {
        Group {
            if isVisible {
                // 可见时显示真实内容
                UnifiedPostRowView(item: post, displayIndex: displayIndex)
                    .background(GeometryReader { geometry in
                        Color.clear.onAppear {
                            // 缓存行高以便更准确地计算
                            if cachedHeight == nil {
                                cachedHeight = geometry.size.height
                            }
                        }
                    })
            } else {
                // 不可见时显示占位符，保持相同高度
                postPlaceholder
                    .frame(height: cachedHeight)
            }
        }
    }
    
    // 帖子占位符视图 - 非常轻量级
    private var postPlaceholder: some View {
        Rectangle()
            .fill(Color.clear)
            .frame(height: cachedHeight ?? 150) // 使用缓存的高度或默认值
    }
}
