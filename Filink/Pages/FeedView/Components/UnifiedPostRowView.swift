import SwiftUI
import CoreData

// MARK: - 统一的帖子行视图
struct UnifiedPostRowView: View, Equatable {
    // 实现Equatable协议以减少不必要的重绘
    static func == (lhs: UnifiedPostRowView, rhs: UnifiedPostRowView) -> Bool {
        return lhs.item.id == rhs.item.id && 
               lhs.displayIndex == rhs.displayIndex &&
               lhs.item.displayIsRead == rhs.item.displayIsRead
    }
    let item: PostDisplayable
    let displayIndex: Int
    @Environment(\.colorScheme) var colorScheme
    @State private var isNavigationActive = false
    
    @EnvironmentObject var postService: PostServiceImpl
    @Environment(\.managedObjectContext) private var viewContext
    
    // 缓存格式化的时间字符串
    private var formattedLastPostedTime: String? {
        item.lastPostedAt?.timeAgoDisplay() // 最后回复时间（显示在名称右侧）
    }

    private var formattedCreatedTime: String? {
        item.createdAt?.timeAgoDisplay() // 创建时间（显示在右下角）
    }

    var body: some View {
        VStack(spacing: 0) {
            NavigationLink(
                destination: PostDetailView(
                    postId: "\(item.displayId)",
                    postService: postService,
                    viewContext: viewContext
                ),
                isActive: $isNavigationActive
            ) {
                EmptyView()
            }
            .hidden()

            ZStack(alignment: .topLeading) {
                HStack(alignment: .top, spacing: 12) {
                    // 统一的头像处理 - 固定宽度
                    PostAvatarView(avatarInfo: item.avatarInfo)
                        .frame(width: 50, height: 50)
                        .equatable() // 明确使用Equatable接口
                        .drawingGroup() // 光栅化头像视图

                    // 右侧内容 - 占用剩余宽度
                    VStack(alignment: .leading, spacing: 4) {
                        // 用户名和时间信息行
                        HStack {
                            Text(item.displayName)
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .foregroundColor(Color.blue)
                                .lineLimit(1)

                            if let formattedTime = formattedLastPostedTime {
                                HStack(spacing: 4) {
                                    Text(formattedTime)
                                        .font(.system(size: 14))
                                    // Feed显示Post ID，搜索结果显示Topic ID
                                    Text("ID:\(item.displayId)")
                                        .font(.system(size: 14))
                                        .foregroundColor(.orange)
                                        .fontWeight(.medium)
                                }
                                .font(.footnote)
                                .foregroundColor(.gray)
                            }

                            Spacer()

                            if item.displayIsPined {
                                Image(systemName: "pin.fill")
                                    .foregroundColor(.red)
                                    .font(.system(size: 14))
                            }
                        }

                        // 标题 - 确保固定宽度约束
                        Text(item.title ?? "无标题")
                            .font(.customFont(size: 16, weight: .medium, for: .feed))
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            // .foregroundColor(item.displayIsRead ? .gray : .primary)

                        // 摘要
                        if let excerpt = item.excerpt, !excerpt.isEmpty {
                            Text(excerpt)
                                .font(.system(size: 14))
                                .lineLimit(3)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }

            // 标签 - 减少垂直间距 - 性能优化：添加drawingGroup和equatable
                        if let tags = item.displayTags, !tags.isEmpty {
                            PostTagsView(
                                tags: tags,
                                categoryId: item.categoryId ?? 0
                            )
                            .padding(.top, 2)
                            .frame(maxWidth: .infinity, alignment: .leading) // 限制最大宽度为父视图宽度
                            .drawingGroup() // 性能优化：将标签视图光栅化处理
                            .equatable() // 明确声明使用Equatable比较
                        }

                        // 底部图标信息 - 减少顶部间距
                        HStack {
                            if let postsCount = item.postsCount {
                                HStack(spacing: 3) {
                                    Image(systemName: "bubble.left.and.bubble.right")
                                        .font(.system(size: 14))
                                        .frame(width: 22, alignment: .leading)
                                    Text(formattedPostsCount)
                                        .font(.system(size: 14))
                                        .frame(width: 50, alignment: .leading)
                                }
                            }

                            if item.postsCount != nil && item.viewsCount != nil {
                                Spacer().frame(width: 15)
                            }

                            if let viewsCount = item.viewsCount {
                                HStack(spacing: 3) {
                                    Image(systemName: "eyes")
                                        .font(.system(size: 14))
                                        .frame(width: 20, alignment: .center)
                                    Text(formattedViewsCount)
                                        .font(.system(size: 14))
                                        .frame(width: 50, alignment: .leading)
                                }
                            }

                            Spacer()

                            if let createdTime = formattedCreatedTime {
                                HStack(spacing: 3) {
                                    Image(systemName: "clock")
                                        .font(.system(size: 14))
                                        .frame(width: 18, alignment: .leading)
                                    Text(createdTime)
                                        .font(.system(size: 14))
                                    Text("[\(displayIndex)]")
                                        .font(.system(size: 14))
                                        .foregroundColor(.orange)
                                }
                            }
                        }
                        .foregroundColor(.gray)
                        .padding(.top, 4)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 8)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                // 标记为已读（仅对Feed中的帖子）
                if let post = item as? Post {
                    post.isRead = true
                    try? viewContext.save()
                }
                isNavigationActive = true
            }

            Divider()
        }
        .background(colorScheme == .dark ? Color(red: 19/255.0, green: 19/255.0, blue: 19/255.0) : Color(UIColor.systemBackground))
    }

    /// 格式化数值显示（1000 -> 1.0K）
    private func formatCount(_ count: Int) -> String {
        if count >= 1000 {
            return String(format: "%.1fK", Double(count) / 1000.0)
        } else {
            return "\(count)"
        }
    }
    
    // 使用缓存的格式化函数，避免重复计算
    private var formattedPostsCount: String {
        if let count = item.postsCount {
            return formatCount(count)
        }
        return ""
    }
    
    private var formattedViewsCount: String {
        if let count = item.viewsCount {
            return formatCount(count)
        }
        return ""
    }
}

// MARK: - 统一的头像视图
struct PostAvatarView: View, Equatable {
    let avatarInfo: PostAvatarInfo
    @EnvironmentObject var settingsManager: SettingsManager
    
    // 实现Equatable以避免不必要的重绘
    static func == (lhs: PostAvatarView, rhs: PostAvatarView) -> Bool {
        return lhs.avatarInfo == rhs.avatarInfo
    }

    var body: some View {
        switch avatarInfo {
        case .local(let username, let identifier):
            SimpleUserAvatarView.medium(
                username: username,
                avatarURL: buildAvatarURL(fromIdentifier: identifier, username: username)
            )
        case .remote(let username, let template):
            SimpleUserAvatarView.medium(
                username: username,
                avatarURL: buildAvatarURL(fromTemplate: template)
            )
        case .none(let username):
            SimpleUserAvatarView.medium(username: username)
        }
    }

    // 构建头像URL
    private func buildAvatarURL(fromTemplate template: String) -> String? {
        guard !template.isEmpty, let baseURL = URL(string: settingsManager.website) else { return nil }
        let processedTemplate = template.replacingOccurrences(of: "{size}", with: "72")
        return baseURL.appendingPathComponent(processedTemplate).absoluteString
    }

    private func buildAvatarURL(fromIdentifier identifier: String, username: String) -> String? {
        guard !identifier.isEmpty else { return nil }

        if identifier.hasPrefix("/") {
            return buildAvatarURL(fromTemplate: identifier)
        } else {
            return buildAvatarURL(forUsername: username, avatarFilename: identifier)
        }
    }

    private func buildAvatarURL(forUsername username: String, avatarFilename: String) -> String? {
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        let domain = settingsManager.website.replacingOccurrences(of: "https://", with: "").replacingOccurrences(of: "http://", with: "")
        let avatarPath = "/user_avatar/\(domain)/\(username)/48/\(avatarFilename).png"
        return baseURL.appendingPathComponent(avatarPath).absoluteString
    }
}
