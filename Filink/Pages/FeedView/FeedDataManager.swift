import SwiftUI
import CoreData
import Combine

/// Feed数据管理器
/// 负责协调PostService和Core Data操作，将数据层逻辑从视图中分离
@MainActor
class FeedDataManager: ObservableObject {
    
    // MARK: - 依赖注入
    private let postService: PostServiceImpl
    private let viewContext: NSManagedObjectContext
    
    // MARK: - 发布状态
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var errorMessage: String? = nil
    @Published var currentPage: Int = 0

    // MARK: - 横幅分界状态
    @Published var lastPostIdBeforeLoadMore: Int64? = nil
    @Published var hasPerformedRefreshOrLoadMore: Bool = false
    
    // MARK: - 私有状态
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(postService: PostServiceImpl, viewContext: NSManagedObjectContext) {
        self.postService = postService
        self.viewContext = viewContext
        
        // 订阅PostService的状态变化
        postService.loadingPublisher
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        postService.errorMessagePublisher
            .receive(on: DispatchQueue.main)
            .assign(to: \.errorMessage, on: self)
            .store(in: &cancellables)
        
        print("【调试】FeedDataManager 已初始化")
    }
    
    // MARK: - 数据刷新
    /// 处理数据刷新
    /// - Parameters:
    ///   - source: 刷新来源描述
    ///   - maintainVisualContinuity: 是否保持视觉连续性
    ///   - feedMode: 当前Feed模式
    ///   - selectedCategoryId: 选中的分类ID
    ///   - categories: 分类列表
    func handleRefresh(
        source: String,
        maintainVisualContinuity: Bool = true,
        feedMode: FeedMode,
        selectedCategoryId: Int,
        categories: [CategoryItem]
    ) async {
        print("【调试】FeedDataManager: \(source)触发，正在获取最新数据（第一页）...")
        
        // 标记开始刷新
        isLoading = true
        
        // 重置页码
        currentPage = 0

        // 重置加载更多状态
        isLoadingMore = false

        // 重置横幅分界ID（刷新时清空分界点）
        lastPostIdBeforeLoadMore = nil
        // 标记已执行刷新操作
        hasPerformedRefreshOrLoadMore = true
        print("【调试】FeedDataManager: 🔄 刷新时重置横幅分界ID，标记已执行刷新操作")
        
        // 如果不需要保持视觉连续性，先将现有帖子的isActiveInHome设置为false
        if !maintainVisualContinuity {
            await deactivateCurrentPosts()
        }
        
        // 根据当前模式调用相应的API
        print("【调试】FeedDataManager: 准备调用API - feedMode=\(feedMode), selectedCategoryId=\(selectedCategoryId)")
        await fetchPostsForCurrentMode(
            isLoadingMore: false,
            preserveCurrentItems: maintainVisualContinuity,
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: categories
        )

        // 标记刷新完成
        isLoading = false

        print("【调试】FeedDataManager: \(source)数据获取完成")
    }
    
    // MARK: - 加载更多
    /// 加载更多帖子
    /// - Parameters:
    ///   - feedMode: 当前Feed模式
    ///   - selectedCategoryId: 选中的分类ID
    ///   - categories: 分类列表
    func loadMorePosts(
        feedMode: FeedMode,
        selectedCategoryId: Int,
        categories: [CategoryItem]
    ) async {
        guard !isLoadingMore && !isLoading else {
            print("【调试】FeedDataManager: 已经在加载中，忽略此次加载请求")
            return
        }

        // 标记开始加载更多
        isLoadingMore = true
        // 使用 defer 确保无论函数如何退出（成功、失败或抛出异常），isLoadingMore 都会被重置
        defer {
            isLoadingMore = false
            print("【调试】FeedDataManager: isLoadingMore 已通过 defer 重置为 false")
        }

        // 🆕 记录加载前的最后一个帖子ID，用于横幅分界
        await recordLastPostIdBeforeLoadMore()

        // 🆕 添加调试信息确认记录成功
        print("【调试】FeedDataManager: 记录完成后 lastPostIdBeforeLoadMore=\(lastPostIdBeforeLoadMore?.description ?? "nil")")

        // 标记已执行加载更多操作
        hasPerformedRefreshOrLoadMore = true

        print("【调试】FeedDataManager: 开始加载更多帖子（数据将追加到末尾，不影响现有帖子位置）...")
        print("【调试】FeedDataManager: 当前页码: \(currentPage)，标记已执行加载更多操作")

        // 根据当前状态选择加载方式
        // 注意：加载更多时 preserveCurrentItems=true 且 isLoadingMore=true
        // 这确保了现有帖子位置不变，新数据追加到末尾
        print("【调试】FeedDataManager: 调用 fetchPostsForCurrentMode 前 lastPostIdBeforeLoadMore=\(lastPostIdBeforeLoadMore?.description ?? "nil")")

        await fetchPostsForCurrentMode(
            isLoadingMore: true,
            preserveCurrentItems: true,
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: categories
        )

        print("【调试】FeedDataManager: 调用 fetchPostsForCurrentMode 后 lastPostIdBeforeLoadMore=\(lastPostIdBeforeLoadMore?.description ?? "nil")")
        print("【调试】FeedDataManager: 加载更多完成，新数据已追加到列表末尾")
    }
    
    // MARK: - 分类加载
    /// 加载分类帖子
    /// - Parameters:
    ///   - category: 要加载的分类
    ///   - feedMode: 当前Feed模式
    func loadCategoryPosts(category: CategoryItem, feedMode: FeedMode) async {
        print("【调试】FeedDataManager: 加载分类 \(category.name) (ID: \(category.id), Slug: \(category.slug)) 的帖子")
        
        // 重置页码和加载状态
        currentPage = 0
        isLoading = true
        isLoadingMore = false

        // 重置横幅相关状态（分类切换相当于重新开始）
        lastPostIdBeforeLoadMore = nil
        hasPerformedRefreshOrLoadMore = false
        
        // 停用当前显示的帖子
        await deactivateCurrentPosts()
        
        // 构造请求参数
        var parameters: [String: Any] = [:]
        parameters["filter"] = "default"
        
        // 调用API获取分类数据
        if category.id == 0 {
            // 所有分类，根据模式选择端点
            await fetchPostsForCurrentMode(
                isLoadingMore: false,
                preserveCurrentItems: false,
                feedMode: feedMode,
                selectedCategoryId: category.id,
                categories: [category]
            )
        } else {
            // 特定分类，使用c/slug/id/l/latest.json格式
            await postService.fetchCategoryPosts(
                slug: category.slug,
                categoryId: category.id,
                page: currentPage,
                parameters: parameters,
                preserveCurrentItems: false,
                isLoadingMore: false
            )
        }
        
        // 更新加载状态
        isLoading = false
        print("【调试】FeedDataManager: 加载分类 \(category.name) 完成")
    }
    
    // MARK: - 标签处理
    /// 处理标签选择
    /// - Parameter tagName: 选中的标签名称
    func handleTagSelection(tagName: String) async {
        print("【调试】FeedDataManager: 选择标签: \(tagName)")
        
        // 重置页码和加载状态
        currentPage = 0
        isLoading = true
        isLoadingMore = false

        // 重置横幅相关状态（标签选择相当于重新开始）
        lastPostIdBeforeLoadMore = nil
        hasPerformedRefreshOrLoadMore = false

        // 停用当前显示的帖子
        await deactivateCurrentPosts()
        
        // 调用标签API
        await postService.fetchTagPosts(
            tagName: tagName,
            page: currentPage,
            preserveCurrentItems: false,
            isLoadingMore: false
        )
        
        // 更新加载状态
        isLoading = false
        print("【调试】FeedDataManager: 标签 \(tagName) 加载完成")
    }
    
    /// 处理标签取消选择
    /// - Parameters:
    ///   - feedMode: 当前Feed模式
    ///   - selectedCategoryId: 选中的分类ID
    ///   - categories: 分类列表
    func handleTagDeselection(
        feedMode: FeedMode,
        selectedCategoryId: Int,
        categories: [CategoryItem]
    ) async {
        print("【调试】FeedDataManager: 取消标签选择")
        
        // 重新加载默认数据
        await handleRefresh(
            source: "取消标签选择",
            maintainVisualContinuity: false,
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: categories
        )
    }
    
    // MARK: - 搜索处理
    /// 执行搜索
    /// - Parameters:
    ///   - searchText: 搜索文本
    ///   - sortOrder: 排序方式
    /// - Returns: 搜索结果
    func performSearch(searchText: String, sortOrder: SearchSortOrder) async throws -> SearchResult {
        guard !searchText.trimmingCharacters(in: .whitespaces).isEmpty else {
            throw NetworkError.invalidURL
        }

        print("【调试】FeedDataManager: 执行搜索: \(searchText)")

        return try await postService.fetchSearchResults(for: searchText, sortOrder: sortOrder)
    }

    // MARK: - Core Data操作

    /// 记录加载更多前的最后一个帖子ID（按显示顺序的最后一个）
    private func recordLastPostIdBeforeLoadMore() async {
        let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isActiveInHome == true")
        fetchRequest.sortDescriptors = [
            NSSortDescriptor(keyPath: \Post.last_fetched, ascending: true), // 按显示顺序排序
            NSSortDescriptor(keyPath: \Post.id, ascending: false) // 只保留ID作为次要排序
        ]

        do {
            let allPosts = try viewContext.fetch(fetchRequest)
            print("【调试】FeedDataManager: 🔍 recordLastPostIdBeforeLoadMore - 找到\(allPosts.count)个活跃帖子")
            print("【调试】FeedDataManager: 🔍 前5个帖子ID: \(allPosts.prefix(5).map { $0.id })")
            print("【调试】FeedDataManager: 🔍 后5个帖子ID: \(allPosts.suffix(5).map { $0.id })")

            if let lastPost = allPosts.last { // 取最后一个（显示顺序的最后一个）
                let previousId = lastPostIdBeforeLoadMore
                lastPostIdBeforeLoadMore = lastPost.id

                if let prevId = previousId {
                    print("【调试】FeedDataManager: 🎯 横幅位置移动: 从帖子\(prevId)后 -> 帖子\(lastPost.id)后")
                } else {
                    print("【调试】FeedDataManager: 🎯 首次设置横幅位置: 帖子\(lastPost.id)后")
                }
            } else {
                lastPostIdBeforeLoadMore = nil
                print("【调试】FeedDataManager: ⚠️ 没有找到当前显示的帖子")
            }
        } catch {
            print("【调试】FeedDataManager: ❌ 记录最后帖子ID失败: \(error)")
            lastPostIdBeforeLoadMore = nil
        }
    }

    /// 停用当前显示的帖子
    func deactivateCurrentPosts() async {
        print("【调试】FeedDataManager: 开始停用当前帖子...")

        // 获取当前所有活跃的帖子
        let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "isActiveInHome == true")

        do {
            let activePosts = try viewContext.fetch(fetchRequest)
            print("【调试】FeedDataManager: 找到 \(activePosts.count) 个活跃帖子需要停用")

            // 批量更新
            for post in activePosts {
                post.isActiveInHome = false
            }

            // 保存更改
            if viewContext.hasChanges {
                try viewContext.save()
                print("【调试】FeedDataManager: 成功停用所有帖子")
            }
        } catch {
            print("【调试】FeedDataManager: 停用帖子时出错: \(error)")
        }
    }

    /// 批量更新帖子的活跃状态
    /// - Parameter ids: 要更新的帖子ID数组
    func updateBatchPostsActiveState(ids: [Int64]) async {
        // 创建批量更新请求
        let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id IN %@", ids)

        do {
            let batchPosts = try viewContext.fetch(fetchRequest)

            // 更新每个帖子的isActiveInHome属性
            for post in batchPosts {
                post.isActiveInHome = false
            }

            // 保存更改
            if viewContext.hasChanges {
                try viewContext.save()
                print("【调试】FeedDataManager: 成功更新一批\(batchPosts.count)个帖子")
            }
        } catch {
            print("【调试】FeedDataManager: 批量更新帖子时出错: \(error)")
        }
    }

    // MARK: - 私有辅助方法
    /// 根据当前模式获取帖子
    private func fetchPostsForCurrentMode(
        isLoadingMore: Bool,
        preserveCurrentItems: Bool,
        feedMode: FeedMode,
        selectedCategoryId: Int,
        categories: [CategoryItem]
    ) async {
        print("【调试】FeedDataManager.fetchPostsForCurrentMode: selectedCategoryId=\(selectedCategoryId), feedMode=\(feedMode)")

        // 如果有选中的分类，使用分类API
        if selectedCategoryId != 0, let selectedCategory = categories.first(where: { $0.id == selectedCategoryId }) {
            print("【调试】FeedDataManager: 使用分类API - \(selectedCategory.name)")
            var parameters: [String: Any] = [:]
            parameters["filter"] = "default"

            await postService.fetchCategoryPosts(
                slug: selectedCategory.slug,
                categoryId: selectedCategory.id,
                page: currentPage,
                parameters: parameters,
                preserveCurrentItems: preserveCurrentItems,
                isLoadingMore: isLoadingMore
            )
        } else {
            // 默认根据模式选择端点
            print("【调试】FeedDataManager: 使用默认API - feedMode=\(feedMode)")
            switch feedMode {
            case .latest:
                print("【调试】FeedDataManager: 调用 fetchLatestPosts")
                await postService.fetchLatestPosts(
                    page: currentPage,
                    noDefinitions: false,
                    preserveCurrentItems: preserveCurrentItems,
                    isLoadingMore: isLoadingMore
                )
            case .popular:
                print("【调试】FeedDataManager: 调用 fetchHotPosts")
                await postService.fetchHotPosts(
                    page: currentPage,
                    noDefinitions: false,
                    preserveCurrentItems: preserveCurrentItems,
                    isLoadingMore: isLoadingMore
                )
            }
        }
    }

    // MARK: - 发布者
    /// PostService的发布者代理
    var newlyFetchedPostIdsPublisher: AnyPublisher<Set<Int64>, Never> {
        postService.newlyFetchedPostIdsPublisher
    }


}
