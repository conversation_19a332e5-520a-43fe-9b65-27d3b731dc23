import Combine
import CoreData
// import Refresher
import SwiftUI
import UIKit

// MARK: - FeedView 主视图
// 重构后的FeedView，职责清晰，代码简洁

struct FeedView: View {
    // MARK: - 依赖注入
    @EnvironmentObject var postService: PostServiceImpl
    @EnvironmentObject var settingsManager: SettingsManager

// MARK: - ViewModel
    @StateObject private var viewModel: FeedViewModel
    @StateObject private var searchViewModel: SearchViewModel
    
    // 状态管理已合并到FeedViewModel中
    private let scrollObserver = ScrollOffsetObserver()
    @State private var currentScrollOffset: CGFloat = 0
    @State private var previousScrollOffset: CGFloat = 0
    
    // MARK: - 性能优化相关
    // 是否使用优化的列表视图
    @AppStorage("useOptimizedFeedList") private var useOptimizedFeedList: Bool = false

    // MARK: - 滚动优化状态
    @State private var lastScrollStateUpdate: Date = Date()
    @State private var scrollStateUpdateWorkItem: DispatchWorkItem?
    @State private var scrollEventCount: Int = 0
    @State private var throttledEventCount: Int = 0

    // MARK: - Footer 显示状态管理
    @State private var isLoadingAreaVisible: Bool = false
    @State private var isFooterLockedByLoader: Bool = false



    // MARK: - Core Data
    @FetchRequest(
        sortDescriptors: [
            NSSortDescriptor(keyPath: \Post.last_fetched, ascending: true),
            NSSortDescriptor(keyPath: \Post.id, ascending: false), // 只保留ID作为次要排序
        ],
        predicate: NSPredicate(format: "isActiveInHome == true"),
        animation: nil)
    private var allPosts: FetchedResults<Post>

    // MARK: - 环境变量
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.colorScheme) private var colorScheme

    // MARK: - 计算属性
    /// 获取所有可见帖子
    var posts: [Post] {
        let result = Array(allPosts)
        return result
    }


    
    // MARK: - Initializer
    init() {
        // Get services from environment - these will be injected by FilinkApp
        // We can't access them here directly, so we'll use a placeholder approach
        // The actual services will be injected via environment objects

        // Create placeholder instances that will be replaced by environment injection
        let networkService = NetworkServiceImpl(
            cookieManager: CookieManager.shared,
            settingsManager: SettingsManager()
        )
        let postService = PostServiceImpl(
            networkService: networkService,
            viewContext: CoreDataManager.shared.container.viewContext
        )

        _viewModel = StateObject(wrappedValue: FeedViewModel(
            postService: postService,
            viewContext: CoreDataManager.shared.container.viewContext
        ))

        _searchViewModel = StateObject(wrappedValue: SearchViewModel(
            postService: postService
        ))
    }

    var body: some View {
        GeometryReader { mainGeo in
            mainContentView(mainGeo: mainGeo)
        }
    }

    @ViewBuilder
    private func mainContentView(mainGeo: GeometryProxy) -> some View {
        ScrollViewReader { scrollProxy in
            ScrollView {
                VStack(spacing: 0) {
                    // 顶部锚点
                    Color.clear
                        .frame(width: UIScreen.main.bounds.width, height: 0)
                        .id(viewModel.scrollTopAnchorID)

                    // 滚动偏移锚点
                    Color.clear
                        .frame(width: UIScreen.main.bounds.width, height: 0)
                        .readingScrollViewOffset(
                            coordinateSpaceName: viewModel.listCoordinateSpace,
                            observer: scrollObserver
                        )

                    // 切换优化模式或标准模式
                    if useOptimizedFeedList {
                        // 优化的帖子列表视图 - 性能更好
                        OptimizedFeedListView(
                            posts: posts,
                            showPinnedPosts: settingsManager.showPinnedPosts,
                            lastPostIdBeforeLoadMore: viewModel.dataManager.lastPostIdBeforeLoadMore,
                            hasPerformedRefreshOrLoadMore: viewModel.dataManager.hasPerformedRefreshOrLoadMore,
                            onScrollToBanner: {
                                // 使用最佳性能的弹簧动画
                                withAnimation(.interactiveSpring(
                                    response: 0.6,        // 响应速度：适中
                                    dampingFraction: 0.8,  // 阻尼：减少弹跳
                                    blendDuration: 0.2     // 混合时间：平滑过渡
                                )) {
                                    scrollProxy.scrollTo("new-data-banner", anchor: .topLeading)
                                }
                            }
                        )
                        .id("optimized-feed-list-\(settingsManager.showPinnedPosts ? "pinned" : "normal")")
                    } else {
                        // 标准帖子列表视图 - 原始实现
                        FeedPostListView(
                            posts: posts,
                            showPinnedPosts: settingsManager.showPinnedPosts,
                            lastPostIdBeforeLoadMore: viewModel.dataManager.lastPostIdBeforeLoadMore,
                            hasPerformedRefreshOrLoadMore: viewModel.dataManager.hasPerformedRefreshOrLoadMore,
                            onScrollToBanner: {
                                // 使用最佳性能的弹簧动画
                                withAnimation(.interactiveSpring(
                                    response: 0.6,        // 响应速度：适中
                                    dampingFraction: 0.8,  // 阻尼：减少弹跳
                                    blendDuration: 0.2     // 混合时间：平滑过渡
                                )) {
                                    scrollProxy.scrollTo("new-data-banner", anchor: .topLeading)
                                }
                            }
                        )
                        .id("feed-post-list-\(settingsManager.showPinnedPosts ? "pinned" : "normal")")
                    }

                    // 统一的加载状态组件
                    FeedLoadingView(
                        posts: posts,
                        isLoading: viewModel.isLoading,
                        isLoadingMore: viewModel.isLoadingMore,
                        onLoadMore: viewModel.loadMorePosts,
                        onBottomAreaVisibilityChanged: { isVisible in
                            let wasVisible = isLoadingAreaVisible

                            // print("【底部回调】收到可见性变化: wasVisible=\(wasVisible), isVisible=\(isVisible), isLoadingMore=\(viewModel.isLoadingMore)")

                            // 立即更新状态
                            DispatchQueue.main.async {
                                isLoadingAreaVisible = isVisible

                                // 修复的锁定/解锁逻辑
                                if !wasVisible && isVisible {
                                    // 加载视图进入底部 → 锁定Footer
                                    isFooterLockedByLoader = true
                                    print("【Footer锁定】加载视图进入底部，Footer被锁定隐藏")
                                } else if wasVisible && !isVisible {
                                    // 加载视图离开底部 → 解锁Footer
                                    isFooterLockedByLoader = false
                                    print("【Footer解锁】加载视图离开底部，Footer解除锁定")
                                } else if !isVisible && !viewModel.isLoadingMore {
                                    // 🆕 修复：如果加载区域不可见且没有在加载，强制解锁
                                    if isFooterLockedByLoader {
                                        isFooterLockedByLoader = false
                                        print("【Footer强制解锁】加载区域不可见且未在加载，强制解除锁定")
                                    }
                                } else {
                                    print("【Footer状态】无变化 - wasVisible=\(wasVisible), isVisible=\(isVisible), locked=\(isFooterLockedByLoader)")
                                }
                            }
                        },
                        bottomInset: viewModel.bottomInset
                    )

                }
            }
            // .refresher {
            //     print("【RFR_ACTION】下拉刷新触发")

            //     // 确保HeaderView完全展开
            //     if self.currentScrollOffset < 0 {
            //         withAnimation(.easeInOut(duration: 0.3)) {
            //             scrollProxy.scrollTo(viewModel.scrollTopAnchorID, anchor: .top)
            //         }
            //         try? await Task.sleep(nanoseconds: 300_000_000)
            //     }

            //     // 执行刷新
            //     await viewModel.handleRefresh(source: "下拉刷新", maintainVisualContinuity: false)
            //     print("【RFR_ACTION】数据刷新完成")
            // }
            .coordinateSpace(name: viewModel.listCoordinateSpace)
            .safeAreaInset(edge: .top, spacing: 0) {
                FeedHeaderView(
                    newOffset: currentScrollOffset,
                    oldOffset: previousScrollOffset,
                    headerHeight: viewModel.headerHeight,
                    topSafeArea: viewModel.currentTopSafeArea,
                    feedMode: $viewModel.feedMode,
                    selectedCategoryId: $viewModel.selectedCategoryId,
                    showSearch: $viewModel.showSearch,
                    searchIconState: viewModel.searchIconState,
                    onModeChange: { newMode in
                        viewModel.feedMode = newMode
                        Task {
                            await viewModel.handleRefresh(
                                source: "模式切换: \(newMode.rawValue)",
                                maintainVisualContinuity: false)
                        }
                    },
                    onTitleTap: {
                        withAnimation(.easeInOut(duration: 0.4)) {
                            scrollProxy.scrollTo(viewModel.scrollTopAnchorID, anchor: .top)
                        }
                    },
                    onTitleDoubleTap: {
                        Task {
                            await viewModel.scrollToTopAndRefresh(scrollProxy: scrollProxy, scrollTopAnchorID: viewModel.scrollTopAnchorID)
                        }
                    },
                    onCategorySelected: { category in
                        Task {
                            await viewModel.loadCategoryPosts(category: category)
                        }
                    }
                )
            }
            .ignoresSafeArea(SafeAreaRegions.container, edges: Edge.Set.top)
            .background(Color(UIColor.systemBackground))
            // .safeAreaInset(edge: .bottom, spacing: 0) {
            //     FeedFooterView(
            //         bottomInset: 0,
            //         newOffset: currentScrollOffset,
            //         oldOffset: previousScrollOffset,
            //         onTagSelected: { tagName in
            //             Task {
            //                 if let tagName = tagName {
            //                     await viewModel.handleTagSelection(tagName: tagName)
            //                 } else {
            //                     await viewModel.handleTagDeselection()
            //                 }
            //             }
            //         },
            //         isLoading: viewModel.isLoadingMore,
            //         onLoadMore: viewModel.loadMorePosts
            //     )
            // }
            
            .captureSafeArea4(
                top: $viewModel.currentTopSafeArea,
                bottom: $viewModel.bottomInset
            )
            .onAppear {
                // 🆕 重置 Footer 锁定状态
                isFooterLockedByLoader = false
                print("【Footer初始化】视图出现，重置Footer锁定状态")
                
                // 添加调试信息 - 当前是否使用优化列表视图
                print("【性能优化】当前列表模式: \(useOptimizedFeedList ? "优化模式" : "标准模式")")

                // 设置滚动回调
                viewModel.onScrollToBanner = {
                    withAnimation(.interactiveSpring(
                        response: 0.6,
                        dampingFraction: 0.8,
                        blendDuration: 0.2
                    )) {
                        scrollProxy.scrollTo("new-data-banner", anchor: .center)
                    }
                }

                // 只在首次加载且没有数据时进行初始化加载
                print("【调试】FeedView.onAppear: posts.count=\(posts.count), currentPage=\(viewModel.currentPage)")
                if posts.isEmpty && viewModel.currentPage == 0 {
                    print("【调试】FeedView.onAppear: 条件满足，开始初始化加载")
                    Task {
                        await viewModel.handleRefresh(
                            source: "视图初次加载",
                            maintainVisualContinuity: false)
                    }
                } else {
                    print("【调试】FeedView.onAppear: 条件不满足，跳过初始化加载")
                }
            }
            .onChange(of: allPosts.count) { newCount in
                print("【调试】FeedView: allPosts数量变化: \(newCount)")
            }
            .onReceive(
                scrollObserver.subject
                    .removeDuplicates { abs($0 - $1) < 0.1 }
            ) { receivedMinY in
                // 【关键路径】立即更新本地滚动偏移量，用于Header/Footer动画
                previousScrollOffset = currentScrollOffset
                currentScrollOffset = receivedMinY

                // 【性能监控】统计滚动事件
                scrollEventCount += 1

                // 【非关键路径】延迟更新ScrollStateManager，避免影响UI响应
                updateScrollStateManagerThrottled(offset: receivedMinY)


            }
            .sheet(isPresented: $viewModel.showSearch) {
                NavigationView {
                    SearchView()
                }
                .environmentObject(searchViewModel) // Inject SearchViewModel into the sheet's environment
            }
            .overlay(alignment: .bottom) {
                // Footer overlay - 不占用 ScrollView 空间，显示逻辑由 Footer 自己管理
                FeedFooterView(
                    bottomInset: 0,
                    newOffset: currentScrollOffset,
                    oldOffset: previousScrollOffset,
                    isFooterLockedByLoader: isFooterLockedByLoader,
                    isLoadingAreaVisible: isLoadingAreaVisible,
                    isLoadingMore: viewModel.isLoadingMore,
                    onTagSelected: { tagName in
                        Task {
                            if let tagName = tagName {
                                await viewModel.handleTagSelection(tagName: tagName)
                            } else {
                                await viewModel.handleTagDeselection()
                            }
                        }
                    }
                )
            }

        }
    }

    // MARK: - 滚动优化方法
    /// 节流更新ScrollStateManager，避免影响Header/Footer响应
    private func updateScrollStateManagerThrottled(offset: CGFloat) {
        let now = Date()
        let timeSinceLastUpdate = now.timeIntervalSince(lastScrollStateUpdate)

        // 取消之前的延迟任务
        scrollStateUpdateWorkItem?.cancel()

        // 如果距离上次更新时间太短，延迟执行
        let minInterval: TimeInterval = 0.033 // ~30fps，比60fps稍慢但足够流畅

        if timeSinceLastUpdate >= minInterval {
            // 立即执行
            ScrollStateManager.shared.updateScrollState(currentOffset: offset)
            lastScrollStateUpdate = now
            throttledEventCount += 1
        } else {
            // 延迟执行
            let delay = minInterval - timeSinceLastUpdate
            let workItem = DispatchWorkItem {
                ScrollStateManager.shared.updateScrollState(currentOffset: offset)
                // 注意：由于FeedView是结构体，这里不能直接更新状态
                // 状态更新将在下次滚动事件中自然更新
            }

            scrollStateUpdateWorkItem = workItem
            DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
        }

        // 【性能监控】每100个事件打印一次统计
        if scrollEventCount % 100 == 0 {
            let throttleRatio = Double(throttledEventCount) / Double(scrollEventCount) * 100
            print("【滚动优化】事件统计 - 总计: \(scrollEventCount), 处理: \(throttledEventCount), 节流率: \(String(format: "%.1f", throttleRatio))%")
        }
    }


}
