import Foundation
import SwiftUI
import Combine
import CoreData

// MARK: - Feed相关枚举定义

// MARK: - Feed模式枚举
enum FeedMode: String, CaseIterable, Identifiable {
    case latest = "最新"
    case popular = "热门"

    var id: String { self.rawValue }

    var icon: String {
        switch self {
        case .latest:
            return "clock"
        case .popular:
            return "flame"
        }
    }
}

// MARK: - 搜索图标状态枚举
enum SearchIconState {
    case search
    case loading
    case success
}

@MainActor
class FeedViewModel: ObservableObject {
    // MARK: - Feed控制状态
    @Published var feedMode: FeedMode = .latest
    @Published var selectedCategoryId: Int = 0
    @Published var showSearch = false
    @Published var searchIconState: SearchIconState = .search

    // MARK: - 滚动状态（从FeedState合并）
    @Published var headerHeight: CGFloat = 200
    @Published var currentTopSafeArea: CGFloat = 0
    @Published var bottomInset: CGFloat = 0

    // MARK: - 常量（从FeedState合并）
    let listCoordinateSpace = "FeedListCoordinateSpace"
    let scrollTopAnchorID = UUID()

    // 加载状态通过dataManager提供，避免重复状态
    var isLoading: Bool { dataManager.isLoading }
    var isLoadingMore: Bool { dataManager.isLoadingMore }
    var currentPage: Int { dataManager.currentPage }

    // MARK: - 回调
    var onScrollToBanner: (() -> Void)?

    // MARK: - Dependencies
    private let postService: PostService
    let dataManager: FeedDataManager // 改为public，供FeedView访问
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initializer
    init(postService: PostService, viewContext: NSManagedObjectContext) {
        self.postService = postService
        // Convert PostService to PostServiceImpl for FeedDataManager
        guard let postServiceImpl = postService as? PostServiceImpl else {
            fatalError("PostService must be PostServiceImpl")
        }
        self.dataManager = FeedDataManager(postService: postServiceImpl, viewContext: viewContext)


    }

    // MARK: - Public Methods
    
    /// 处理刷新操作
    func handleRefresh(source: String, maintainVisualContinuity: Bool) async {
        guard !isLoading else {
            print("【\(source)】正在加载中，跳过此次刷新")
            return
        }

        print("【\(source)】开始刷新...")

        // 重置页码（DataManager会处理isLoading状态）
        dataManager.currentPage = 0

        await dataManager.handleRefresh(
            source: source,
            maintainVisualContinuity: maintainVisualContinuity,
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: Filink.categories
        )

        print("【\(source)】刷新完成")
    }

    /// 加载更多帖子
    func loadMorePosts() async {
        print("【调试】FeedViewModel: loadMorePosts 被调用，当前 isLoadingMore=\(isLoadingMore)")
        guard !isLoadingMore else {
            print("【调试】FeedViewModel: 已在加载更多中，跳过此次请求")
            return
        }

        // 递增页码（DataManager会处理isLoadingMore状态）
        dataManager.currentPage += 1
        print("【调试】FeedViewModel: 开始加载更多帖子，页码: \(dataManager.currentPage - 1) -> \(dataManager.currentPage)")
        print("【调试】FeedViewModel: 调用 dataManager.loadMorePosts 前，isLoadingMore=\(isLoadingMore)")

        await dataManager.loadMorePosts(
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: Filink.categories
        )

        print("【调试】FeedViewModel: dataManager.loadMorePosts 完成后，isLoadingMore=\(isLoadingMore)")
        print("【调试】FeedViewModel: 加载更多完成，新数据已追加到列表末尾")

        // 加载完成后，触发滚动到横幅位置
        await triggerScrollToBannerIfNeeded()
    }

    /// 如果有横幅则滚动到横幅位置
    @MainActor
    private func triggerScrollToBannerIfNeeded() async {
        // 延迟一下确保UI更新完成
        try? await Task.sleep(nanoseconds: 300_000_000)

        // 检查是否有横幅需要显示
        if dataManager.lastPostIdBeforeLoadMore != nil {
            print("【调试】FeedViewModel: 🚀 触发滚动到横幅位置")
            onScrollToBanner?()
        }
    }

    /// 根据分类加载帖子
    func loadCategoryPosts(category: CategoryItem) async {
        guard !isLoading else { return }

        selectedCategoryId = category.id
        dataManager.currentPage = 0

        await dataManager.loadCategoryPosts(
            category: category,
            feedMode: feedMode
        )
    }

    /// 处理标签选择
    func handleTagSelection(tagName: String) async {
        guard !isLoading else { return }

        dataManager.currentPage = 0
        selectedCategoryId = 0 // 清除分类选择

        await dataManager.handleTagSelection(tagName: tagName)
    }
    
    /// 处理取消标签选择
    func handleTagDeselection() async {
        guard !isLoading else { return }

        await dataManager.handleTagDeselection(
            feedMode: feedMode,
            selectedCategoryId: selectedCategoryId,
            categories: Filink.categories
        )
    }




    
    /// 滚动到顶部并刷新
    func scrollToTopAndRefresh(scrollProxy: ScrollViewProxy, scrollTopAnchorID: UUID) async {
        withAnimation(.easeInOut(duration: 0.4)) {
            scrollProxy.scrollTo(scrollTopAnchorID, anchor: .top)
        }
        try? await Task.sleep(nanoseconds: 400_000_000)
        await handleRefresh(source: "双击标题刷新", maintainVisualContinuity: false)
    }
}
