import SwiftUI
import Combine

// MARK: - 滚动观察器
/// 使用Combine的PassthroughSubject进行滚动偏移观察
class ScrollOffsetObserver: ObservableObject {
    let subject = PassthroughSubject<CGFloat, Never>()
}

// MARK: - 滚动偏移修饰符
/// 用于读取ScrollView滚动偏移的ViewModifier
struct ScrollOffsetModifier: ViewModifier {
    let coordinateSpaceName: String
    let observer: ScrollOffsetObserver

    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    let calculatedMinY = geometry.frame(
                        in: .named(coordinateSpaceName)
                    ).minY

                    // 将发送操作包装在DispatchQueue.main.async中，以避免在视图更新期间直接修改状态
                    DispatchQueue.main.async {
                        observer.subject.send(calculatedMinY)
                    }

                    return Color.clear
                }
            )
    }
}

// MARK: - View扩展
extension View {
    /// 读取ScrollView滚动偏移
    func readingScrollViewOffset(
        coordinateSpaceName: String, 
        observer: ScrollOffsetObserver
    ) -> some View {
        self.modifier(
            ScrollOffsetModifier(
                coordinateSpaceName: coordinateSpaceName, 
                observer: observer
            )
        )
    }
    
    /// 条件修饰符
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// 条件修饰符（自动闭包版本）
    @ViewBuilder
    func `if`<Content: View>(_ condition: @autoclosure () -> Bool, transform: (Self) -> Content) -> some View {
        if condition() {
            transform(self)
        } else {
            self
        }
    }

    /// 条件安全区域插入
    @ViewBuilder
    func conditionalSafeAreaInset<Content: View>(
        edge: VerticalEdge,
        spacing: CGFloat,
        condition: Bool,
        @ViewBuilder content: () -> Content
    ) -> some View {
        if condition {
            self.safeAreaInset(edge: edge, spacing: spacing, content: content)
        } else {
            self
        }
    }
}

// MARK: - 常用常量
struct FeedConstants {
    static let defaultHeaderHeight: CGFloat = 200
    static let loadMoreTriggerHeight: CGFloat = 80
    static let animationDuration: Double = 0.3
    static let scrollAnimationDuration: Double = 0.4
    static let refreshDelayDuration: Double = 0.5
}

// MARK: - 工具函数
struct FeedUtils {
    /// 格式化滚动方向日志
    static func formatScrollDirectionLog(offset: CGFloat) -> String {
        if offset > 0 {
            let log = "下拉, Offset: \(String(format: "%.1f", offset))"
            return offset > 70 ? log + " (强)" : log
        } else if offset < 0 {
            return "上滑, Offset: \(String(format: "%.1f", offset))"
        } else {
            return "静止于基线"
        }
    }
    
    /// 估算帖子高度（用于滚动补偿）
    static func estimatePostHeight() -> CGFloat {
        return 120.0
    }
    
    /// 计算移除帖子的总高度
    static func calculateRemovedHeight(postCount: Int) -> CGFloat {
        return CGFloat(postCount) * estimatePostHeight()
    }
}
