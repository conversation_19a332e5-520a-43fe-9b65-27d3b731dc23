import SwiftUI
import Foundation

// MARK: - 统一的帖子显示协议
protocol PostDisplayable {
    // 基础信息
    var displayId: Int64 { get }
    var title: String? { get }
    var excerpt: String? { get }

    // 用户信息
    var username: String { get }
    var displayName: String { get }
    var avatarInfo: PostAvatarInfo { get }

    // 时间信息
    var createdAt: Date? { get }
    var lastPostedAt: Date? { get }

    // 状态信息
    var displayIsRead: Bool { get }
    var displayIsPined: Bool { get }

    // 分类和标签
    var displayTags: [String]? { get }
    var categoryId: Int? { get }

    // 统计信息
    var postsCount: Int? { get }
    var viewsCount: Int? { get }
}

// MARK: - 头像信息统一处理
enum PostAvatarInfo: Equatable {
    case local(username: String, identifier: String)
    case remote(username: String, template: String)
    case none(username: String)
    
    // 实现Equatable协议
    static func == (lhs: PostAvatarInfo, rhs: PostAvatarInfo) -> Bool {
        switch (lhs, rhs) {
        case (.local(let lhsUsername, let lhsId), .local(let rhsUsername, let rhsId)):
            return lhsUsername == rhsUsername && lhsId == rhsId
        case (.remote(let lhsUsername, let lhsTemplate), .remote(let rhsUsername, let rhsTemplate)):
            return lhsUsername == rhsUsername && lhsTemplate == rhsTemplate
        case (.none(let lhsUsername), .none(let rhsUsername)):
            return lhsUsername == rhsUsername
        default:
            return false
        }
    }
}

// MARK: - Post扩展实现协议
extension Post: PostDisplayable {
    var displayName: String {
        // 实现getUserDisplayName逻辑
        guard let user = self.user else { return "未知用户" }
        if let name = user.name, !name.isEmpty {
            return name
        } else if let username = user.username, !username.isEmpty {
            return username
        } else if let displayUsername = user.displayUsername, !displayUsername.isEmpty {
            return displayUsername
        } else {
            return "未知用户"
        }
    }

    var avatarInfo: PostAvatarInfo {
        if let user = self.user,
           let username = user.username,
           let avatarData = user.avatar, !avatarData.isEmpty {
            return .local(username: username, identifier: avatarData)
        }
        return .none(username: self.user?.username ?? "")
    }

    var username: String {
        return self.user?.username ?? ""
    }

    var createdAt: Date? {
        return self.created_at
    }

    var lastPostedAt: Date? {
        return self.last_posted_at
    }

    var displayId: Int64 {
        return self.id
    }

    // 使用不同的计算属性名避免冲突
    var displayIsRead: Bool {
        return self.isRead
    }

    var displayIsPined: Bool {
        return self.isPined
    }

    var displayTags: [String]? {
        return self.tagsArray // 使用现有的tagsArray属性
    }

    var categoryId: Int? {
        return Int(self.category_id)
    }

    var postsCount: Int? {
        return Int(self.posts_count)
    }

    var viewsCount: Int? {
        return Int(self.views)
    }
}

// MARK: - DisplayableResult扩展实现协议
extension DisplayableResult: PostDisplayable {
    var displayId: Int64 {
        Int64(topic.id)
    }

    var title: String? {
        topic.fancy_title
    }

    var excerpt: String? {
        post.blurb
    }

    var username: String {
        post.username
    }

    var displayName: String {
        // 如果name存在且不为空，使用name；否则使用username
        if let name = post.name, !name.isEmpty {
            return name
        } else {
            return post.username
        }
    }

    var avatarInfo: PostAvatarInfo {
        return .remote(username: post.username, template: post.avatar_template)
    }

    var createdAt: Date? {
        Date.flexibleDateParser(from: topic.created_at) // 从topic获取创建时间
    }

    var lastPostedAt: Date? {
        Date.flexibleDateParser(from: topic.last_posted_at ?? "") // 从topic获取最后回复时间
    }

    // 搜索结果的默认值
    var displayIsRead: Bool {
        false
    }

    var displayIsPined: Bool {
        false
    }

    var displayTags: [String]? {
        topic.tags
    }

    var categoryId: Int? {
        topic.category_id
    }

    var postsCount: Int? {
        topic.posts_count
    }

    var viewsCount: Int? {
        nil // 搜索结果没有这个字段
    }
}
