import SwiftUI
import CoreData

// MARK: - 主评论视图
struct CommentView: View {
    @StateObject private var viewModel: CommentViewModel
    
    @State private var expandedReplies: Set<String> = []
    
    // 初始化方法，接收依赖并创建ViewModel
    init(postId: String, allCommentIds: [Int64], authorId: Int64, commentService: CommentService, viewContext: NSManagedObjectContext) {
        _viewModel = StateObject(wrappedValue: CommentViewModel(
            postId: postId,
            allCommentIds: allCommentIds,
            authorId: authorId,
            commentService: commentService,
            viewContext: viewContext
        ))
    }
    
    var body: some View {
        if viewModel.isLoading {
            ProgressView("加载评论中...")
        } else if let errorMessage = viewModel.errorMessage {
            errorView(errorMessage)
        } else if viewModel.comments.isEmpty {
            emptyCommentsView
        } else {
            commentListView
        }
    }
    
    // MARK: - 子视图
    
    private var commentListView: some View {
        ScrollViewReader { scrollProxy in
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(viewModel.comments) { comment in
                        CommentRowView(
                            comment: comment,
                            onLike: { commentId in
                                Task {
                                    await viewModel.toggleLike(commentId: commentId)
                                }
                            },
                            onReply: { commentId in
                                // TODO: 实现回复功能
                                print("回复评论: \(commentId)")
                            },
                            onShowReplies: { commentId in
                                toggleReplies(commentId: commentId)
                            },
                            onJumpToComment: { commentId in
                                scrollProxy.scrollTo(commentId, anchor: .center)
                                viewModel.scrollToCommentId = commentId

                                // 2秒后清除高亮
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                    viewModel.scrollToCommentId = nil
                                }
                            },
                            isExpanded: expandedReplies.contains(comment.id),
                            highlightTargetId: viewModel.scrollToCommentId
                        )
                        .id(comment.id)
                    }

                    // 加载更多区域
                    if viewModel.canLoadMore {
                        loadMoreSection
                            .onAppear {
                                Task { await viewModel.loadMoreComments() }
                            }
                    } else if !viewModel.comments.isEmpty {
                        // 结束提示
                        Text("没有更多评论了")
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                            .padding(.vertical, 20)
                    }

                    // 底部安全间距
                    Color.clear
                        .frame(height: 80)
                }
                .padding(.top, 12)
            }
            .refreshable {
                await viewModel.refreshComments()
            }
        }
    }
    
    private var loadMoreSection: some View {
        VStack(spacing: 8) {
            if viewModel.isLoadingMore {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("加载中...")
                        .font(.system(size: 13))
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 16)
            }
        }
    }
    
    private var emptyCommentsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 40))
                .foregroundColor(.secondary.opacity(0.6))

            VStack(spacing: 4) {
                Text("还没有评论")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)

                Text("成为第一个发表评论的人吧")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.top, 40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle).foregroundColor(.red)
            Text("加载失败").font(.headline)
            Text(message).font(.subheadline).foregroundColor(.secondary).multilineTextAlignment(.center).padding(.horizontal)
            Button("重试") {
                Task { await viewModel.loadInitialComments() }
            }
            .buttonStyle(.bordered)
        }
    }
    

    
    private func toggleReplies(commentId: String) {
        expandedReplies.toggle(member: commentId)
    }


}

// MARK: - 评论行视图
struct CommentRowView: View {
    let comment: CommentModel
    let onLike: (String) -> Void
    let onReply: (String) -> Void
    let onShowReplies: (String) -> Void
    let onJumpToComment: (String) -> Void
    let isExpanded: Bool
    let highlightTargetId: String?

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 用户头像
            NavigationLink(destination: UserProfileView(username: comment.username, userService: UserServiceImpl(networkService: NetworkServiceImpl(), cookieManager: .shared, viewContext: CoreDataManager.shared.persistentContainer.viewContext))) {
                SimpleUserAvatarView.small(
                    username: comment.username,
                    avatarURL: comment.avatarUrl
                )
            }.buttonStyle(PlainButtonStyle())

            VStack(alignment: .leading, spacing: 6) {
                // 用户名和时间
                HStack(spacing: 8) {
                    Text(comment.username)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.primary)

                    if comment.isAuthor {
                        Text("楼主")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.red)
                            .cornerRadius(4)
                    } else if let floorInfo = comment.floorInfo {
                        Text(floorInfo)
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.accentColor)
                            .cornerRadius(4)
                    }

                    Spacer()

                    Text(comment.createdAt.timeAgoDisplay())
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                // 回复信息（如果有）
                if let replyToUser = comment.replyToUser {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack(spacing: 6) {
                            // 被回复用户的头像
                            AsyncImage(url: URL(string: replyToUser.avatarUrl ?? "")) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                            }
                            .frame(width: 16, height: 16)
                            .clipShape(Circle())

                            Text("@\(replyToUser.username)")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.secondary)

                            Spacer()
                        }

                        // 被回复的内容（如果有）
                        if let originalContent = replyToUser.originalCommentContent, !originalContent.isEmpty {
                            Text(originalContent)
                                .font(.system(size: 11))
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 6)
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(6)
                }

                // 评论内容
                Text(comment.content)
                    .font(.system(size: 14))
                    .foregroundColor(.primary)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)

                // 操作按钮
                HStack(spacing: 16) {
                    // 点赞按钮
                    Button(action: { onLike(comment.id) }) {
                        HStack(spacing: 4) {
                            Image(systemName: comment.isLiked ? "heart.fill" : "heart")
                                .font(.system(size: 12))
                                .foregroundColor(comment.isLiked ? .red : .secondary)
                            if comment.likeCount > 0 {
                                Text("\(comment.likeCount)")
                                    .font(.system(size: 12))
                                    .foregroundColor(comment.isLiked ? .red : .secondary)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 回复按钮
                    Button(action: { onReply(comment.id) }) {
                        HStack(spacing: 4) {
                            Image(systemName: "bubble.left")
                                .font(.system(size: 12))
                            Text("回复")
                                .font(.system(size: 12))
                        }
                        .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 查看回复按钮
                    if comment.replyCount > 0 {
                        Button(action: { onShowReplies(comment.id) }) {
                            HStack(spacing: 4) {
                                Text("\(comment.replyCount) 条回复")
                                    .font(.system(size: 12))
                                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                    .font(.system(size: 10))
                            }
                            .foregroundColor(.secondary)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    Spacer()
                }
                .padding(.top, 8)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            highlightTargetId == comment.id ?
            Color.accentColor.opacity(0.1) :
            Color.clear
        )
        .cornerRadius(8)
    }
}


// MARK: - 辅助扩展
extension Set {
    mutating func toggle(member: Element) {
        if contains(member) {
            remove(member)
        } else {
            insert(member)
        }
    }
}
