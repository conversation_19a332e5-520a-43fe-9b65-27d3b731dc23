import SwiftUI
import CoreData
import Combine

// MARK: - Model Definitions (Moved here for scope)

struct ReplyToUserInfo: Identifiable, Hashable {
    let id: Int64
    let username: String
    let avatarUrl: String?
    let originalCommentContent: String?
    let repliedCommentId: String?

    var idString: String { String(id) }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: ReplyToUserInfo, rhs: ReplyToUserInfo) -> Bool {
        return lhs.id == rhs.id
    }
}

struct CommentModel: Identifiable, Hashable {
    let id: String
    let userId: Int64
    let username: String
    let avatarUrl: String?
    let content: String
    let createdAt: Date
    let isLiked: Bool
    let likeCount: Int
    let replyCount: Int
    let level: Int
    let floorInfo: String?
    let replyToUser: ReplyToUserInfo?
    let authorId: Int64 // 原帖作者ID

    // 计算属性：判断是否为楼主
    var isAuthor: Bool {
        return userId == authorId
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: CommentModel, rhs: CommentModel) -> Bool {
        return lhs.id == rhs.id
    }
}

class UserInfoCache {
    static let shared = UserInfoCache()
    private var userInfoCache: [Int64: (username: String, avatarUrl: String?)] = [:]
    private let cacheLock = NSLock()
    private init() {}

    func preloadAllUsers(context: NSManagedObjectContext) {
        cacheLock.lock()
        defer { cacheLock.unlock() }
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        do {
            let users = try context.fetch(fetchRequest)
            for user in users {
                if let username = user.username {
                    userInfoCache[user.id] = (username: username, avatarUrl: user.avatar)
                }
            }
        } catch {
            print("UserInfoCache: Preload failed: \(error)")
        }
    }

    func getUserInfo(userId: Int64, context: NSManagedObjectContext) -> (username: String, avatarUrl: String?) {
        cacheLock.lock()
        if let cachedInfo = userInfoCache[userId] {
            cacheLock.unlock()
            return cachedInfo
        }
        cacheLock.unlock()
        
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", userId)
        fetchRequest.fetchLimit = 1
        do {
            if let user = try context.fetch(fetchRequest).first, let username = user.username {
                let info = (username: username, avatarUrl: user.avatar)
                print("【调试】UserInfoCache: 获取用户信息 - 用户ID: \(userId), 用户名: \(username), 头像: \(user.avatar ?? "nil")")
                cacheLock.lock()
                userInfoCache[userId] = info
                cacheLock.unlock()
                return info
            }
        } catch {
            print("UserInfoCache: Fetch failed for user \(userId): \(error)")
        }
        return (username: "未知用户", avatarUrl: nil)
    }
}

enum ForumFloor {
    static func getNameForFloor(_ floorNumber: Int) -> String {
        switch floorNumber {
        case 1: return "沙发"
        case 2: return "板凳"
        case 3: return "地板"
        default: return "\(floorNumber)楼"
        }
    }
}


// MARK: - CommentViewModel
@MainActor
class CommentViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var comments: [CommentModel] = []
    @Published var isLoading: Bool = true
    @Published var isLoadingMore: Bool = false
    @Published var errorMessage: String?
    @Published var canLoadMore: Bool = true
    @Published var totalCommentCount: Int
    @Published var scrollToCommentId: String?

    
    // MARK: - Private Properties
    private let postId: String
    private var allCommentIds: [Int64]
    private let authorId: Int64
    private let commentService: CommentService
    private let viewContext: NSManagedObjectContext
    private let parser = HTMLParser()
    
    private var currentCommentIndex: Int = 0
    private let pageSize: Int = 20
    
    // MARK: - Initialization
    init(
        postId: String,
        allCommentIds: [Int64],
        authorId: Int64,
        commentService: CommentService,
        viewContext: NSManagedObjectContext
    ) {
        self.postId = postId
        self.allCommentIds = allCommentIds
        self.authorId = authorId
        self.totalCommentCount = allCommentIds.count
        self.commentService = commentService
        self.viewContext = viewContext
        
        UserInfoCache.shared.preloadAllUsers(context: viewContext)
        CommentCache.shared.cacheAllCommentIds(postId: postId, commentIds: allCommentIds)
        

        
        Task {
            await loadInitialComments()
        }
    }
    
    // MARK: - Public Methods
    
    func loadInitialComments() async {
        isLoading = true
        errorMessage = nil
        
        let firstPageIds = CommentCache.shared.getNextPageCommentIds(
            postId: postId,
            startIndex: 0,
            pageSize: pageSize
        )
        
        guard !firstPageIds.isEmpty else {
            isLoading = false
            canLoadMore = false
            comments = []
            return
        }
        
        await fetchAndProcessComments(with: firstPageIds, isInitialLoad: true)
    }
    
    func loadMoreComments() async {
        guard !isLoadingMore, canLoadMore else { return }
        
        isLoadingMore = true
        
        let nextPageIds = CommentCache.shared.getNextPageCommentIds(
            postId: postId,
            startIndex: currentCommentIndex,
            pageSize: pageSize
        )
        
        guard !nextPageIds.isEmpty else {
            isLoadingMore = false
            canLoadMore = false
            return
        }
        
        await fetchAndProcessComments(with: nextPageIds, isInitialLoad: false)
    }


    
    // 新增：点赞功能
    func toggleLike(commentId: String) async {
        guard let index = comments.firstIndex(where: { $0.id == commentId }) else { return }
        
        let comment = comments[index]
        let newLikeStatus = !comment.isLiked
        let newLikeCount = newLikeStatus ? comment.likeCount + 1 : max(0, comment.likeCount - 1)
        
        // 立即更新UI
        comments[index] = CommentModel(
            id: comment.id,
            userId: comment.userId,
            username: comment.username,
            avatarUrl: comment.avatarUrl,
            content: comment.content,
            createdAt: comment.createdAt,
            isLiked: newLikeStatus,
            likeCount: newLikeCount,
            replyCount: comment.replyCount,
            level: comment.level,
            floorInfo: comment.floorInfo,
            replyToUser: comment.replyToUser,
            authorId: comment.authorId
        )
        
        // TODO: 在这里调用后端 API 更新点赞状态
        // await commentService.toggleLike(commentId: commentId)
    }
    
    // 新增：刷新评论
    func refreshComments() async {
        currentCommentIndex = 0
        canLoadMore = true
        await loadInitialComments()
    }
    

    
    // MARK: - Private Helper Methods
    
    private func fetchAndProcessComments(with ids: [Int64], isInitialLoad: Bool) async {
        do {
            var fetchedComments = try await fetchCommentsFromCoreData(with: ids)
            let fetchedIds = fetchedComments.map { $0.id }
            
            let missingIds = ids.filter { !fetchedIds.contains($0) }
            if !missingIds.isEmpty {
                if case .success(let networkComments) = await commentService.fetchSpecificComments(postId: postId, commentIds: missingIds) {
                    fetchedComments.append(contentsOf: networkComments)
                }
            }
            
            let newModels = await convertEntitiesToCommentModels(fetchedComments)
            
            if isInitialLoad {
                comments = newModels
            } else {
                comments.append(contentsOf: newModels)
            }
            
            currentCommentIndex += newModels.count
            canLoadMore = currentCommentIndex < totalCommentCount
            
        } catch {
            errorMessage = "加载评论失败: \(error.localizedDescription)"
        }
        
        if isInitialLoad {
            isLoading = false
        } else {
            isLoadingMore = false
        }
    }
    
    private func fetchCommentsFromCoreData(with ids: [Int64]) async throws -> [Comment] {
        guard !ids.isEmpty else { return [] }
        
        let fetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id IN %@", ids)
        
        let fetchedEntities = try viewContext.fetch(fetchRequest)
        
        return ids.compactMap { id in
            fetchedEntities.first { $0.id == id }
        }
    }
    
    private func convertEntitiesToCommentModels(_ entities: [Comment]) async -> [CommentModel] {
        var models: [CommentModel] = []
        
        for entity in entities {
            let userInfo = UserInfoCache.shared.getUserInfo(userId: entity.userId, context: viewContext)
            
            let commentContent: String
            do {
                let contentItems = try parser.parse(html: entity.cooked ?? "")
                commentContent = contentItems.map { item in
                    switch item {
                    case .text(let text, _, _, _): return text
                    case .link(let text, _): return text
                    default: return ""
                    }
                }.joined(separator: " ")
            } catch {
                commentContent = "内容解析失败"
            }
            
            let floorIndex = allCommentIds.firstIndex(of: entity.id).map { $0 + 1 }
            let floorInfo = floorIndex.map { ForumFloor.getNameForFloor($0) }
            
            var replyToUserInfo: ReplyToUserInfo? = nil
            if entity.replyToUserId != 0 {
                let repliedUserInfo = UserInfoCache.shared.getUserInfo(userId: entity.replyToUserId, context: viewContext)

                // 获取被回复评论的内容
                let repliedContent = getRepliedCommentContent(replyToPostNumber: entity.replyToPostNumber)

                replyToUserInfo = ReplyToUserInfo(
                    id: entity.replyToUserId,
                    username: repliedUserInfo.username,
                    avatarUrl: repliedUserInfo.avatarUrl,
                    originalCommentContent: repliedContent,
                    repliedCommentId: String(entity.replyToPostNumber)
                )
            }
            
            let model = CommentModel(
                id: String(entity.id),
                userId: entity.userId,
                username: userInfo.username,
                avatarUrl: userInfo.avatarUrl,
                content: commentContent,
                createdAt: entity.updated_at ?? Date(),
                isLiked: false,
                likeCount: Int(entity.like_count),
                replyCount: Int(entity.reply_count),
                level: 0,
                floorInfo: floorInfo,
                replyToUser: replyToUserInfo,
                authorId: self.authorId
            )
            models.append(model)
        }
        return models
    }

    // 获取被回复评论的内容
    private func getRepliedCommentContent(replyToPostNumber: Int32) -> String? {
        let fetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "post_number == %d", replyToPostNumber)
        fetchRequest.fetchLimit = 1

        do {
            if let repliedComment = try viewContext.fetch(fetchRequest).first {
                // 解析HTML内容获取纯文本
                if let cooked = repliedComment.cooked {
                    do {
                        let parsedContent = try parser.parseToNodes(html: cooked)
                        let plainText = extractPlainText(from: parsedContent)
                        // 限制显示长度，取第一行或前50个字符
                        let firstLine = plainText.components(separatedBy: CharacterSet.newlines).first ?? plainText
                        return String(firstLine.prefix(50))
                    } catch {
                        print("解析被回复评论HTML失败: \(error)")
                        // 如果解析失败，直接返回原始HTML的简化版本
                        let cleanText = cooked.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
                        let firstLine = cleanText.components(separatedBy: CharacterSet.newlines).first ?? cleanText
                        return String(firstLine.prefix(50))
                    }
                }
            }
        } catch {
            print("获取被回复评论内容失败: \(error)")
        }
        return nil
    }

    // 从解析的HTML节点中提取纯文本
    private func extractPlainText(from nodes: [HTMLNode]) -> String {
        var text = ""
        for node in nodes {
            if node.isTextNode {
                text += node.content
            } else {
                text += extractPlainText(from: node.children)
            }
        }
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}
