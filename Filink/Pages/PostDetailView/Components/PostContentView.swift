//
//  PostContentView.swift
//  Filink
//
//  Created for rendering post content in post detail
//

import SwiftUI

// MARK: - 辅助视图
struct ClearBackgroundView: UIViewRepresentable {
    func makeUIView(context: Context) -> UIView {
        return InnerView()
    }

    func updateUIView(_ uiView: UIView, context: Context) {}

    private class InnerView: UIView {
        override func didMoveToWindow() {
            super.didMoveToWindow()
            superview?.superview?.backgroundColor = .clear
        }
    }
}

// MARK: - 帖子内容视图
struct PostContentView: View {
    @EnvironmentObject var settingsManager: SettingsManager  // 添加以获取字体缩放值

    let mixedContentItems: [MixedContentItem]
    let linkCountsDict: [String: LinkCount]
    let width: CGFloat
    let bottomPadding: CGFloat
    let titleView: AnyView?
    let footerView: AnyView?
    let onImageTap: (([URL], Int) -> Void)?  // 新增：图片点击的回调闭包

    init(
        mixedContentItems: [MixedContentItem],
        linkCountsDict: [String: LinkCount],
        width: CGFloat,
        bottomPadding: CGFloat = 0,
        onImageTap: (([URL], Int) -> Void)? = nil,  // 新增
        @ViewBuilder titleView: @escaping () -> some View = { EmptyView() },
        @ViewBuilder footerView: @escaping () -> some View = { EmptyView() }
    ) {
        self.mixedContentItems = mixedContentItems
        self.linkCountsDict = linkCountsDict
        self.width = width
        self.bottomPadding = bottomPadding
        self.onImageTap = onImageTap  // 新增
        self.titleView = AnyView(titleView())
        self.footerView = AnyView(footerView())
    }

    var body: some View {
        EquatableContentView(
            mixedContentItems: mixedContentItems,
            width: width,
            bottomPadding: bottomPadding,
            linkCountsDict: linkCountsDict,
            fontScale: settingsManager.postContentFontScale,  // 传递当前字体缩放值
            onImageTap: onImageTap,  // 传递闭包
            titleView: { titleView ?? AnyView(EmptyView()) },
            footerView: { footerView ?? AnyView(EmptyView()) }
        )
    }
}

// MARK: - 隔离的代码块视图
struct IsolatedCodeBlockView: View, Equatable {
    let content: String
    let theme: CodeBlockTheme
    let codeBlockId: String  // 新增：代码块唯一标识符
    let onCollapseScroll: ((String) -> Void)?  // 修改：传递代码块ID
    let onExpandScroll: ((String, CGFloat, UnitPoint) -> Void)?  // 新增：展开滚动回调

    var body: some View {
        CodeBlockView(
            content: content,
            theme: theme,
            onCollapseScroll: {
                onCollapseScroll?(codeBlockId)
            },
            onExpandScroll: { height, anchor in
                onExpandScroll?(codeBlockId, height, anchor)
            }
        )
    }

    // 实现 Equatable 以避免不必要的重绘
    static func == (lhs: IsolatedCodeBlockView, rhs: IsolatedCodeBlockView)
        -> Bool
    {
        return lhs.content == rhs.content && lhs.codeBlockId == rhs.codeBlockId
            && String(describing: lhs.theme) == String(describing: rhs.theme)
    }
}

// MARK: - 隔离的引用块视图
struct IsolatedBlockquoteView: View, Equatable {
    let contentItems: [ContentItem]

    var body: some View {
        BlockquoteView(contentItems: contentItems)
    }

    // 实现 Equatable 以避免不必要的重绘
    static func == (lhs: IsolatedBlockquoteView, rhs: IsolatedBlockquoteView)
        -> Bool
    {
        return lhs.contentItems == rhs.contentItems
    }
}

// MARK: - 隔离的文本视图
struct IsolatedAttributedTextView: View, Equatable {
    let contentItems: [ContentItem]
    let fontScope: FontScope  // <-- 新增
    let onImageTap: (([URL], Int) -> Void)?
    @EnvironmentObject var settingsManager: SettingsManager

    var body: some View {
        AttributedTextView(
            contentItems: .constant(contentItems),
            bottomPadding: 0,
            lineSpacing: 4,
            fontScope: self.fontScope,  // <-- 新增
            onImageTap: onImageTap
        )
    }

    // 实现 Equatable 以避免不必要的重绘
    static func == (
        lhs: IsolatedAttributedTextView, rhs: IsolatedAttributedTextView
    ) -> Bool {
        // 比较内容项数组
        guard lhs.contentItems.count == rhs.contentItems.count else {
            return false
        }

        // 逐个比较内容项
        for i in 0..<lhs.contentItems.count {
            if lhs.contentItems[i] != rhs.contentItems[i] {
                return false
            }
        }

        // 注意：不再比较 settingsManager，因为 @EnvironmentObject 无法在静态方法中访问
        // 字体变化的重新渲染由 .id(viewVersion) 处理

        // onImageTap 闭包无法直接比较，但如果内容相同，通常闭包也应该相同
        return true
    }
}

// MARK: - 内容视图包装器
struct EquatableContentView: View, Equatable {
    @EnvironmentObject var settingsManager: SettingsManager
    @State private var viewVersion: Int = 0

    let mixedContentItems: [MixedContentItem]
    let width: CGFloat
    var bottomPadding: CGFloat = 0
    var titleView: AnyView? = nil
    var footerView: AnyView? = nil
    let linkCountsDict: [String: LinkCount]
    let onImageTap: (([URL], Int) -> Void)?  // 新增

    // 添加字体缩放值作为显式属性，用于Equatable比较
    let fontScale: Double

    // 便利初始化方法，支持添加标题视图和页脚视图
    init(
        contentItems: [ContentItem],
        width: CGFloat,
        bottomPadding: CGFloat = 0,
        linkCountsDict: [String: LinkCount],
        fontScale: Double,
        onImageTap: (([URL], Int) -> Void)? = nil,  // 新增
        @ViewBuilder titleView: @escaping () -> some View = { EmptyView() },
        @ViewBuilder footerView: @escaping () -> some View = { EmptyView() }
    ) {
        // 将普通ContentItem包装为MixedContentItem
        self.mixedContentItems = [.regularContent(contentItems)]
        self.width = width
        self.bottomPadding = bottomPadding
        self.linkCountsDict = linkCountsDict
        self.fontScale = fontScale
        self.onImageTap = onImageTap  // 新增
        self.titleView = AnyView(titleView())
        self.footerView = AnyView(footerView())
    }

    // 新增初始化方法，直接接受MixedContentItem数组
    init(
        mixedContentItems: [MixedContentItem],
        width: CGFloat,
        bottomPadding: CGFloat = 0,
        linkCountsDict: [String: LinkCount],
        fontScale: Double,
        onImageTap: (([URL], Int) -> Void)? = nil,  // 新增
        @ViewBuilder titleView: @escaping () -> some View = { EmptyView() },
        @ViewBuilder footerView: @escaping () -> some View = { EmptyView() }
    ) {
        self.mixedContentItems = mixedContentItems
        self.width = width
        self.bottomPadding = bottomPadding
        self.linkCountsDict = linkCountsDict
        self.fontScale = fontScale
        self.onImageTap = onImageTap  // 新增
        self.titleView = AnyView(titleView())
        self.footerView = AnyView(footerView())
    }

    // 根据URL查找对应的点击次数
    private func getClickCount(for url: String) -> Int {
        // 直接从字典中查找，O(1)复杂度
        if let linkCount = linkCountsDict[url] {
            return linkCount.clicks
        }
        return 0  // 如果没有找到匹配的URL，返回0
    }

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(spacing: 0) {
                    // 标题视图（如果有）
                    if let titleView = titleView {
                        titleView
                            .frame(width: width)
                    }

                    // 渲染混合内容
                    ForEach(
                        Array(mixedContentItems.enumerated()),
                        id: \.element.id
                    ) { index, item in
                            switch item {
                            case .regularContent(let contentItems):
                                // 常规内容使用隔离的AttributedTextView渲染，避免重绘
                                IsolatedAttributedTextView(
                                    contentItems: contentItems,
                                    fontScope: .detail,  // <- 新增
                                    onImageTap: onImageTap
                                )
                                .id("text-\(index)-\(viewVersion)")  // 使用索引和版本号作为ID
                                .padding(10)
                                .frame(width: width)

                            case .codeBlock(let content, let theme):
                                // 代码块使用父视图给定的最大宽度，使用独立的视图容器避免重绘
                                let codeBlockId = "code-\(index)-\(viewVersion)"
                                IsolatedCodeBlockView(
                                    content: content,
                                    theme: theme,
                                    codeBlockId: codeBlockId,
                                    onCollapseScroll: { blockId in
                                        // 当代码块折叠时，滚动到对应的代码块位置
                                        withAnimation(.easeOut(duration: 0.3)) {
                                            proxy.scrollTo(
                                                blockId, anchor: .top)
                                        }
                                    },
                                    onExpandScroll: { blockId, height, anchor in
                                        // 当代码块展开时，根据智能判断结果进行滚动
                                        withAnimation(.easeOut(duration: 0.3)) {
                                            proxy.scrollTo(
                                                blockId, anchor: anchor)
                                        }
                                    }
                                )
                                .equatable()
                                .frame(maxWidth: .infinity)
                                .id(codeBlockId)
                                .padding(.vertical, 10)

                            case .linkCard(
                                let imageURL, let url, let domain, let title,
                                let description):
                                // 链接卡片使用LinkCardView渲染
                                LinkCardView(
                                    imageURL: imageURL != nil
                                        ? URL(string: imageURL!) : nil,
                                    domainName: domain ?? "未知域名",
                                    title: title ?? "无标题",
                                    tagNumber: getClickCount(for: url!),
                                    description: description ?? "无描述"
                                )
                                .id("linkcard-\(index)-\(viewVersion)")
                                .padding(.horizontal, 10)
                                .padding(.vertical, 5)
                                .frame(width: width)
                                .onTapGesture {
                                    // 点击时打开链接
                                    if let urlString = url,
                                        let linkURL = URL(string: urlString)
                                    {
                                        UIApplication.shared.open(linkURL)
                                    }
                                }
                            case .poll(let pollItem):
                                PollView(pollItem: pollItem)
                                    .id("poll-\(index)-\(viewVersion)")
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 5)
                                    .frame(width: width)

                            case .blockquote(let contentItems):
                                // 引用块使用BlockquoteView渲染
                                IsolatedBlockquoteView(
                                    contentItems: contentItems
                                )
                                .equatable()
                                .id("blockquote-\(index)-\(viewVersion)")
                                .padding(.horizontal, 10)
                                .padding(.vertical, 5)
                                .frame(width: width)
                            }
                        }

                        // 页脚视图（如果有）
                        if let footerView = footerView {
                            footerView
                                .frame(width: width)
                        }

                        // 底部填充，确保内容底部有足够空间
                        if bottomPadding > 0 {
                            Spacer().frame(height: bottomPadding)
                        }
                    }
            }
            .onReceive(settingsManager.$postContentFontScale) { newScale in
                // 字体比例变化时，清理样式缓存并更新视图版本号以触发重绘
                StyleManager.clearStyleCache()
                viewVersion += 1
            }
        }
    }

    // 实现Equatable以避免不必要的重绘
    static func == (lhs: EquatableContentView, rhs: EquatableContentView)
        -> Bool
    {
        // 基本属性比较
        guard
            lhs.width == rhs.width && lhs.bottomPadding == rhs.bottomPadding
                && lhs.mixedContentItems.count == rhs.mixedContentItems.count
                && lhs.linkCountsDict.count == rhs.linkCountsDict.count
        else {
            return false
        }

        // 比较字体缩放比例 - 关键修复：确保字体变化时重绘
        if abs(lhs.fontScale - rhs.fontScale) > 0.001 {
            return false
        }

        // 比较内容项的ID，更精确地判断内容是否变化
        for i in 0..<lhs.mixedContentItems.count {
            if lhs.mixedContentItems[i].id != rhs.mixedContentItems[i].id {
                return false
            }
        }

        // 比较 linkCountsDict 的内容
        for (key, value) in lhs.linkCountsDict {
            if rhs.linkCountsDict[key]?.clicks != value.clicks {
                return false
            }
        }

        // 如果所有内容项ID、linkCountsDict 和字体缩放都相同，则认为视图相等
        return true
    }
}
