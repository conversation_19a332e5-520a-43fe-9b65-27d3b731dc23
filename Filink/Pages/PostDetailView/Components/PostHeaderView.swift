//
//  PostHeaderView.swift
//  Filink
//
//  Created for displaying post header information
//

import SwiftUI

struct PostHeaderView: View {
    let postTitle: String
    let authorName: String
    let authorAvatarFilename: String
    let authorUsername: String
    let authorDisplayUsername: String
    let authorTitle: String
    let postCreatedAt: Date?

    @EnvironmentObject var settingsManager: SettingsManager

    // 构建头像URL
    private func buildAvatarURL(fromTemplate template: String) -> String? {
        guard !template.isEmpty, let baseURL = URL(string: settingsManager.website) else { return nil }
        let processedTemplate = template.replacingOccurrences(of: "{size}", with: "48")
        return baseURL.appendingPathComponent(processedTemplate).absoluteString
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 帖子标题
            Text(postTitle)
                .font(.title)
                .fontWeight(.bold)
                .padding(.horizontal, 10)
            
            // 作者信息
            HStack(alignment: .top, spacing: 10) {
                // 作者头像
                SimpleUserAvatarView.medium(
                    username: authorUsername.isEmpty ? "未知用户" : authorUsername,
                    avatarURL: buildAvatarURL(fromTemplate: authorAvatarFilename)
                )
                
                // 作者名称和头衔/显示用户名
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 6) {
                        Text(authorName.isEmpty ? "未知用户" : authorName)
                            .font(.headline)
                        
                        // 显示用户名（如果有）
                        if !authorDisplayUsername.isEmpty {
                            Text("@\(authorDisplayUsername)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // 用户头衔（如果有）
                    if !authorTitle.isEmpty {
                        Text(authorTitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 发布时间
                VStack {
                    Spacer()
                    if let createdAt = postCreatedAt {
                        Text(createdAt.timeAgoDisplay())
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                }
            }
            .padding(.horizontal, 10)
            .padding(.top, 10)
            
            // 添加底部分割线
            Divider()
                .padding(.vertical, 10)
        }
    }
}

// MARK: - Preview
struct PostHeaderView_Previews: PreviewProvider {
    static var previews: some View {
        PostHeaderView(
            postTitle: "示例帖子标题",
            authorName: "示例用户",
            authorAvatarFilename: "avatar.png",
            authorUsername: "example_user",
            authorDisplayUsername: "ExampleUser",
            authorTitle: "高级用户",
            postCreatedAt: Date()
        )
        .previewLayout(.sizeThatFits)
        .padding()
    }
}
