//
//  PostViewModeComponents.swift
//  Filink
//
//  Created for view mode switching components in post detail
//

import SwiftUI

// MARK: - 查看器视图模式
enum ViewMode: String, CaseIterable, Identifiable {
    case structure = "结构"
    case source = "源码"
    case content = "视图"

    var id: String { self.rawValue }

    var iconName: String {
        switch self {
        case .structure: return "list.bullet.indent"
        case .source: return "doc.text"
        case .content: return "doc.text.image"
        }
    }
}

// MARK: - 结构视图
struct PostStructureView: View {
    let parsedNodes: [HTMLNode]
    let isLoading: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            if parsedNodes.isEmpty && !isLoading {
                Text("HTML结构为空或解析失败。")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding()
            } else {
                ScrollView {
                    VStack(alignment: .leading, spacing: 0) {
                        ForEach(parsedNodes) { node in
                            TreeNodeView(node: node, depth: 0)
                            Divider()
                        }
                    }
                    .padding()
                }
            }
        }
    }
}

// MARK: - 源码视图
struct PostSourceView: View {
    let rawHTML: String
    
    var body: some View {
        ScrollView {
            Text(rawHTML)
                .font(.system(.body, design: .monospaced))
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .textSelection(.enabled)
        }
    }
}

// MARK: - 树形节点视图
struct TreeNodeView: View {
    let node: HTMLNode
    let depth: Int
    @State private var isExpanded: Bool = true // 默认展开第一层
    @Environment(\.colorScheme) private var colorScheme

    private var textColor: Color { colorScheme == .dark ? .white : .black }
    private var tagNameColor: Color { Color.accentColor }
    private var textNodeColor: Color { colorScheme == .dark ? .green : Color(UIColor.systemGreen) }
    private var commentColor: Color { Color.gray }

    private var nodeIcon: some View {
        Group {
            if node.isTextNode {
                Image(systemName: "text.alignleft").foregroundColor(textNodeColor)
            } else if node.tagName.starts(with: "!") { // DOCTYPE or Comment
                 if node.tagName == "!--" {
                     Image(systemName: "bubble.left.and.bubble.right.fill").foregroundColor(commentColor) // Comment icon
                 } else {
                     Image(systemName: "exclamationmark.shield.fill").foregroundColor(.purple) // DOCTYPE icon
                 }
            } else if node.tagName == "img" {
                Image(systemName: "photo.fill").foregroundColor(tagNameColor)
            } else if node.children.isEmpty {
                Image(systemName: "doc.fill").foregroundColor(tagNameColor)
            } else {
                Image(systemName: "folder.fill").foregroundColor(tagNameColor)
            }
        }
    }

    var body: some View {
        if !node.isTextNode {
            VStack(alignment: .leading, spacing: 0) {
                HStack(spacing: 4) {
                    if depth > 0 {
                        ForEach(0..<depth, id: \.self) { _ in Spacer().frame(width: 20) }
                    }
                    if !node.children.isEmpty {
                        Image(systemName: isExpanded ? "arrowtriangle.down.fill" : "arrowtriangle.right.fill")
                            .font(.system(size: 10)).frame(width: 12, height: 12).foregroundColor(textColor)
                            .onTapGesture { isExpanded.toggle() }
                    } else {
                        Spacer().frame(width: 12) // Placeholder for alignment
                    }
                    nodeIcon.frame(width: 16)
                    
                    if node.tagName == "!--" {
                        Text("注释") .foregroundColor(commentColor)
                            .italic()
                            .lineLimit(1)
                            .truncationMode(.tail)
                    } else if node.tagName.starts(with: "!") {
                         Text(node.tagName) .foregroundColor(.purple)
                            .lineLimit(1)
                            .truncationMode(.tail)
                    }
                    else {
                        Text(node.tagName).foregroundColor(tagNameColor)
                            .lineLimit(1)
                            .truncationMode(.tail)
                    }

                    if !node.attributes.isEmpty {
                        Text(formatAttributes(node.attributes))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                            .truncationMode(.tail)
                    }
                    Spacer()
                    Image(systemName: "info.circle")
                        .foregroundColor(.accentColor)
                }
                .padding(.vertical, 4)
                .contentShape(Rectangle())
                .onTapGesture {
                    if !node.children.isEmpty { isExpanded.toggle() }
                }

                if isExpanded && !node.children.isEmpty {
                    ForEach(node.children) { child in
                        TreeNodeView(node: child, depth: depth + 1)
                    }
                }
            }
        } else if node.isTextNode {
            let trimmedContent = node.content.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmedContent.isEmpty {
                HStack(spacing: 4) {
                    if depth > 0 {
                        ForEach(0..<depth, id: \.self) { _ in Spacer().frame(width: 20) }
                    }
                    Spacer().frame(width: 12) // Arrow placeholder
                    Image(systemName: "text.quote").foregroundColor(textNodeColor).frame(width:16) // Text icon
                    Text(trimmedContent)
                        .foregroundColor(textNodeColor)
                        .lineLimit(1)
                        .truncationMode(.tail)
                    Spacer()
                    Image(systemName: "info.circle")
                        .foregroundColor(.accentColor)
                }
                .padding(.vertical, 4)
            }
        }
    }

    private func formatAttributes(_ attributes: [String: String]) -> String {
        attributes.map { key, value in
            value.isEmpty ? key : "\(key)=\"\(value)\""
        }.joined(separator: " ")
    }
}
