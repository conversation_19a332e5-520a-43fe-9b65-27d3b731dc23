//
//  RelatedTopicsView.swift
//  Filink
//
//  Created for displaying related topics in post detail
//

import SwiftUI

struct RelatedTopicsView: View {
    let topics: [LinkCount]
    let onTopicSelected: (String) -> Void // 用于处理点击事件的回调

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            if !topics.isEmpty {
                Text("相关话题")
                    .font(.headline)
                    .padding(.horizontal)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 10) {
                        ForEach(topics, id: \.url) { topic in
                            if let postId = extractPostId(from: topic.url) {
                                But<PERSON>(action: {
                                    onTopicSelected(postId)
                                }) {
                                    TopicCard(title: topic.title ?? "无标题", clicks: topic.clicks)
                                }
                                .buttonStyle(PlainButtonStyle()) // 使用朴素样式以避免默认按钮行为
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .padding(.vertical)
    }

    private func extractPostId(from url: String) -> String? {
        // 假设URL格式为 "/p/12345"
        let components = url.split(separator: "/")
        if components.count > 1 && components[components.count - 2] == "p" {
            return String(components.last!)
        }
        return nil
    }
}

// MARK: - Topic Card View (Re-defined)
// 重新定义 TopicCard，因为它可能在移动文件后无法访问
struct TopicCard: View {
    let title: String
    let clicks: Int

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.subheadline)
                .lineLimit(2)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("\(clicks) 次点击")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(10)
        .frame(width: 150, height: 80)
        .background(Color(UIColor.systemGray6))
        .cornerRadius(8)
    }
}

