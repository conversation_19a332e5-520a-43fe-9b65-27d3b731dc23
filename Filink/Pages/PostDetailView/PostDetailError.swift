//
//  PostDetailError.swift
//  Filink
//
//  Created to define custom errors for the PostDetail module.
//

import Foundation

/// 帖子详情模块的特定错误类型
enum PostDetailError: LocalizedError {
    /// 网络请求失败
    case networkError(Error)
    
    /// 数据解析失败
    case parsingError(Error)
    
    /// 在数据源中未找到所需数据
    case dataNotFound
    
    /// 无效的帖子ID
    case invalidPostId
    
    /// 未知的错误
    case unknown(Error? = nil)
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .parsingError(let error):
            return "数据解析失败: \(error.localizedDescription)"
        case .dataNotFound:
            return "找不到指定的帖子或相关数据。"
        case .invalidPostId:
            return "帖子ID无效。"
        case .unknown(let error):
            return "发生未知错误: \(error?.localizedDescription ?? "没有更多信息")"
        }
    }
}
