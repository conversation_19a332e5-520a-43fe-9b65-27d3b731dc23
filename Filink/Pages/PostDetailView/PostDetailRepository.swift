//
//  PostDetailRepository.swift
//  Filink
//
//  Created to abstract the data source for post details.
//

import Foundation
import CoreData

/// 帖子详情数据仓库协议，定义了获取帖子详情数据的接口
protocol PostDetailRepositoryProtocol {
    /// 异步获取帖子详情的完整数据
    /// - Parameters:
    ///   - postId: 帖子的ID
    ///   - needsUserInfo: 是否需要从网络强制获取用户信息（例如，从关联话题跳转时）
    /// - Returns: 一个包含帖子所有相关信息的元组
    /// - Throws: `PostDetailError`
    func fetchPostDetails(postId: String, needsUserInfo: Bool) async throws -> (
        mainPost: String,
        allCommentIds: [Int64],
        linkCounts: [String: LinkCount]?,
        userInfo: DiscourseUser?,
        postCreatedAt: Date?,
        postTitle: String
    )
}

/// 帖子详情数据仓库的默认实现
class PostDetailRepository: PostDetailRepositoryProtocol {
    private let postService: PostServiceImpl
    private let viewContext: NSManagedObjectContext

    /// 初始化方法
    /// - Parameters:
    ///   - postService: 用于网络请求的帖子服务
    ///   - viewContext: Core Data 的 managed object context
    init(postService: PostServiceImpl, viewContext: NSManagedObjectContext) {
        self.postService = postService
        self.viewContext = viewContext
    }

    func fetchPostDetails(postId: String, needsUserInfo: Bool) async throws -> (
        mainPost: String,
        allCommentIds: [Int64],
        linkCounts: [String: LinkCount]?,
        userInfo: DiscourseUser?,
        postCreatedAt: Date?,
        postTitle: String
    ) {
        // 1. 首先尝试从 Core Data 加载本地已有的用户信息和标题
        let localData = loadPostInfoFromCoreData(postId: postId)
        
        // 2. 调用网络服务获取帖子详情
        // `needsUserInfo` 会传递给 service，决定是否必须从网络获取用户信息
        let result = await postService.fetchPostDetail(postId: postId, needsUserInfo: needsUserInfo || localData.authorName.isEmpty)

        switch result {
        case .success((let mainPost, let allCommentIds, let linkCounts, let remoteUserInfo, let postCreatedAt)):
            // 3. 组合数据
            // 如果网络请求返回了用户信息，则使用最新的用户信息
            // 否则，使用从 Core Data 加载的本地数据
            let finalUserInfo = remoteUserInfo ?? DiscourseUser(
                id: 0, // 本地数据可能没有用户ID
                username: localData.authorUsername,
                name: localData.authorName,
                avatarTemplate: localData.authorAvatarFilename,
                displayUsername: localData.authorDisplayUsername,
                title: localData.authorTitle
            )


            
            // 优先使用网络返回的创建时间，否则使用本地的
            let finalPostCreatedAt = postCreatedAt ?? localData.postCreatedAt
            
            // 标题总是优先使用本地 Core Data 的，因为它通常在列表视图中已经存在且更可靠
            let finalPostTitle = localData.postTitle.isEmpty ? "无标题" : localData.postTitle

            return (mainPost, allCommentIds, linkCounts, finalUserInfo, finalPostCreatedAt, finalPostTitle)
            
        case .failure(let error):
            // 4. 处理错误
            throw PostDetailError.networkError(error)
        }
    }

    // MARK: - Core Data Helper Methods (Private)

    /// 从 Core Data 加载帖子信息（用户数据和标题）
    private func loadPostInfoFromCoreData(postId: String) -> (
        authorName: String,
        authorUsername: String,
        authorAvatarFilename: String,
        authorDisplayUsername: String,
        authorTitle: String,
        postCreatedAt: Date?,
        postTitle: String
    ) {
        guard let postIdInt = Int64(postId) else { return ("", "", "", "", "", nil, "") }

        let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", postIdInt)
        fetchRequest.fetchLimit = 1

        do {
            guard let post = try viewContext.fetch(fetchRequest).first else {
                return ("", "", "", "", "", nil, "")
            }

            let postTitle = post.title ?? ""
            let postCreatedAt = post.created_at

            if let user = post.user {
                let authorName = getUserDisplayName(user: user)
                return (authorName, user.username ?? "", user.avatar ?? "", user.displayUsername ?? "", user.usertitle ?? "", postCreatedAt, postTitle)
            } else {
                // 如果 Post 没有直接关联 User，尝试通过 userId 查找
                if let user = findUser(byId: post.userId) {
                    let authorName = getUserDisplayName(user: user)
                    return (authorName, user.username ?? "", user.avatar ?? "", user.displayUsername ?? "", user.usertitle ?? "", postCreatedAt, postTitle)
                }
            }
            return ("", "", "", "", "", postCreatedAt, postTitle)
        } catch {
            // Core Data 查询失败
            return ("", "", "", "", "", nil, "")
        }
    }
    
    /// 根据用户ID查找用户
    private func findUser(byId userId: Int64) -> User? {
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", userId)
        fetchRequest.fetchLimit = 1
        return try? viewContext.fetch(fetchRequest).first
    }

    /// 获取用户显示名称的辅助方法
    private func getUserDisplayName(user: User) -> String {
        if let name = user.name, !name.isEmpty {
            return name
        } else if let username = user.username, !username.isEmpty {
            return username
        } else if let displayUsername = user.displayUsername, !displayUsername.isEmpty {
            return displayUsername
        } else {
            return "未知用户"
        }
    }
}
