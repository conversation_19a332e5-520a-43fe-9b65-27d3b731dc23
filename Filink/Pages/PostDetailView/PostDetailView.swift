//
//  PostDetailView.swift
//  Filink
//
//  Created for displaying post details and comments
//

import SwiftUI
import CoreData

// MARK: - 主视图
struct PostDetailView: View {
    // ViewModel驱动视图
    @StateObject private var viewModel: PostDetailViewModel
    
    // 环境依赖
    @EnvironmentObject var commentService: CommentServiceImpl
    @EnvironmentObject var settingsManager: SettingsManager // 新增
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) var dismiss

    // 初始化方法，负责创建ViewModel
    init(postId: String, isFromRelatedTopics: Bool = false, postService: PostServiceImpl, viewContext: NSManagedObjectContext) {
        let repository = PostDetailRepository(
            postService: postService,
            viewContext: viewContext
        )
        _viewModel = StateObject(wrappedValue: PostDetailViewModel(
            postId: postId,
            isFromRelatedTopics: isFromRelatedTopics,
            repository: repository
        ))
    }

    var body: some View {
        VStack {
            if viewModel.isLoading {
                ProgressView("加载中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = viewModel.errorMessage {
                errorView(error)
            } else {
                contentView
            }
        }
        .navigationTitle("主题详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar { toolbarContent }
        .onAppear {
            viewModel.loadPostDetail()
        }
        .gesture(dragToDismissGesture)
        .fullScreenCover(isPresented: $viewModel.isImageViewerPresented) {
            ImageViewer(
                items: viewModel.allImageViewerItems,
                selectedIndex: viewModel.selectedImageIndex,
                isPresented: $viewModel.isImageViewerPresented
            )
            .background(ClearBackgroundView())
        }
        .withToast()
        .overlay(
            // 使用条件判断防止不必要的重绘
            Group {
                if viewModel.showCommentDrawer {
                    drawerView
                }
            }
        )
    }

    // 将Drawer提取为独立的计算属性，避免重复创建
    @ViewBuilder
    private var drawerView: some View {
        Drawer(isOpen: $viewModel.showCommentDrawer) {
            CommentView(
                postId: viewModel.postId,
                allCommentIds: viewModel.allCommentIds,
                authorId: viewModel.authorId,
                commentService: commentService,
                viewContext: viewContext
            )
        }
    }
    
    // MARK: - Subviews
    
    @ViewBuilder
    private var contentView: some View {
        switch viewModel.selectedMode {
        case .structure:
            PostStructureView(parsedNodes: viewModel.parsedNodes, isLoading: viewModel.isLoading)
        case .source:
            PostSourceView(rawHTML: viewModel.rawHTML)
        case .content:
            mainContentView
        }
    }
    
    private var mainContentView: some View {
        ZStack(alignment: .bottom) {
            // 主内容区域 - 使用独立的视图来避免重绘
            MainContentContainer(
                mixedContentItems: viewModel.mixedContentItems,
                linkCountsDict: viewModel.linkCountsDict,
                onImageTap: { urls, index in
                    viewModel.handleImageTap(urls: urls, index: index)
                },
                headerView: headerView,
                footerView: footerView
            )
            .environmentObject(settingsManager)

            // 评论按钮 - 独立组件避免重绘
            CommentButtonView(
                commentCount: viewModel.commentCount,
                isDrawerOpen: viewModel.showCommentDrawer,
                onTap: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        viewModel.showCommentDrawer = true
                    }
                }
            )
        }
    }

    private var headerView: some View {
        NavigationLink(destination: UserProfileView(username: viewModel.authorUsername, userService: UserServiceImpl(networkService: NetworkServiceImpl(), cookieManager: .shared, viewContext: viewContext))) {
            PostHeaderView(
                postTitle: viewModel.postTitle,
                authorName: viewModel.authorName,
                authorAvatarFilename: viewModel.authorAvatarFilename,
                authorUsername: viewModel.authorUsername,
                authorDisplayUsername: viewModel.authorDisplayUsername,
                authorTitle: viewModel.authorTitle,
                postCreatedAt: viewModel.postCreatedAt
            )
        }.buttonStyle(PlainButtonStyle())
    }

    private var footerView: some View {
        RelatedTopicsView(topics: viewModel.relatedTopics) { postId in
            NotificationCenter.default.post(
                name: Notification.Name("NavigateToPost"),
                object: nil,
                userInfo: ["postId": postId]
            )
        }
    }
    
    private func errorView(_ errorMessage: String) -> some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .resizable().scaledToFit().frame(width: 50, height: 50)
                .foregroundColor(.red)
            Text(errorMessage)
                .foregroundColor(.red).padding().textSelection(.enabled)
            Button("重试") {
                viewModel.loadPostDetail()
            }
            .buttonStyle(.bordered).padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Toolbar

    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        // 字体控制专用按钮 - 方案1：分离式按钮
        ToolbarItem(placement: .navigationBarTrailing) {
            Menu {
                Button(action: {
                    settingsManager.postContentFontScale = min(2.0, settingsManager.postContentFontScale + 0.1)
                }) {
                    Label("大", systemImage: "textformat.size.larger")
                }
                .disabled(settingsManager.postContentFontScale >= 2.0)

                Button(action: {
                    settingsManager.postContentFontScale = 1.0
                }) {
                    Label("\(Int(round(settingsManager.postContentFontScale * 100)))%", systemImage: "arrow.counterclockwise")
                }

                Button(action: {
                    settingsManager.postContentFontScale = max(0.8, settingsManager.postContentFontScale - 0.1)
                }) {
                    Label("小", systemImage: "textformat.size.smaller")
                }
                .disabled(settingsManager.postContentFontScale <= 0.8)
            } label: {
                Image(systemName: "textformat.size")
                    .imageScale(.large)
            }
            .disabled(viewModel.showCommentDrawer) // 评论抽屉打开时禁用
        }

        // 其他功能菜单
        ToolbarItem(placement: .navigationBarTrailing) {
            Menu {
                Section("视图模式") {
                    ForEach(ViewMode.allCases) { mode in
                        Button(action: { viewModel.selectedMode = mode }) {
                            Label(mode.rawValue, systemImage: viewModel.selectedMode == mode ? "checkmark.circle.fill" : mode.iconName)
                        }
                    }
                }
            } label: {
                Image(systemName: "ellipsis.circle").imageScale(.large)
            }
            .disabled(viewModel.showCommentDrawer) // 评论抽屉打开时禁用
        }
    }
    
    // MARK: - Gestures

    private var dragToDismissGesture: some Gesture {
        DragGesture()
            .onEnded { value in
                if value.translation.width > 50 && abs(value.translation.height) < 50 {
                    dismiss()
                }
            }
    }
}

// MARK: - 独立组件，避免重绘

/// 主内容容器 - 独立组件，避免因Drawer状态变化而重绘
struct MainContentContainer: View, Equatable {
    let mixedContentItems: [MixedContentItem]
    let linkCountsDict: [String: LinkCount]
    let onImageTap: (([URL], Int) -> Void)?
    let headerView: AnyView
    let footerView: AnyView

    init(
        mixedContentItems: [MixedContentItem],
        linkCountsDict: [String: LinkCount],
        onImageTap: (([URL], Int) -> Void)?,
        headerView: some View,
        footerView: some View
    ) {
        self.mixedContentItems = mixedContentItems
        self.linkCountsDict = linkCountsDict
        self.onImageTap = onImageTap
        self.headerView = AnyView(headerView)
        self.footerView = AnyView(footerView)
    }

    var body: some View {
        PostContentView(
            mixedContentItems: mixedContentItems,
            linkCountsDict: linkCountsDict,
            width: UIScreen.main.bounds.width - 10,
            bottomPadding: 80,
            onImageTap: onImageTap,
            titleView: { headerView },
            footerView: { footerView }
        )
    }

    static func == (lhs: MainContentContainer, rhs: MainContentContainer) -> Bool {
        // 只比较关键属性，避免不必要的重绘
        return lhs.mixedContentItems.count == rhs.mixedContentItems.count &&
               lhs.linkCountsDict.count == rhs.linkCountsDict.count
    }
}

/// 评论按钮组件 - 独立组件，避免重绘
struct CommentButtonView: View, Equatable {
    let commentCount: Int
    let isDrawerOpen: Bool
    let onTap: () -> Void

    var body: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Button(action: onTap) {
                    HStack {
                        Image(systemName: "bubble.left.fill").font(.system(size: 18))
                        Text("\(commentCount > 0 ? String(commentCount) : "暂无") 条评论").fontWeight(.medium)
                    }
                    .foregroundColor(.white).padding(.vertical, 10).padding(.horizontal, 20)
                    .background(Color.accentColor).cornerRadius(25)
                    .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 2)
                }
                .disabled(isDrawerOpen)
                Spacer()
            }
            .padding(.bottom, 20)
        }
        .frame(height: 80)
    }

    static func == (lhs: CommentButtonView, rhs: CommentButtonView) -> Bool {
        return lhs.commentCount == rhs.commentCount && lhs.isDrawerOpen == rhs.isDrawerOpen
    }
}
