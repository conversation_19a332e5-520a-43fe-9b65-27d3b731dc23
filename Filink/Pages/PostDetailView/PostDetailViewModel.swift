//
//  PostDetailViewModel.swift
//  Filink
//
//  Created to manage the state and logic for PostDetailView.
//

import SwiftUI
import Combine

@MainActor
class PostDetailViewModel: ObservableObject {
    // MARK: - Published Properties (UI State)
    
    @Published var isLoading: Bool = true
    @Published var errorMessage: String? = nil
    @Published var selectedMode: ViewMode = .content
    
    // Post content
    @Published var postTitle: String = ""
    @Published var rawHTML: String = ""
    @Published var parsedNodes: [HTMLNode] = []
    @Published var mixedContentItems: [MixedContentItem] = []
    @Published var allImageViewerItems: [ImageViewerItem] = []
    
    // Author and post info
    @Published var authorId: Int64 = 0
    @Published var authorName: String = ""
    @Published var authorAvatarFilename: String = ""
    @Published var authorUsername: String = ""
    @Published var authorDisplayUsername: String = ""
    @Published var authorTitle: String = ""
    @Published var postCreatedAt: Date? = nil
    
    // Related topics
    @Published var relatedTopics: [LinkCount] = []
    @Published var linkCountsDict: [String: LinkCount] = [:]
    
    // Comments
    @Published var allCommentIds: [Int64] = []
    @Published var commentCount: Int = 0
    @Published var showCommentDrawer: Bool = false
    
    // Image viewer
    @Published var isImageViewerPresented: Bool = false
    @Published var selectedImageIndex: Int = 0

    // MARK: - Dependencies
    
    let postId: String
    private let isFromRelatedTopics: Bool
    private let repository: PostDetailRepositoryProtocol
    private let htmlParser = HTMLParser()

    // MARK: - Initializer
    
    init(
        postId: String,
        isFromRelatedTopics: Bool = false,
        repository: PostDetailRepositoryProtocol
    ) {
        self.postId = postId
        self.isFromRelatedTopics = isFromRelatedTopics
        self.repository = repository
    }
    
    // MARK: - Public Methods
    
    /// 加载帖子详情的入口方法
    func loadPostDetail() {
        guard isLoading else { return }
        errorMessage = nil
        
        Task {
            do {
                let details = try await repository.fetchPostDetails(
                    postId: postId,
                    needsUserInfo: true  // 总是获取用户信息以确保头像正确显示
                )
                
                if let userInfo = details.userInfo {
                    self.authorId = userInfo.id
                    self.authorName = Self.getUserDisplayName(userInfo: userInfo)
                    self.authorUsername = userInfo.username
                    self.authorAvatarFilename = userInfo.avatarTemplate ?? ""
                    self.authorDisplayUsername = userInfo.displayUsername ?? ""
                    self.authorTitle = userInfo.title ?? ""
                }
                
                self.rawHTML = details.mainPost
                self.allCommentIds = details.allCommentIds
                self.commentCount = details.allCommentIds.count
                self.postCreatedAt = details.postCreatedAt
                self.postTitle = details.postTitle
                
                if let linkCounts = details.linkCounts {
                    let (topics, dict) = Self.processRelatedTopics(linkCounts)
                    self.relatedTopics = topics
                    self.linkCountsDict = dict
                }
                
                CommentCache.shared.cacheAllCommentIds(postId: postId, commentIds: allCommentIds)
                
                // 修正：添加 await
                await parseHTMLContent()
                
            } catch let error as PostDetailError {
                self.errorMessage = error.localizedDescription
            } catch {
                self.errorMessage = "发生未知错误: \(error.localizedDescription)"
            }
            
            self.isLoading = false
        }
    }
    
    /// 处理图片点击事件
    func handleImageTap(urls: [URL], index: Int) {
        guard !allImageViewerItems.isEmpty, index < urls.count else { return }
        self.selectedImageIndex = index
        self.isImageViewerPresented = true
    }

    // MARK: - Private Helper Methods
    
    /// 解析HTML内容并更新UI状态
    private func parseHTMLContent() async {
        do {
            let nodes = try self.htmlParser.parseToNodes(html: self.rawHTML)
            let items = nodes.toMixedContentItems(baseURL: nil)
            
            let imageViewerItems = items.flatMap { item -> [ImageViewerItem] in
                if case .regularContent(let contentItems) = item {
                    return contentItems.compactMap { contentItem in
                        if case .image(let url, let altText, _, _, let linkURL, let isEmoji) = contentItem, !isEmoji {
                            return ImageViewerItem(displayURL: url, highResURL: linkURL, altText: altText)
                        }
                        return nil
                    }
                }
                return []
            }
            
            self.parsedNodes = nodes
            self.mixedContentItems = items
            self.allImageViewerItems = imageViewerItems
            
        } catch {
            self.errorMessage = PostDetailError.parsingError(error).localizedDescription
        }
    }
    
    // MARK: - Private Static Helpers
    
    private static func getUserDisplayName(userInfo: DiscourseUser) -> String {
        if let name = userInfo.name, !name.isEmpty {
            return name
        } else if !userInfo.username.isEmpty {
            return userInfo.username
        } else if let displayUsername = userInfo.displayUsername, !displayUsername.isEmpty {
            return displayUsername
        } else {
            return "未知用户"
        }
    }
    
    private static func processRelatedTopics(_ linkCountsData: [String: LinkCount]) -> ([LinkCount], [String: LinkCount]) {
        let filteredTopics = linkCountsData.values.filter { $0.reflection == true }
        return (filteredTopics, linkCountsData)
    }
}
