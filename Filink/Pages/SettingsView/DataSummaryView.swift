// DataSummaryView.swift
import SwiftUI
import WebKit

struct DataSummaryView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var cookieManager: CookieManager
    @EnvironmentObject var settingsManager: SettingsManager
    @FetchRequest(entity: Post.entity(), sortDescriptors: []) var posts:
        FetchedResults<Post>
    @FetchRequest(entity: User.entity(), sortDescriptors: []) var users:
        FetchedResults<User>
    @State private var showingWebsiteCookieAlert = false

    // MARK: - 存储数据状态变量
    @State private var storageData: [StorageData] = []
    @State private var isLoading = true

    var body: some View {
        List {
            Section("数据统计") {
                Text("帖子数量: \(posts.count)")
                Text("用户数量: \(users.count)")
                // 添加清除所有用户数据按钮
                Button(role: .destructive) {
                    print("【调试】DataSummaryView: 清除所有用户数据。")
                    cookieManager.clearAllData()
                } label: {
                    Label("清除所有用户数据", systemImage: "trash.fill")
                }

                // 添加清除当前网站cookie的按钮
                Button(role: .destructive) {
                    showingWebsiteCookieAlert = true
                } label: {
                    Label("清除当前网站Cookie", systemImage: "xmark.circle.fill")
                }
                .alert("确认清除", isPresented: $showingWebsiteCookieAlert) {
                    Button("取消", role: .cancel) {}
                    Button("清除", role: .destructive) {
                        clearCurrentWebsiteCookies()
                    }
                } message: {
                    Text(
                        "确定要清除与 \(settingsManager.website) 相关的所有Cookie吗？这将导致所有用户登出。"
                    )
                }
            }

            // MARK: - 存储空间概览
            Section("存储空间概览") {
                if !isLoading {
                    let totalSize = storageData.map(\.size).reduce(0, +)
                    let segments = storageData.map { data in
                        StorageSegment(
                            name: data.name,
                            size: Double(data.size) / (1024 * 1024 * 1024),  // 转换为 GB
                            color: {
                                switch data.type {
                                case .cookies: return .blue
                                case .coreData: return .green
                                case .imageCache: return .orange
                                case .webViewCache: return .purple
                                case .userDefaults: return .yellow
                                case .tempFiles: return .gray
                                case .urlCache: return .red
                                case .other: return .secondary
                                case .fonts: return .brown
                                }
                            }()
                        )
                    }

                    StorageUsageDetailView(
                        usedGB: Double(totalSize) / (1024 * 1024 * 1024),  // 转换为 GB
                        segments: segments
                    )
                } else {
                    // 显示加载中提示
                    HStack {
                        Spacer()
                        ProgressView()
                            .padding()
                        Spacer()
                    }
                }
            }

            Section("Cookie Manager 数据") {
                if cookieManager.storedUsers.isEmpty {
                    Text("没有保存的 Cookie 用户数据。")
                        .foregroundColor(.secondary)
                } else {
                    ForEach(cookieManager.storedUsers.keys.sorted(), id: \.self)
                    { identifier in
                        if let userData = cookieManager.storedUsers[identifier]
                        {
                            DisclosureGroup {
                                VStack(alignment: .leading) {
                                    Text("用户标识符: \(identifier)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    if let username = userData.username,
                                        !username.isEmpty
                                    {
                                        Text("用户名: \(username)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    // 添加创建时间显示
                                    Text(
                                        "创建时间: \(userData.createdAt.formatDateTime())"
                                    )
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    Divider()
                                    Text("保存的 Cookies:")
                                        .font(.caption)
                                        .fontWeight(.bold)
                                    if userData.cookies.isEmpty {
                                        Text("无 Cookie。")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    } else {
                                        ForEach(
                                            userData.cookies.keys.sorted(),
                                            id: \.self
                                        ) { cookieName in
                                            HStack {
                                                Text(cookieName)
                                                    .font(.caption2)
                                                    .fontWeight(.medium)
                                                Spacer()
                                                Text(
                                                    userData.cookies[
                                                        cookieName]?.prefix(20)
                                                        ?? "..."
                                                )  // 截断显示
                                                .font(.caption2)
                                                .foregroundColor(.secondary)
                                            }
                                        }
                                    }
                                }
                                .padding(.vertical, 4)
                            } label: {
                                HStack {
                                    Text(userData.username ?? identifier)  // 优先显示用户名，否则显示标识符
                                        .font(.headline)
                                    Spacer()
                                    if cookieManager.activeUserIdentifier
                                        == identifier
                                    {
                                        Image(
                                            systemName: "checkmark.circle.fill"
                                        )
                                        .foregroundColor(.accentColor)
                                    }
                                }
                            }
                            .swipeActions(
                                edge: .trailing, allowsFullSwipe: false
                            ) {
                                Button(role: .destructive) {
                                    cookieManager.deleteUser(
                                        identifier: identifier)
                                } label: {
                                    Label("删除", systemImage: "trash")
                                }
                            }
                        }
                    }
                }
            }
        }
        .navigationTitle("数据统计")
        .task {
            await loadStorageData()
        }
        .refreshable {
            await loadStorageData()
        }
    }

    private func loadStorageData() async {
        isLoading = true
        storageData = await StorageCalculator.shared.getAllStorageData()
        isLoading = false

        // 添加调试打印
        print("【调试】DataSummaryView - 存储空间数据:")
        for data in storageData {
            print("【调试】  \(data.name): \(data.size.formattedSize)")
        }
        print("【调试】  总计: \(storageData.map(\.size).reduce(0, +).formattedSize)")
    }

    // 添加清除当前网站cookie的方法
    private func clearCurrentWebsiteCookies() {
        let websiteURL = settingsManager.website
        print("【调试】DataSummaryView: 清除网站 \(websiteURL) 的所有Cookie")

        // 使用WKWebsiteDataStore清除所有cookie
        WKWebsiteDataStore.default().fetchDataRecords(ofTypes: [
            WKWebsiteDataTypeCookies
        ]) { records in
            let websitesToRemove = records.filter { record in
                // 过滤出与当前网站相关的记录
                return record.displayName.contains(
                    URL(string: websiteURL)?.host ?? "")
            }

            if websitesToRemove.isEmpty {
                print("【调试】DataSummaryView: 未找到与 \(websiteURL) 相关的Cookie记录")
            } else {
                print(
                    "【调试】DataSummaryView: 找到 \(websitesToRemove.count) 条与 \(websiteURL) 相关的Cookie记录"
                )

                // 删除这些记录
                WKWebsiteDataStore.default().removeData(
                    ofTypes: [WKWebsiteDataTypeCookies], for: websitesToRemove
                ) {
                    print("【调试】DataSummaryView: 已清除 \(websiteURL) 的所有Cookie")

                    // 如果当前有活跃用户，也需要登出
                    if self.cookieManager.activeUserIdentifier != nil {
                        self.cookieManager.logout()
                    }
                }
            }
        }

        // 还需要清除HTTPCookieStorage中的相关cookie
        if let host = URL(string: websiteURL)?.host {
            if let cookies = HTTPCookieStorage.shared.cookies {
                for cookie in cookies {
                    if cookie.domain.contains(host) {
                        HTTPCookieStorage.shared.deleteCookie(cookie)
                        print(
                            "【调试】DataSummaryView: 已删除HTTP Cookie: \(cookie.name) from \(cookie.domain)"
                        )
                    }
                }
            }
        }
    }
}
