//
//  FAQView.swift
//  Filink
//
//  Created by <PERSON>line on 2025/7/9.
//

import SwiftUI

// MARK: - FAQViewModel
@MainActor
class FAQViewModel: ObservableObject {
    @Published var isLoading: Bool = true
    @Published var errorMessage: String?
    @Published var mixedContentItems: [MixedContentItem] = []

    private let htmlParser = HTMLParser()
    private let faqURL = URL(string: "https://linux.do/faq.html")!

    func loadFAQContent() {
        guard isLoading else { return }
        errorMessage = nil

        Task {
            do {
                let htmlString = try await fetchHTMLContent()
                await parseHTMLContent(html: htmlString)
            } catch {
                errorMessage = "加载失败: \(error.localizedDescription)"
            }
            isLoading = false
        }
    }

    private func fetchHTMLContent() async throws -> String {
        let (data, response) = try await URLSession.shared.data(from: faqURL)
        guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
            throw URLError(.badServerResponse)
        }
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw URLError(.cannotDecodeContentData)
        }
        return htmlString
    }

    private func parseHTMLContent(html: String) async {
        do {
            let nodes = try htmlParser.parseToNodes(html: html)
            let items = nodes.toMixedContentItems(baseURL: faqURL)
            self.mixedContentItems = items
        } catch {
            self.errorMessage = "HTML 解析失败: \(error.localizedDescription)"
        }
    }
}


// MARK: - FAQView
struct FAQView: View {
    @StateObject private var viewModel = FAQViewModel()

    var body: some View {
        GeometryReader { geometry in
            VStack {
                if viewModel.isLoading {
                    ProgressView()
                } else if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .padding()
                } else {
                    PostContentView(
                        mixedContentItems: viewModel.mixedContentItems,
                        linkCountsDict: [:],
                        width: geometry.size.width,
                        bottomPadding: 20
                    )
                }
            }
        }
        .navigationTitle("FAQ")
        .onAppear {
            viewModel.loadFAQContent()
        }
    }
}

#Preview {
    NavigationView {
        FAQView()
    }
}
