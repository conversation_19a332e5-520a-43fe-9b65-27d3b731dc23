// FontManagementView.swift
import SwiftUI

struct FontManagementView: View {
    @State private var isPresentingFontPicker = false
    @State private var currentFontName: String = "系统默认"
    @State private var showingFontAlert = false
    @State private var fontAlertMessage = ""
    
    // 预留的控制开关状态
    @State private var applyToFeedView = FontManager.shared.isFontEnabled(for: .feed)
    @State private var applyToDetailView = FontManager.shared.isFontEnabled(for: .detail)
    @State private var applyToCommentView = FontManager.shared.isFontEnabled(for: .comment)
    @State private var applyToSettingsView = FontManager.shared.isFontEnabled(for: .settings)
    
    var body: some View {
        Form {
            Section(content: {
                HStack {
                    Text("当前字体")
                    Spacer()
                    Text(currentFontName)
                        .foregroundColor(.secondary)
                }
            }, header: {
                Text("当前字体")
            })
            
            Section(content: {
                Button {
                    isPresentingFontPicker = true
                } label: {
                    Label("导入新字体", systemImage: "square.and.arrow.down")
                }

                Button(role: .destructive) {
                    FontManager.shared.resetToDefaultFont()
                    updateCurrentFontName()
                    fontAlertMessage = "字体已恢复为系统默认。请重启应用以完全生效。"
                    showingFontAlert = true
                } label: {
                    Label("恢复默认字体", systemImage: "arrow.uturn.backward")
                }
            }, header: {
                Text("字体操作")
            })
            
            Section(content: {
                Toggle("首页", isOn: $applyToFeedView)
                    .onChange(of: applyToFeedView) { newValue in
                        FontManager.shared.setFontEnabled(newValue, for: .feed)
                    }
                Toggle("详情页", isOn: $applyToDetailView)
                    .onChange(of: applyToDetailView) { newValue in
                        FontManager.shared.setFontEnabled(newValue, for: .detail)
                    }
                Toggle("评论区", isOn: $applyToCommentView)
                    .onChange(of: applyToCommentView) { newValue in
                        FontManager.shared.setFontEnabled(newValue, for: .comment)
                    }
                Toggle("设置页", isOn: $applyToSettingsView)
                    .onChange(of: applyToSettingsView) { newValue in
                        FontManager.shared.setFontEnabled(newValue, for: .settings)
                    }
            }, header: {
                Text("应用范围")
            }, footer: {
                Text("选择自定义字体需要生效的页面。")
            })
        }
        .navigationTitle("字体管理")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            updateCurrentFontName()
            loadToggleStates()
        }
        .sheet(isPresented: $isPresentingFontPicker) {
            FontPicker(onFontPicked: { url in
                handleFontPicked(url: url)
            })
        }
        .alert("字体设置", isPresented: $showingFontAlert) {
            Button("好的", role: .cancel) {}
        } message: {
            Text(fontAlertMessage)
        }
    }
    
    // MARK: - 字体处理方法
    private func updateCurrentFontName() {
        if let fontName = FontManager.shared.getCurrentFontName() {
            self.currentFontName = fontName
        } else {
            self.currentFontName = "系统默认"
        }
    }

    private func loadToggleStates() {
        applyToFeedView = FontManager.shared.isFontEnabled(for: .feed)
        applyToDetailView = FontManager.shared.isFontEnabled(for: .detail)
        applyToCommentView = FontManager.shared.isFontEnabled(for: .comment)
        applyToSettingsView = FontManager.shared.isFontEnabled(for: .settings)
    }
    
    private func handleFontPicked(url: URL) {
        FontManager.shared.saveFont(from: url) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let fontName):
                    FontManager.shared.setCurrentFont(name: fontName)
                    updateCurrentFontName()
                    fontAlertMessage = "字体 '\(fontName)' 已成功导入并设置。请重启应用以完全生效。"
                    showingFontAlert = true
                case .failure(let error):
                    fontAlertMessage = "导入失败: \(error.localizedDescription)"
                    showingFontAlert = true
                }
            }
        }
    }
}
