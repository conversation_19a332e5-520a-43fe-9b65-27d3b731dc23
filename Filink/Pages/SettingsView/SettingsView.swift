// SettingsView.swift
import SwiftUI
import WebKit

struct SettingsView: View {
    @EnvironmentObject var settingsManager: SettingsManager
    @EnvironmentObject var cookieManager: CookieManager
    @State private var isPresentingLoginSheet = false
    @State private var loginURL: URL?  // 新增：用于存储登录 URL

    // --- 验证相关状态 ---
    @State private var showValidationErrorAlert = false

    // --- 字体设置状态 ---
    @State private var isPresentingFontPicker = false
    @State private var currentFontName: String = "系统默认"
    @State private var showingFontAlert = false
    @State private var fontAlertMessage = ""

    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
            ?? "N/A"
    }
    private var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "N/A"
    }
    private var minimumOSVersionFromPlist: String {
        Bundle.main.infoDictionary?["MinimumOSVersion"] as? String ?? "16.0"
    }
    private var deviceOSVersion: String { UIDevice.current.systemVersion }
    private var deviceModel: String { UIDevice.modelName }

    var body: some View {
        Form {
            // 使用单独的UserHeaderView组件显示头像和用户信息
            UserHeaderView()
                .listRowBackground(Color.clear)  // 确保没有行背景色
                .listRowInsets(EdgeInsets())  // 移除行内边距

            Section {
                if !cookieManager.availableUserIdentifiers.isEmpty {
                    ForEach(cookieManager.availableUserIdentifiers, id: \.self)
                    { identifier in
                        HStack {
                            // 获取用户名或标识符
                            if let username = cookieManager.storedUsers[
                                identifier]?.username, !username.isEmpty
                            {
                                // 只有当用户名存在且非空时才显示
                                Text(username)
                                    .foregroundColor(
                                        cookieManager.activeUserIdentifier
                                            == identifier ? .blue : .primary
                                    )
                                    .fontWeight(
                                        cookieManager.activeUserIdentifier
                                            == identifier ? .bold : .regular)

                                // 添加"当前活动"标签和状态图标
                                if cookieManager.activeUserIdentifier
                                    == identifier
                                {
                                    Spacer()

                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                } else {
                                    Spacer()
                                }
                            } else {
                                // 如果没有用户名，显示提示信息，但不自动删除
                                Text("用户名缺失 (\(identifier))")
                                    .foregroundColor(.orange)
                                Spacer()
                            }
                        }
                        .contentShape(Rectangle())
                        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                            // 无论是否是当前活动用户，都显示"删除"按钮
                            Button(role: .destructive) {
                                print("【调试】SettingsView: 删除用户 '\(identifier)'")
                                cookieManager.deleteUser(identifier: identifier)
                            } label: {
                                Label("删除", systemImage: "trash")
                            }
                        }
                    }
                }

                // 根据登录状态显示不同的按钮
                if cookieManager.isCurrentlyLoggedIn {
                    // 已登录状态：显示"验证"按钮
                    Button {
                        // 判断当前活跃用户是否是游客
                        if cookieManager.activeUserIdentifier == "guest_session" {
                            print("【调试】SettingsView: 已登录游客点击 '验证' 按钮。")
                            // 游客用户验证：重新进行游客验证流程以更新CF key
                            settingsManager.isGuestLoginActive = true  // 确保弹出 GuestViewRepresentable
                            isPresentingLoginSheet = true
                        } else {
                            // 普通用户已登录的验证逻辑
                            print("【调试】SettingsView: 已登录普通用户点击 '验证' 按钮。")

                            // 普通用户验证：打开WebKit进行CF验证以更新cf_clearance
                            // 不删除用户，而是通过WebKit让用户完成CF验证后自动更新Cookie
                            settingsManager.isGuestLoginActive = false

                            // 构造网站主页URL用于CF验证
                            if let websiteURL = URL(string: settingsManager.website) {
                                print("【调试】SettingsView: 打开WebKit进行CF验证，URL: \(websiteURL.absoluteString)")
                                loginURL = websiteURL
                                isPresentingLoginSheet = true
                            } else {
                                print("【调试】⚠️ SettingsView: 无法构造网站URL，请检查Website设置。")
                                showValidationErrorAlert = true
                            }
                        }
                    } label: {
                        Label("验证", systemImage: "checkmark.circle.fill")
                    }
                } else {
                    // 未登录状态：显示"登录"和"游客登录"两个按钮
                    Button {
                        print("【调试】SettingsView: 未登录用户点击 '登录' 按钮。")
                        settingsManager.isGuestLoginActive = false  // 确保弹出 LoginViewRepresentable
                        if let url = constructLoginUrl() {
                            print(
                                "【调试】SettingsView: constructLoginUrl() 返回 \(url.absoluteString)"
                            )
                            loginURL = url
                            isPresentingLoginSheet = true
                        } else {
                            print(
                                "【调试】⚠️ SettingsView: 无法构造登录 URL，请检查 Website 和 Login Path 设置。无法进行登录。"
                            )
                            // 考虑在这里显示一个 Toast 或 Alert 提示用户
                        }
                    } label: {
                        Label("登录", systemImage: "person.crop.circle.fill")
                    }

                    Button {
                        print("【调试】SettingsView: 未登录用户点击 '游客登录' 按钮。")
                        settingsManager.isGuestLoginActive = true  // 确保弹出 GuestViewRepresentable
                        isPresentingLoginSheet = true
                        print(
                            "【调试】SettingsView: 将显示 GuestViewRepresentable 进行游客验证。"
                        )
                    } label: {
                        Label("游客登录", systemImage: "person.fill.questionmark")
                    }
                }
            } header: {
                Text("用户")
            } footer: {
                Text("左滑退出用户。")
            }

            Section("外观设置") {
                NavigationLink {
                    FontManagementView()
                } label: {
                    Label("字体管理", systemImage: "textformat")
                }
            }

            Section("高级设置") {
                NavigationLink {
                    APISettingsView()
                } label: {
                    Label("API 服务器配置", systemImage: "wrench.and.screwdriver")
                }
                NavigationLink {
                    DataSummaryView()
                } label: {  // 将数据统计视图移到高级设置
                    Label("数据统计", systemImage: "chart.bar")
                }
                // 代码块UI示例
                NavigationLink {
                    CodeBlockView(
                        content: """
                            func calculateSum(a: Int, b: Int) -> Int {
                                // 这是一个计算两个整数和的函数
                                // 如果a或b为负数，则返回0
                                if a < 0 || b < 0 {
                                    return 0
                                }
                                return a + b
                            }

                            let x = 10
                            let y = 20
                            let sum = calculateSum(a: x, b: y)
                            print("The sum is: \\(sum)")

                            // 更多代码行来确保内容足够长
                            // 这是一个示例，展示了如何使用SwiftUI创建可折叠的代码块视图。
                            // 它可以用于显示代码片段、引用文本或其他需要格式化的长文本。
                            // 用户可以点击右下角的按钮来展开或折叠内容。
                            """,
                        theme: .purple
                    )
                    .navigationTitle("代码块示例")
                } label: {
                    Label("代码块示例", systemImage: "curlybraces.square")
                }

            }

            Section(header: Text("开发测试")) {
                NavigationLink(destination: RichTextEditor()) {
                    Label("富文本编辑器测试", systemImage: "textformat")
                }

                NavigationLink(destination: BlockquoteTestView()) {
                    Label("引用块样式测试", systemImage: "quote.bubble")
                }
                
                NavigationLink(destination: DrawerTestView()) {
                    Label("新抽屉组件测试", systemImage: "rectangle.and.hand.point.up.left")
                }
            }

            Section {
                HStack {
                    Label("App 版本", systemImage: "app.badge").foregroundColor(
                        .gray)
                    Spacer()
                    Text("\(appVersion) (\(buildNumber))").foregroundColor(
                        .secondary)
                }
                HStack {
                    Label("最低支持系统", systemImage: "shift").foregroundColor(.gray)
                    Spacer()
                    Text("iOS \(minimumOSVersionFromPlist)").foregroundColor(
                        .secondary)
                }
            } header: {
                Label("关于应用", systemImage: "info.circle")
            }

            Section {
                HStack {
                    Label("设备型号", systemImage: "ipad.and.iphone")
                        .foregroundColor(.gray)
                    Spacer()
                    Text(deviceModel).foregroundColor(.secondary)
                }
                HStack {
                    Label("系统版本", systemImage: "gear").foregroundColor(.gray)
                    Spacer()
                    Text("iOS \(deviceOSVersion)").foregroundColor(.secondary)
                }
            } header: {
                Label("设备信息", systemImage: "cpu")
            }
        }
        .navigationTitle("设置")
        .sheet(isPresented: $isPresentingLoginSheet) {
            // 根据 isGuestLogin 状态决定显示 LoginViewRepresentable 还是 GuestViewRepresentable
            // 备用方案说明：如果未来有更多登录方式，可以考虑使用枚举或更复杂的视图工厂模式来管理不同的登录视图。
            if settingsManager.isGuestLoginActive {  // 假设 settingsManager 中有一个状态来判断是否是游客登录
                GuestViewRepresentable(
                    isPresented: $isPresentingLoginSheet,
                    url: URL(string: settingsManager.website)  // 游客登录直接访问网站根目录
                )
            } else {
                LoginViewRepresentable(
                    isPresented: $isPresentingLoginSheet,
                    url: constructLoginUrl())
            }
        }
        .alert("验证错误", isPresented: $showValidationErrorAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text("无法构造网站URL，请检查网站设置。")
        }
        // 移除 toolbar 中的 NavigationLink
    }

    // 修改 canConstructLoginUrl 为 constructLoginUrl，并返回 URL?
    private func constructLoginUrl() -> URL? {
        guard let baseUrl = URL(string: settingsManager.website),
            !settingsManager.loginPath.isEmpty
        else { return nil }
        var path = settingsManager.loginPath
        if !path.hasPrefix("/") && !settingsManager.website.hasSuffix("/") {
            path = "/" + path
        } else if path.hasPrefix("/") && settingsManager.website.hasSuffix("/")
        {
            path = String(path.dropFirst())
        }
        var components = URLComponents(
            url: baseUrl, resolvingAgainstBaseURL: false)
        components?.path = path
        return components?.url
    }
}
