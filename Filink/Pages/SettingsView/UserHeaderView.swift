// UserHeaderView.swift
import SwiftUI
import WebKit

// 用户头像和信息显示组件
struct UserHeaderView: View {
    @EnvironmentObject var cookieManager: CookieManager
    @EnvironmentObject var settingsManager: SettingsManager
    @Environment(\.managedObjectContext) private var viewContext

    // 用于触发用户资料获取的状态
    @State private var isLoadingUserProfile = false

    // 用于缓存失败的请求，避免重复请求
    @State private var failedUserProfileRequests: Set<String> = []

    // 获取所有用户数据用于头像查找
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \User.id, ascending: true)],
        predicate: nil,
        animation: .default)
    private var users: FetchedResults<User>

    // 根据用户名查找用户的头像模板
    private func getUserAvatarTemplate(for username: String) -> String? {
        // 通过用户名查找用户（更可靠的方式）
        let user = users.first { user in
            user.username == username
        }

        // 返回完整的头像模板路径
        return user?.avatar
    }

    // 根据用户名查找用户的完整信息
    private func getUserInfo(for username: String) -> User? {
        return users.first { user in
            user.username == username
        }
    }

    // 检查是否需要获取用户资料
    private func shouldFetchUserProfile(for username: String) -> Bool {
        // 检查是否是游客用户
        guard let activeIdentifier = cookieManager.activeUserIdentifier else {
            return false
        }

        // 游客用户不需要获取用户资料
        if activeIdentifier == "guest_session" {
            print("【调试】UserHeaderView: 游客用户不需要获取用户资料")
            return false
        }

        // 检查是否已经失败过，避免重复请求
        if failedUserProfileRequests.contains(activeIdentifier) {
            print("【调试】UserHeaderView: 用户 '\(activeIdentifier)' 的资料获取之前已失败，跳过重复请求")
            return false
        }

        let userInfo = getUserInfo(for: username)
        // 如果用户不存在或者头像为空，则需要获取
        return userInfo == nil || userInfo?.avatar?.isEmpty != false
    }

    // 构建头像URL
    private func buildAvatarURL(fromTemplate template: String) -> String? {
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        let processedTemplate = template.replacingOccurrences(of: "{size}", with: "128")
        return baseURL.appendingPathComponent(processedTemplate).absoluteString
    }

    // 获取当前用户资料
    private func fetchCurrentUserProfile() {
        guard !isLoadingUserProfile,
              cookieManager.isCurrentlyLoggedIn,
              let username = cookieManager.activeUsername,
              shouldFetchUserProfile(for: username) else {
            return
        }

        isLoadingUserProfile = true

        Task {
            // 创建UserService实例来获取用户资料
            let networkService = NetworkServiceImpl(
                cookieManager: cookieManager,
                settingsManager: SettingsManager.shared
            )

            let userService = UserServiceImpl(
                networkService: networkService,
                cookieManager: cookieManager,
                viewContext: viewContext
            )

            let result = await userService.fetchCurrentUserProfile()

            await MainActor.run {
                isLoadingUserProfile = false

                switch result {
                case .success(let user):
                    print("【调试】UserHeaderView: ✅ 成功获取用户资料 - 用户名: \(user.username ?? "无"), 头像: \(user.avatar ?? "无")")
                    // 成功时从失败缓存中移除（如果存在）
                    if let activeIdentifier = cookieManager.activeUserIdentifier {
                        failedUserProfileRequests.remove(activeIdentifier)
                    }
                case .failure(let error):
                    print("【调试】UserHeaderView: ❌ 获取用户资料失败: \(error)")
                    // 将失败的用户标识符添加到缓存中，避免重复请求
                    if let activeIdentifier = cookieManager.activeUserIdentifier {
                        failedUserProfileRequests.insert(activeIdentifier)
                    }
                }
            }
        }
    }

    var body: some View {
        if let activeIdentifier = cookieManager.activeUserIdentifier,
            let activeUser = cookieManager.storedUsers[activeIdentifier],
            cookieManager.isCurrentlyLoggedIn
        {
            VStack(alignment: .center) {
                // 头像部分
                if let username = activeUser.username, !username.isEmpty {
                    // 检查是否是游客用户
                    if activeIdentifier == "guest_session" {
                        // 游客用户显示特殊的头像
                        HStack {
                            Spacer()
                            SimpleUserAvatarView.large(username: "游客")
                            Spacer()
                        }
                        .padding(.top, 15)
                    } else {
                        // 正常用户的头像逻辑
                        // 获取头像模板
                        let avatarTemplate = getUserAvatarTemplate(for: username)

                        // 使用头像模板显示头像（如果有的话）
                        if let template = avatarTemplate, !template.isEmpty {
                            HStack {
                                Spacer()
                                SimpleUserAvatarView.large(
                                    username: username,
                                    avatarURL: buildAvatarURL(fromTemplate: template)
                                )
                                .frame(width: 128, height: 128)
                                .clipped()
                                Spacer()
                            }
                            .padding(.top, 15)
                        } else {
                            // 如果没有头像模板，显示加载状态或默认头像
                            ZStack {
                                if isLoadingUserProfile {
                                    // 显示加载指示器
                                    HStack {
                                        Spacer()
                                        ProgressView()
                                            .scaleEffect(1.5)
                                            .frame(width: 128, height: 128)
                                        Spacer()
                                    }
                                    .padding(.top, 15)
                                } else {
                                    // 使用传统方式（向后兼容）
                                    HStack {
                                        Spacer()
                                        SimpleUserAvatarView.large(
                                            username: username
                                        )
                                        .frame(width: 128, height: 128)
                                        .clipped()
                                        Spacer()
                                    }
                                    .padding(.top, 15)
                                }
                            }
                            .onAppear {
                                // 当头像模板为空时，尝试获取用户资料
                                fetchCurrentUserProfile()
                            }
                        }
                    }
                } else {
                    // 默认头像
                    HStack {
                        Spacer()
                        Image(systemName: "person.fill.questionmark")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 128, height: 128)
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    .padding(.top, 15)
                }

                // 用户名显示 - 优先使用Core Data中的完整信息
                if let username = activeUser.username, !username.isEmpty {
                    let userInfo = getUserInfo(for: username)

                    // 显示用户的显示名称（如果有的话）
                    if let displayName = userInfo?.name, !displayName.isEmpty {
                        Text(displayName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding(.top, 5)
                    } else {
                        Text(username)
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding(.top, 5)
                    }

                    // 账号名称
                    Text("@\(username)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    // 用户头衔（如果有的话）
                    if let userTitle = userInfo?.usertitle, !userTitle.isEmpty {
                        Text(userTitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 15)
                    } else {
                        Spacer()
                            .frame(height: 15)
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .background(Color.clear)
            .onChange(of: cookieManager.activeUserIdentifier) { newActiveIdentifier in
                // 当用户切换时，清理失败缓存
                failedUserProfileRequests.removeAll()
                print("【调试】UserHeaderView: 用户切换，已清理失败请求缓存")
            }
        } else {
            // 未登录状态显示
            VStack(spacing: 12) {
                Image(systemName: "person.crop.circle.badge.exclamationmark")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
                    .foregroundColor(.secondary)
                    .padding(.top, 15)

                Text("未登录")
                    .font(.title3)
                    .fontWeight(.medium)

                Text("请登录后查看个人资料")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.bottom, 15)
            }
            .frame(maxWidth: .infinity)
            .background(Color.clear)
        }
    }
}
