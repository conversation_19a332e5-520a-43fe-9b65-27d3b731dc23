import SwiftUI

// MARK: - ActivitySectionView
// 活动版块视图（用于热门回复、热门话题）
struct ActivitySectionView: View {
    let title: String
    let items: [ActivityItem]

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 5)

            ForEach(items, id: \.title) { item in
                VStack(alignment: .leading) {
                    Text(item.title)
                        .font(.headline)
                    HStack {
                        Text(item.date)
                        Spacer()
                        Label("\(item.likes)", systemImage: "heart.fill")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding(.bottom, 5)
            }
            
            Button("更多\(title)") {}
                .font(.caption)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}
