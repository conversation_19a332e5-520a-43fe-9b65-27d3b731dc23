import SwiftUI

// MARK: - BadgeSectionView
// 徽章版块视图
struct BadgeSectionView: View {
    let title: String
    let items: [BadgeItem]

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 5)

            // 使用LazyVGrid来创建一个灵活的网格布局
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 150))], spacing: 20) {
                ForEach(items, id: \.name) { item in
                    HStack {
                        AsyncImage(url: URL(string: item.iconUrl)) { image in
                            image.resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 40, height: 40)
                        } placeholder: {
                            ProgressView()
                        }
                        .frame(width: 40, height: 40)

                        VStack(alignment: .leading) {
                            Text(item.name).fontWeight(.bold)
                            Text(item.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }
                    }
                    .padding()
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(10)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}
