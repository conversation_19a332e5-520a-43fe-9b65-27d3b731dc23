import SwiftUI

// MARK: - CategorySectionView
// 类别版块视图
@available(iOS 15.0, *)
struct CategorySectionView: View {
    let title: String
    let items: [ProfileCategoryItem]

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 5)

            VStack {
                HStack {
                    Text("类别").fontWeight(.bold)
                    Spacer()
                    Text("话题").fontWeight(.bold)
                    Spacer()
                    Text("回复").fontWeight(.bold)
                }
                .foregroundColor(.secondary)
                
                Divider()

                ForEach(items, id: \.name) { item in
                    HStack {
                        Text(item.name)
                        Spacer()
                        Text("\(item.topics)")
                        Spacer()
                        Text("\(item.replies)")
                    }
                    .padding(.vertical, 2)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}
