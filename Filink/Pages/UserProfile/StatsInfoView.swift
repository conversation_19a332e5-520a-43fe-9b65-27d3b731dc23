import SwiftUI

// MARK: - StatsInfoView
// 统计信息视图
@available(iOS 15.0, *)
struct StatsInfoView: View {
    let statsInfo: StatsInfo

    var body: some View {
        VStack(alignment: .leading) {
            Text("统计信息")
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 5)

            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    StatItem(value: "\(statsInfo.visitDays)", label: "访问天数")
                    Spacer()
                    StatItem(value: statsInfo.readTime, label: "阅读时间")
                    Spacer()
                    StatItem(value: statsInfo.recentReadTime, label: "最近阅读时间")
                }
                HStack {
                    StatItem(value: statsInfo.viewedPosts, label: "已读帖子")
                    Spacer()
                    StatItem(value: statsInfo.readPosts, label: "已读帖子")
                    Spacer()
                    StatItem(value: statsInfo.sentCount, label: "已送出")
                }
                HStack {
                    StatItem(value: statsInfo.receivedCount, label: "已收到")
                    Spacer()
                    StatItem(value: "\(statsInfo.createdTopics)", label: "创建的话题")
                    Spacer()
                    StatItem(value: "\(statsInfo.createdPosts)", label: "创建的帖子")
                }
                HStack {
                    StatItem(value: "\(statsInfo.solutions)", label: "解决方案")
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}

// MARK: - StatItem
// 单个统计项
struct StatItem: View {
    let value: String
    let label: String

    var body: some View {
        VStack(alignment: .leading) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}
