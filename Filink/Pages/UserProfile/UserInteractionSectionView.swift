import SwiftUI

// MARK: - UserInteractionSectionView
// 用户交互版块视图
struct UserInteractionSectionView: View {
    let mostRepliedTo: [UserInteraction]
    let mostLikedBy: [UserInteraction]
    let mostLikes: [UserInteraction]

    var body: some View {
        HStack(alignment: .top, spacing: 20) {
            InteractionListView(title: "最多回复至", items: mostRepliedTo)
            InteractionListView(title: "被赞最多", items: mostLikedBy)
            InteractionListView(title: "赞最多", items: mostLikes)
        }
    }
}

// MARK: - InteractionListView
// 单个交互列表
struct InteractionListView: View {
    let title: String
    let items: [UserInteraction]

    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.headline)
                .padding(.bottom, 5)

            ForEach(items, id: \.username) { item in
                HStack {
                    AsyncImage(url: URL(string: item.userAvatarUrl)) { image in
                        image.resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 30, height: 30)
                            .clipShape(Circle())
                    } placeholder: {
                        ProgressView()
                    }
                    .frame(width: 30, height: 30)
                    
                    Text(item.username)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Label("\(item.count)", systemImage: "heart.fill")
                }
                .font(.caption)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}
