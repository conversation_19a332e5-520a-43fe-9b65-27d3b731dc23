import SwiftUI

// MARK: - UserProfileHeaderView
// 用户信息头部视图
struct UserProfileHeaderView: View {
    let userInfo: UserInfo

    var body: some View {
        HStack(spacing: 15) {
            // 头像
            AsyncImage(url: URL(string: userInfo.avatarUrl)) { image in
                image.resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 80, height: 80)
                    .clipShape(Circle())
            } placeholder: {
                ProgressView()
            }
            .frame(width: 80, height: 80)

            VStack(alignment: .leading, spacing: 5) {
                // 昵称和用户名
                Text(userInfo.nickname).font(.largeTitle).fontWeight(.bold)
                Text("@\(userInfo.username)").font(.headline).foregroundColor(.secondary)
                Text(userInfo.description).font(.subheadline).foregroundColor(.gray)
                
                // 加入日期等信息
                HStack {
                    Text("加入日期: \(userInfo.joinDate)")
                    Text("最后一次发帖: \(userInfo.lastPostDate)")
                    Text("最后活动: \(userInfo.lastActivityDate)")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 操作按钮
            VStack(spacing: 10) {
                Button("私信") {}
                    .buttonStyle(.borderedProminent)
                Button("聊天") {}
                    .buttonStyle(.borderedProminent)
                Button("关注") {}
                    .buttonStyle(.bordered)
            }
        }
    }
}
