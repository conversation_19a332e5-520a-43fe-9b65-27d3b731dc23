import Foundation

// MARK: - UserProfileModel
// 用户个人信息页面的数据模型
struct UserProfileModel {
    let userInfo: UserInfo
    let statsInfo: StatsInfo
    let hotReplies: [ActivityItem]
    let hotTopics: [ActivityItem]
    let hotLinks: [LinkItem]
    let mostRepliedTo: [UserInteraction]
    let mostLikedBy: [UserInteraction]
    let mostLikes: [UserInteraction]
    let hotCategories: [ProfileCategoryItem]
    let hotBadges: [BadgeItem]
}

// MARK: - UserInfo
// 用户基本信息
struct UserInfo {
    let avatarUrl: String
    let nickname: String
    let username: String
    let description: String
    let joinDate: String
    let lastPostDate: String
    let lastActivityDate: String
}

// MARK: - StatsInfo
// 统计信息
struct StatsInfo {
    let visitDays: Int
    let readTime: String
    let recentReadTime: String
    let viewedPosts: String
    let readPosts: String
    let sentCount: String
    let receivedCount: String
    let createdTopics: Int
    let createdPosts: Int
    let solutions: Int
}

// MARK: - ActivityItem
// 活动项目（如热门回复、热门话题）
struct ActivityItem {
    let title: String
    let date: String
    let likes: Int
}

// MARK: - LinkItem
// 链接项目
struct LinkItem {
    let title: String
    let url: String
    let count: Int
}

// MARK: - UserInteraction
// 用户交互信息（如最多回复、被赞最多）
struct UserInteraction {
    let userAvatarUrl: String
    let username: String
    let count: Int
}

// MARK: - ProfileCategoryItem
// 类别项目
struct ProfileCategoryItem {
    let name: String
    let topics: Int
    let replies: Int
}

// MARK: - BadgeItem
// 徽章项目
struct BadgeItem {
    let name: String
    let description: String
    let iconUrl: String
}
