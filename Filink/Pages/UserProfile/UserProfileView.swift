import SwiftUI

// MARK: - UserProfileView
// 个人信息主页面
struct UserProfileView: View {
    @StateObject private var viewModel: UserProfileViewModel
    
    // 从外部传入用户名
    let username: String

    init(username: String, userService: UserService) {
        self.username = username
        _viewModel = StateObject(wrappedValue: UserProfileViewModel(userService: userService))
    }

    var body: some View {
        ScrollView {
            if viewModel.isLoading {
                ProgressView()
            } else if let userProfile = viewModel.userProfile {
                VStack(alignment: .leading, spacing: 20) {
                    // 用户信息头部
                    UserProfileHeaderView(userInfo: userProfile.userInfo)
                    
                    // 统计信息
                    StatsInfoView(statsInfo: userProfile.statsInfo)
                    
                    // 热门回复和热门话题
                    HStack(alignment: .top, spacing: 20) {
                        ActivitySectionView(title: "热门回复", items: userProfile.hotReplies)
                        ActivitySectionView(title: "热门话题", items: userProfile.hotTopics)
                    }
                    
                    // 热门链接
                    LinkSectionView(title: "热门链接", items: userProfile.hotLinks)
                    
                    // 用户交互信息
                    UserInteractionSectionView(
                        mostRepliedTo: userProfile.mostRepliedTo,
                        mostLikedBy: userProfile.mostLikedBy,
                        mostLikes: userProfile.mostLikes
                    )
                    
                    // 热门类别
                    CategorySectionView(title: "热门类别", items: userProfile.hotCategories)
                    
                    // 热门徽章
                    BadgeSectionView(title: "热门徽章", items: userProfile.hotBadges)
                }
                .padding()
            } else if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .padding()
            }
        }
        .navigationTitle("个人信息")
        .onAppear {
            viewModel.loadUserProfile(username: username)
        }
    }
}

// MARK: - 预览
struct UserProfileView_Previews: PreviewProvider {
    static var previews: some View {
        // 在预览中，我们需要一个模拟的UserService
        let networkService = NetworkServiceImpl()
        let cookieManager = CookieManager.shared
        let settingsManager = SettingsManager.shared
        let viewContext = CoreDataManager.shared.container.viewContext
        let userService = UserServiceImpl(networkService: networkService, cookieManager: cookieManager, settingsManager: settingsManager, viewContext: viewContext)
        
        return UserProfileView(username: "steven07", userService: userService)
    }
}
