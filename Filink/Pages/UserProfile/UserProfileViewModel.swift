import Foundation

@MainActor
class UserProfileViewModel: ObservableObject {
    @Published var userProfile: UserProfileModel?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let userService: UserService
    
    init(userService: UserService) {
        self.userService = userService
    }

    func loadUserProfile(username: String) {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let response = try await userService.fetchUserProfileSummary(username: username)
                self.userProfile = transform(response: response)
            } catch {
                self.errorMessage = "加载用户数据失败: \(error.localizedDescription)"
                print("Error loading user profile: \(error)")
            }
            isLoading = false
        }
    }

    private func transform(response: UserSummaryResponse) -> UserProfileModel {
        // 查找主用户信息
        let mainUser = response.users.first { $0.username == "steven07" }

        // 转换 UserInfo
        let userInfo = UserInfo(
            avatarUrl: mainUser?.avatarTemplate.replacingOccurrences(of: "{size}", with: "120") ?? "",
            nickname: mainUser?.name ?? "N/A",
            username: mainUser?.username ?? "N/A",
            description: response.badges.first(where: { $0.id == 119 })?.description ?? "", // "蛇来运转"
            joinDate: "N/A", // JSON中没有直接提供
            lastPostDate: "N/A", // JSON中没有直接提供
            lastActivityDate: "N/A" // JSON中没有直接提供
        )

        // 转换 StatsInfo
        let summary = response.userSummary
        let statsInfo = StatsInfo(
            visitDays: summary.daysVisited,
            readTime: formatTime(seconds: summary.timeRead),
            recentReadTime: formatTime(seconds: summary.recentTimeRead),
            viewedPosts: formatNumber(summary.postsReadCount),
            readPosts: formatNumber(summary.postsReadCount), // JSON中只有一个阅读计数
            sentCount: formatNumber(summary.likesGiven),
            receivedCount: formatNumber(summary.likesReceived),
            createdTopics: summary.topicCount,
            createdPosts: summary.postCount,
            solutions: summary.solvedCount
        )

        // 转换热门回复
        let hotReplies = summary.replies.map { reply -> ActivityItem in
            let topic = response.topics.first { $0.id == reply.topicID }
            return ActivityItem(
                title: topic?.title ?? "未知话题",
                date: formatDate(reply.createdAt),
                likes: reply.likeCount
            )
        }

        // 转换热门话题
        let hotTopics = response.topics.prefix(6).map { topic in
            ActivityItem(
                title: topic.title,
                date: formatDate(topic.createdAt),
                likes: topic.likeCount
            )
        }

        // 转换热门链接
        let hotLinks = summary.links.map { link in
            LinkItem(
                title: link.title ?? link.url,
                url: link.url,
                count: link.clicks
            )
        }

        // 转换用户交互
        let mostRepliedTo = summary.mostRepliedToUsers.map { user in
            UserInteraction(
                userAvatarUrl: user.avatarTemplate.replacingOccurrences(of: "{size}", with: "40"),
                username: user.name,
                count: user.count
            )
        }
        
        let mostLikedBy = summary.mostLikedByUsers.map { user in
            UserInteraction(
                userAvatarUrl: user.avatarTemplate.replacingOccurrences(of: "{size}", with: "40"),
                username: user.name,
                count: user.count
            )
        }

        let mostLikes = summary.mostLikedUsers.map { user in
            UserInteraction(
                userAvatarUrl: user.avatarTemplate.replacingOccurrences(of: "{size}", with: "40"),
                username: user.name,
                count: user.count
            )
        }

        // 转换热门类别
        let hotCategories = summary.topCategories.map { category in
            ProfileCategoryItem(
                name: category.name,
                topics: category.topicCount,
                replies: category.postCount
            )
        }

        // 转换热门徽章
        let grantedBadgeIDs = Set(summary.badges.map { $0.badgeID })
        let hotBadges = response.badges.filter { grantedBadgeIDs.contains($0.id) }.map { badge in
            BadgeItem(
                name: badge.name,
                description: badge.description,
                iconUrl: badge.imageURL ?? ""
            )
        }

        return UserProfileModel(
            userInfo: userInfo,
            statsInfo: statsInfo,
            hotReplies: Array(hotReplies.prefix(6)),
            hotTopics: Array(hotTopics),
            hotLinks: Array(hotLinks.prefix(6)),
            mostRepliedTo: Array(mostRepliedTo.prefix(6)),
            mostLikedBy: Array(mostLikedBy.prefix(6)),
            mostLikes: Array(mostLikes.prefix(6)),
            hotCategories: Array(hotCategories),
            hotBadges: Array(hotBadges)
        )
    }
    
    // MARK: - Helper Functions
    private func formatTime(seconds: Int) -> String {
        let minutes = seconds / 60
        if minutes < 60 {
            return "\(minutes)分钟"
        }
        let hours = minutes / 60
        if hours < 24 {
            return "\(hours)小时"
        }
        let days = hours / 24
        return "\(days)天"
    }

    private func formatNumber(_ number: Int) -> String {
        if number < 1000 {
            return "\(number)"
        }
        let kValue = Double(number) / 1000.0
        return String(format: "%.1fk", kValue)
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "yyyy年 M月 d日"
            return displayFormatter.string(from: date)
        }
        return "N/A"
    }
}
