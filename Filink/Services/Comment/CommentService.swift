import Foundation
import CoreData
import Combine

/// 评论服务接口
@MainActor
protocol CommentService {
    /// 获取指定评论列表（异步）
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - commentIds: 评论ID列表
    /// - Returns: 评论列表结果
    @available(iOS 15.0, *)
    func fetchSpecificComments(
        postId: String,
        commentIds: [Int64]
    ) async -> Result<[Comment], NetworkError>
    
    /// 发布评论（异步）
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - content: 评论内容
    ///   - replyToCommentId: 回复的评论ID（可选）
    /// - Returns: 评论结果
    @available(iOS 15.0, *)
    func postComment(
        postId: String,
        content: String,
        replyToCommentId: Int64?
    ) async -> Result<Comment, NetworkError>
    
    /// 发布观察状态
    var isLoading: Bool { get }
    var errorMessage: String? { get }
    
    /// 发布者
    var loadingPublisher: Published<Bool>.Publisher { get }
    var errorMessagePublisher: Published<String?>.Publisher { get }
} 