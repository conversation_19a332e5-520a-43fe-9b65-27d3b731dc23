import Foundation
import CoreData
import Combine

/// CommentService实现类
@MainActor
class CommentServiceImpl: CommentService, ObservableObject, ErrorHandler, NonisolatedCustomErrorMessageProvider {
    // 依赖注入
    private let networkService: NetworkService
    private let viewContext: NSManagedObjectContext
    
    // 状态管理
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    var loadingPublisher: Published<Bool>.Publisher { $isLoading }
    var errorMessagePublisher: Published<String?>.Publisher { $errorMessage }
    
    // 初始化
    init(networkService: NetworkService, viewContext: NSManagedObjectContext) {
        self.networkService = networkService
        self.viewContext = viewContext
    }
    
    // MARK: - ErrorHandler Protocol Implementation
    
    func getImplementationName() -> String {
        return "CommentServiceImpl"
    }
    
    // MARK: - NonisolatedCustomErrorMessageProvider Protocol Implementation
    
    nonisolated func customErrorMessageForStatusCode(_ statusCode: Int) -> String? {
        if statusCode == 404 {
            return "评论不存在 (状态码: \(statusCode))"
        }
        return nil
    }
    
    // MARK: - CommentService Protocol Implementation
    
    @available(iOS 15.0, *)
    func fetchSpecificComments(
        postId: String,
        commentIds: [Int64]
    ) async -> Result<[Comment], NetworkError> {
        // 更新状态
        isLoading = true
        errorMessage = nil

        // 构建参数
        var parameters: [String: Any] = [:]

        // 将每个评论ID作为单独的post_ids[]参数
        // NetworkService的RequestBuilder需要能够处理这种数组，将其展开为多个同名参数
        parameters["post_ids[]"] = commentIds.map { "\($0)" }

        // 添加 include_suggested 参数
        parameters["include_suggested"] = "true"
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "t/\(postId)/posts.json",
            method: .get,
            parameters: parameters,
            headers: nil
        )
        
        // 重置状态
        isLoading = false
        
        switch result {
        case .success(let data):
            do {
                // 解析数据
                let comments = try processSpecificCommentsData(data: data, postId: postId)
                return .success(comments)
            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
                return .failure(.decodingError(error))
            }
            
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }
    
    @available(iOS 15.0, *)
    func postComment(
        postId: String,
        content: String,
        replyToCommentId: Int64?
    ) async -> Result<Comment, NetworkError> {
        
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 构建参数
        var parameters: [String: Any] = [
            "raw": content,
            "topic_id": postId
        ]
        
        if let replyToCommentId = replyToCommentId {
            parameters["reply_to_post_number"] = replyToCommentId
        }
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "posts",
            method: .post,
            parameters: parameters,
            headers: nil
        )
        
        // 重置状态
        isLoading = false
        
        switch result {
        case .success(let data):
            do {
                // 解析数据
                let comment = try processCommentPostData(data: data, postId: postId)
                return .success(comment)
            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
                return .failure(.decodingError(error))
            }
            
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }
    
    // MARK: - Private Helper Methods
    
    private func processSpecificCommentsData(data: Data, postId: String) throws -> [Comment] {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            

            
            // 尝试解析为不同的响应格式
            var commentPosts: [PostData] = []
            
            do {
                // 尝试解析为 PostDetailResponse
                let postResponse = try decoder.decode(PostDetailResponse.self, from: data)
                commentPosts = postResponse.postStream.posts
            } catch {
                do {
                    // 尝试解析为 MoreCommentsResponse
                    let moreCommentsResponse = try decoder.decode(MoreCommentsResponse.self, from: data)
                    commentPosts = moreCommentsResponse.postStream.posts
                } catch {
                    do {
                        // 尝试解析为单个评论
                        let singleComment = try decoder.decode(CommentDetailResponse.self, from: data)
                        commentPosts = [PostData(
                            id: singleComment.id,
                            userId: singleComment.userId,
                            username: singleComment.username,
                            name: nil,
                            avatarTemplate: singleComment.avatarTemplate,
                            createdAt: singleComment.createdAt,
                            updatedAt: singleComment.updatedAt,
                            cooked: singleComment.cooked,
                            postNumber: singleComment.postNumber,
                            replyCount: nil,
                            likeCount: nil,
                            replyToPostNumber: nil,
                            plainTextContent: nil,
                            displayUsername: nil,
                            userTitle: nil,
                            shortContent: nil,
                            linkCounts: nil
                        )]
                    } catch {
                        // 尝试解析可能的错误消息或评论ID数组
                        
                        // 尝试从Core Data获取评论
                        if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                           let commentIds = jsonObject["post_ids"] as? [Int] {

                            // 尝试直接获取这些评论
                            var comments: [Comment] = []
                            for commentId in commentIds {
                                let fetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
                                fetchRequest.predicate = NSPredicate(format: "id == %lld", Int64(commentId))
                                fetchRequest.fetchLimit = 1

                                if let existingComment = try? viewContext.fetch(fetchRequest).first {
                                    comments.append(existingComment)
                                }
                            }

                            if !comments.isEmpty {
                                return comments
                            }
                        }
                        
                        throw NetworkError.decodingError(NSError(domain: "ParsingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "解析特定评论数据失败，未找到评论数组"]))
                    }
                }
            }
            
            var comments: [Comment] = []
            let postIdInt64 = Int64(postId) ?? 0
            
            // 处理每条评论
            for postData in commentPosts {
                // 直接使用 PostData 中已解析的日期，避免重复转换
                let comment = try saveOrUpdateCommentEntity(from: postData, for: postIdInt64)
                comments.append(comment)
            }

            // 保存Context
            if viewContext.hasChanges {
                try viewContext.save()
            }

            return comments

        } catch {
            throw error
        }
    }
    
    private func processCommentPostData(data: Data, postId: String) throws -> Comment {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            
            // 解码为 PostData
            let postData = try decoder.decode(PostData.self, from: data)

            let postIdInt64 = Int64(postId) ?? 0

            // 直接使用 PostData，避免重复转换
            let comment = try saveOrUpdateCommentEntity(from: postData, for: postIdInt64)
            
            // 保存Context
            if viewContext.hasChanges {
                try viewContext.save()
            }

            return comment

        } catch {
            throw error
        }
    }
    
    // 保存或更新评论实体 - 直接从 PostData
    internal func saveOrUpdateCommentEntity(from postData: PostData, for postId: Int64) throws -> Comment {
        // 处理评论用户信息 - 转换为字典格式以使用现有方法
        let userDict: [String: Any] = [
            "user_id": postData.userId,
            "username": postData.username,
            "name": postData.name ?? postData.username,
            "avatar_template": postData.avatarTemplate ?? "",
            "display_username": postData.displayUsername ?? postData.username,
            "user_title": postData.userTitle ?? ""
        ]
        _ = UserServiceImpl.extractUserFromPost(from: userDict, context: viewContext)

        // 提取评论数据
        let id = Int64(postData.id)
        let userId = postData.userId
        let postNumber = postData.postNumber
        let cooked = postData.cooked

        // 直接使用已解析的日期
        let commentDate = postData.createdAt // 优先使用 createdAt



        // 提取可选字段
        let plainTextContent = postData.plainTextContent
        let replyCount = postData.replyCount ?? 0
        let likeCount = postData.likeCount ?? 0

        // 提取回复信息
        let replyToPostNumber = Int32(postData.replyToPostNumber ?? 0)
        let replyToUserId: Int64 = 0 // PostData中没有直接的replyToUserId字段
        let shortContent = postData.shortContent

        // 查找或创建评论实体
        let fetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", id)
        fetchRequest.fetchLimit = 1

        let comments = try viewContext.fetch(fetchRequest)
        let comment: Comment

        if let existingComment = comments.first {
            // 更新现有评论
            comment = existingComment
        } else {
            // 创建新评论
            comment = Comment(context: viewContext)
            comment.id = id
        }

        // 更新评论属性
        comment.userId = userId
        comment.postNumber = Int32(postNumber)
        comment.cooked = cooked
        comment.plain_text_content = plainTextContent
        comment.updated_at = commentDate // 存储创建时间到updated_at字段
        comment.reply_count = Int32(replyCount)
        comment.like_count = Int32(likeCount)
        comment.replyToPostNumber = replyToPostNumber
        comment.replyToUserId = replyToUserId
        comment.shortContent = shortContent

        // 关联到主帖子
        let postFetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        postFetchRequest.predicate = NSPredicate(format: "id == %lld", postId)
        postFetchRequest.fetchLimit = 1

        if let post = try viewContext.fetch(postFetchRequest).first {
            comment.post = post
        }

        return comment
    }

    // 保存或更新评论实体 - 从字典（保留原有方法以兼容性）
    internal func saveOrUpdateCommentEntity(from commentData: [String: Any], for postId: Int64, dateFormatter: ISO8601DateFormatter) throws -> Comment {
        // 处理评论用户信息
        _ = UserServiceImpl.extractUserFromPost(from: commentData, context: viewContext)
        
        // 提取评论数据
        guard let id = commentData["id"] as? Int,
              let userId = commentData["user_id"] as? Int,
              let postNumber = commentData["post_number"] as? Int,
              let cooked = commentData["cooked"] as? String else {
            throw NetworkError.decodingError(NSError(domain: "ParsingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法提取评论基本数据"]))
        }
        
        // 优先使用 created_at，如果没有则使用 updated_at
        let createdAtString = commentData["created_at"] as? String
        let updatedAtString = commentData["updated_at"] as? String
        var commentDate: Date? = nil

        // 优先解析 created_at
        if let createdAtString = createdAtString {
            commentDate = Date.flexibleDateParser(from: createdAtString)
        }

        // 如果 created_at 解析失败或不存在，则使用 updated_at
        if commentDate == nil, let updatedAtString = updatedAtString {
            commentDate = Date.flexibleDateParser(from: updatedAtString)
        }

        // 如果都解析失败，使用当前时间
        if commentDate == nil {
            commentDate = Date()
        }
        
        // 提取可选字段
        let plainTextContent = commentData["plain_text_content"] as? String
        let replyCount = commentData["reply_count"] as? Int ?? 0
        let likeCount = commentData["like_count"] as? Int ?? 0
        
        // 提取回复信息
        var replyToPostNumber: Int32 = 0
        var replyToUserId: Int64 = 0
        
        if let replyToPost = commentData["reply_to_post_number"] as? Int {
            replyToPostNumber = Int32(replyToPost)
        }
        
        if let replyToUser = commentData["reply_to_user"] as? [String: Any],
           let replyToUserIdInt = replyToUser["id"] as? Int {
            replyToUserId = Int64(replyToUserIdInt)
        }
        
        let shortContent = commentData["short_content"] as? String
        
        // 查找或创建评论实体
        let fetchRequest: NSFetchRequest<Comment> = Comment.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", Int64(id))
        fetchRequest.fetchLimit = 1
        
        let comments = try viewContext.fetch(fetchRequest)
        let comment: Comment
        
        if let existingComment = comments.first {
            // 更新现有评论
            comment = existingComment
        } else {
            // 创建新评论
            comment = Comment(context: viewContext)
            comment.id = Int64(id)
        }
        
        // 更新评论属性
        comment.userId = Int64(userId)
        comment.postNumber = Int32(postNumber)
        comment.cooked = cooked
        comment.plain_text_content = plainTextContent
        comment.updated_at = commentDate // 使用优先选择的日期（created_at 或 updated_at）
        comment.reply_count = Int32(replyCount)
        comment.like_count = Int32(likeCount)
        comment.replyToPostNumber = replyToPostNumber
        comment.replyToUserId = replyToUserId
        comment.shortContent = shortContent
        
        // 关联到主帖子
        let postFetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
        postFetchRequest.predicate = NSPredicate(format: "id == %lld", postId)
        postFetchRequest.fetchLimit = 1
        
        if let post = try viewContext.fetch(postFetchRequest).first {
            comment.post = post
        }
        
        return comment
    }
}
