import Foundation

// MARK: - 帖子列表响应模型
struct LatestPostsResponse: Codable {
    let users: [DiscourseUser]
    let topicList: TopicList
    
    enum CodingKeys: String, CodingKey {
        case users
        case topicList = "topic_list"
    }
}

struct TopicList: Codable {
    let topics: [TopicData]
}

struct TopicData: Codable {
    let id: Int64
    let title: String
    let postsCount: Int32
    let replyCount: Int32
    let highestPostNumber: Int32
    let imageUrl: String?
    let createdAt: Date
    let lastPostedAt: Date?
    let isPinned: Bool
    let excerpt: String?
    let tags: [String]?
    let views: Int32
    let likeCount: Int32
    let categoryId: Int64
    let canVote: Bool
    let posters: [PosterData]?
    
    enum CodingKeys: String, CodingKey {
        case id, title, excerpt, tags, views, posters
        case postsCount = "posts_count"
        case replyCount = "reply_count"
        case highestPostNumber = "highest_post_number"
        case imageUrl = "image_url"
        case createdAt = "created_at"
        case lastPostedAt = "last_posted_at"
        case isPinned = "pinned"
        case likeCount = "like_count"
        case categoryId = "category_id"
        case canVote = "can_vote"
    }
}

struct PosterData: Codable {
    let userId: Int64
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
    }
}

// MARK: - 帖子详情响应模型
struct PostDetailResponse: Codable {
    let title: String?
    let postStream: PostStream

    enum CodingKeys: String, CodingKey {
        case title
        case postStream = "post_stream"
    }
}

struct PostStream: Codable {
    let posts: [PostData]
    let stream: [Int]?
}

struct PostData: Codable {
    let id: Int64
    let userId: Int64
    let username: String
    let name: String?
    let avatarTemplate: String?
    let createdAt: Date
    let updatedAt: Date?
    let cooked: String
    let postNumber: Int
    let replyCount: Int?
    let likeCount: Int?
    let replyToPostNumber: Int?
    let plainTextContent: String?
    let displayUsername: String?
    let userTitle: String?
    let shortContent: String?
    let linkCounts: [LinkCount]?
    
    enum CodingKeys: String, CodingKey {
        case id, username, name, cooked
        case userId = "user_id"
        case avatarTemplate = "avatar_template"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case postNumber = "post_number"
        case replyCount = "reply_count"
        case likeCount = "like_count"
        case replyToPostNumber = "reply_to_post_number"
        case plainTextContent = "plain_text_content"
        case displayUsername = "display_username"
        case userTitle = "user_title"
        case shortContent = "short_content"
        case linkCounts = "link_counts"
    }
}

struct LinkCount: Codable {
    let url: String
    let isInternal: Bool?
    let reflection: Bool?
    let title: String?
    let clicks: Int
    
    // 从URL提取帖子ID
    var postId: String? {
        // 假设URL格式为 https://linux.do/t/topic/684045/2
        let components = url.split(separator: "/")
        if components.count >= 5, let topicIndex = components.firstIndex(of: "topic") {
            if topicIndex + 1 < components.count {
                // 提取ID并移除可能的页码
                let idWithPage = String(components[topicIndex + 1])
                return idWithPage.components(separatedBy: "#").first
            }
        }
        return nil
    }
    
    enum CodingKeys: String, CodingKey {
        case url, reflection, title, clicks
        case isInternal = "internal"
    }
}

// MARK: - 用户数据模型
struct DiscourseUser: Codable {
    let id: Int64
    let username: String
    let name: String?
    let avatarTemplate: String?
    let displayUsername: String?
    let title: String?
    
    enum CodingKeys: String, CodingKey {
        case id, username, name, title
        case avatarTemplate = "avatar_template"
        case displayUsername = "display_username"
    }
}

// MARK: - 更多评论响应模型
struct MoreCommentsResponse: Codable {
    let postStream: PostStream
    
    enum CodingKeys: String, CodingKey {
        case postStream = "post_stream"
    }
}

// MARK: - 用户资料响应模型
struct PostUserProfileResponse: Codable {
    let user: UserProfileData
}

struct UserProfileData: Codable {
    let id: Int64
    let username: String
    let name: String?
    let avatarTemplate: String?
    let displayUsername: String?
    let title: String?
    let bioRaw: String?
    
    enum CodingKeys: String, CodingKey {
        case id, username, name, title
        case avatarTemplate = "avatar_template"
        case displayUsername = "display_username"
        case bioRaw = "bio_raw"
    }
}

// MARK: - 当前用户响应模型
struct CurrentUserResponse: Codable {
    let currentUser: UserProfileData
    
    enum CodingKeys: String, CodingKey {
        case currentUser = "current_user"
    }
}

// MARK: - 评论详情响应模型
struct CommentDetailResponse: Codable {
    let id: Int64
    let topicId: Int64
    let userId: Int64
    let username: String
    let avatarTemplate: String?
    let cooked: String
    let postNumber: Int
    let createdAt: Date
    let updatedAt: Date?
    
    enum CodingKeys: String, CodingKey {
        case id, username, cooked
        case topicId = "topic_id"
        case userId = "user_id"
        case avatarTemplate = "avatar_template"
        case postNumber = "post_number"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}
