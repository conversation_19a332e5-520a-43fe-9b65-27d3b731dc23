import Foundation

// MARK: - UserSummaryResponse
// 用于解析用户摘要API响应的Codable模型
struct UserSummaryResponse: Codable {
    let topics: [Topic]
    let badges: [Badge]
    let users: [APIUser]
    let userSummary: UserSummary

    enum CodingKeys: String, CodingKey {
        case topics, badges, users
        case userSummary = "user_summary"
    }
}

// MARK: - UserProfileAPIResponse
struct UserProfileAPIResponse: Codable {
    let user: APIUser
}

// MARK: - Badge
struct Badge: Codable {
    let id: Int
    let name: String
    // 注意：description可能包含HTML标签，需要处理
    let description: String
    let icon: String?
    let imageURL: String?

    enum CodingKeys: String, CodingKey {
        case id, name, description, icon
        case imageURL = "image_url"
    }
}

// MARK: - Topic
struct Topic: Codable {
    let id: Int
    let title: String
    let likeCount: Int
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id, title
        case likeCount = "like_count"
        case createdAt = "created_at"
    }
}

// MARK: - APIUser
struct APIUser: Codable {
    let id: Int
    let username: String
    let name: String
    let avatarTemplate: String

    enum CodingKeys: String, CodingKey {
        case id, username, name
        case avatarTemplate = "avatar_template"
    }
}

// MARK: - UserSummary
struct UserSummary: Codable {
    let likesGiven: Int
    let likesReceived: Int
    let postsReadCount: Int
    let daysVisited: Int
    let topicCount: Int
    let postCount: Int
    let timeRead: Int
    let recentTimeRead: Int
    let solvedCount: Int
    let replies: [Reply]
    let links: [Link]
    let mostLikedByUsers: [InteractionUser]
    let mostLikedUsers: [InteractionUser]
    let mostRepliedToUsers: [InteractionUser]
    let badges: [GrantedBadge]
    let topCategories: [TopCategory]

    enum CodingKeys: String, CodingKey {
        case likesGiven = "likes_given"
        case likesReceived = "likes_received"
        case postsReadCount = "posts_read_count"
        case daysVisited = "days_visited"
        case topicCount = "topic_count"
        case postCount = "post_count"
        case timeRead = "time_read"
        case recentTimeRead = "recent_time_read"
        case solvedCount = "solved_count"
        case replies, links
        case mostLikedByUsers = "most_liked_by_users"
        case mostLikedUsers = "most_liked_users"
        case mostRepliedToUsers = "most_replied_to_users"
        case badges
        case topCategories = "top_categories"
    }
}

// MARK: - GrantedBadge
struct GrantedBadge: Codable {
    let badgeID: Int

    enum CodingKeys: String, CodingKey {
        case badgeID = "badge_id"
    }
}

// MARK: - Link
struct Link: Codable {
    let url: String
    let title: String?
    let clicks: Int
    let topicID: Int

    enum CodingKeys: String, CodingKey {
        case url, title, clicks
        case topicID = "topic_id"
    }
}

// MARK: - InteractionUser
struct InteractionUser: Codable {
    let id: Int
    let username: String
    let name: String
    let count: Int
    let avatarTemplate: String

    enum CodingKeys: String, CodingKey {
        case id, username, name, count
        case avatarTemplate = "avatar_template"
    }
}

// MARK: - Reply
struct Reply: Codable {
    let likeCount: Int
    let createdAt: String
    let topicID: Int

    enum CodingKeys: String, CodingKey {
        case likeCount = "like_count"
        case createdAt = "created_at"
        case topicID = "topic_id"
    }
}

// MARK: - TopCategory
struct TopCategory: Codable {
    let topicCount: Int
    let postCount: Int
    let id: Int
    let name: String

    enum CodingKeys: String, CodingKey {
        case topicCount = "topic_count"
        case postCount = "post_count"
        case id, name
    }
}
