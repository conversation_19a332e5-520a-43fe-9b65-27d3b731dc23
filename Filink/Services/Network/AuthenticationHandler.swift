import Foundation

/// 认证处理器，用于处理认证相关功能
class AuthenticationHandler {
    private let cookieManager: CookieManager
    
    init(cookieManager: CookieManager) {
        self.cookieManager = cookieManager
    }
    
    /// 为请求添加认证信息
    /// - Parameter request: URLRequest
    /// - Returns: 添加了认证信息的URLRequest
    func authenticateRequest(_ request: inout URLRequest) -> Bool {
        guard let activeIdentifier = cookieManager.activeUserIdentifier else {
            print("【调试】⚠️ AuthenticationHandler: 没有活动用户")
            return false
        }
        
        let cookies = cookieManager.activeCookies
        if !cookies.isEmpty && areAllRequiredCookiesPresent(in: cookies) {
            let cookieString = cookies.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
            request.setValue(cookieString, forHTTPHeaderField: "Cookie")
            print("【调试】✅ AuthenticationHandler: 为用户 '\(activeIdentifier)' 添加认证信息")
            return true
        } else {
            print("【调试】⚠️ AuthenticationHandler: 用户 '\(activeIdentifier)' 没有所有必需的cookies")
            GlobalToastPresenter.shared.showError("认证失败：缺少必要的 cookies")
            return false
        }
    }
    
    /// 处理响应中的认证信息
    /// - Parameters:
    ///   - response: HTTP响应
    ///   - activeIdentifier: 当前活动用户标识符
    func handleAuthenticationResponse(_ response: HTTPURLResponse, activeIdentifier: String) {
        // 从响应头中提取用户名
        let headerKey = "x-discourse-username"
        if let extractedUsername = response.value(forHTTPHeaderField: headerKey) {
            cookieManager.updateUsernameForUser(
                identifier: activeIdentifier,
                username: extractedUsername
            )
            print("【调试】✅ AuthenticationHandler: 更新用户 '\(activeIdentifier)' 的用户名为 '\(extractedUsername)'")
        }
        
        // 处理认证失败
        if response.statusCode == 401 || response.statusCode == 403 {
            print("【调试】⚠️ AuthenticationHandler: 收到 \(response.statusCode) 未授权/禁止访问")
            cookieManager.updateUsernameForUser(identifier: activeIdentifier, username: nil)
            GlobalToastPresenter.shared.showError("认证失败或无权限 (状态码: \(response.statusCode))")
        }
    }
    
    /// 检查所有必需的Cookie是否存在
    /// - Parameter cookies: Cookie字典
    /// - Returns: 是否所有必需的Cookie都存在
    private func areAllRequiredCookiesPresent(in cookies: [String: String]) -> Bool {
        // 获取当前活动用户标识符
        guard let activeIdentifier = cookieManager.activeUserIdentifier else {
            return false
        }
        
        // 对于游客用户（guest_session），只检查 cf_clearance cookie
        if activeIdentifier == "guest_session" {
            return cookies["cf_clearance"] != nil && !(cookies["cf_clearance"]?.isEmpty ?? true)
        } else {
            // 对于已登录用户，检查所有必需的 cookies
            return cookieManager.requiredCookieNames.allSatisfy { requiredName in
                cookies[requiredName] != nil && !(cookies[requiredName]?.isEmpty ?? true)
            }
        }
    }
    
    /// 获取当前认证状态
    /// - Returns: 认证状态
    func getAuthenticationStatus() -> AuthenticationStatus {
        guard let activeIdentifier = cookieManager.activeUserIdentifier else {
            return .unauthenticated
        }
        
        let cookies = cookieManager.activeCookies
        if !cookies.isEmpty && areAllRequiredCookiesPresent(in: cookies) {
            return .authenticated(userId: activeIdentifier)
        } else {
            GlobalToastPresenter.shared.showError("认证状态不完整：缺少必要的 cookies")
            return .incomplete(userId: activeIdentifier)
        }
    }
}

/// 认证状态枚举
enum AuthenticationStatus {
    case unauthenticated
    case incomplete(userId: String)
    case authenticated(userId: String)
    
    var isAuthenticated: Bool {
        switch self {
        case .authenticated:
            return true
        case .unauthenticated, .incomplete:
            return false
        }
    }
    
    var userId: String? {
        switch self {
        case .authenticated(let userId), .incomplete(let userId):
            return userId
        case .unauthenticated:
            return nil
        }
    }
} 