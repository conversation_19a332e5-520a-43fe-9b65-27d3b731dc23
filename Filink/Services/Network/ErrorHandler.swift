import Foundation
import Combine

/// 错误处理器协议，用于统一处理网络错误
@MainActor
protocol ErrorHandler: AnyObject {
    /// 错误消息
    var errorMessage: String? { get set }
    
    /// 获取实现类名称，用于日志记录
    func getImplementationName() -> String
    
    /// 处理网络错误
    func handleNetworkError(_ error: NetworkError)
}

/// 自定义错误消息提供者（非隔离版本，兼容Swift 6）
protocol NonisolatedCustomErrorMessageProvider {
    /// 获取指定状态码的自定义错误消息
    nonisolated func customErrorMessageForStatusCode(_ statusCode: Int) -> String?
}

/// 默认实现
extension ErrorHandler {
    /// 处理网络错误
    func handleNetworkError(_ error: NetworkError) {
        // 处理网络错误
        switch error {
        case .invalidURL:
            errorMessage = "内部错误：无效的URL"
        case .networkError(let underlyingError):
            errorMessage = "网络错误：\(underlyingError.localizedDescription)"
        case .requestFailed(let statusCode):
            // 检查是否有自定义错误消息
            if let customErrorProvider = self as? NonisolatedCustomErrorMessageProvider,
               let customMessage = customErrorProvider.customErrorMessageForStatusCode(statusCode) {
                errorMessage = customMessage
            } else {
                // 使用默认错误消息
                switch statusCode {
                case 401, 403:
                    errorMessage = "认证失败或无权限 (状态码: \(statusCode))"
                case 404:
                    errorMessage = "资源未找到 (状态码: \(statusCode))"
                case 500...599:
                    errorMessage = "服务器内部错误 (状态码: \(statusCode))"
                default:
                    errorMessage = "服务器请求失败 (状态码: \(statusCode))"
                }
            }
        case .decodingError(let underlyingError):
            errorMessage = "解析数据失败：\(underlyingError.localizedDescription)"
        case .noCookies:
            errorMessage = "需要登录凭证 (Cookies)"
        case .invalidSettings:
            errorMessage = "应用设置无效或服务器地址错误"
        case .noUserAgent:
            errorMessage = "缺少User-Agent"
        }
        
        print("【调试】❌ \(getImplementationName()): 错误: \(errorMessage ?? "未知错误")")
    }
} 