import Foundation

/// HTTP请求方法枚举
enum HTTPMethod: String {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case delete = "DELETE"
}

/// 网络错误定义
enum NetworkError: Error {
    case invalidURL
    case networkError(Error)
    case requestFailed(statusCode: Int)
    case decodingError(Error)
    case noCookies
    case invalidSettings
    case noUserAgent
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .requestFailed(let statusCode):
            return "请求失败，状态码: \(statusCode)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .noCookies:
            return "缺少必要的Cookie"
        case .invalidSettings:
            return "无效的应用设置"
        case .noUserAgent:
            return "缺少User-Agent"
        }
    }
}

/// 网络服务接口定义
@MainActor
protocol NetworkService {
    /// 异步发送请求并解析为指定类型
    @available(iOS 15.0, *)
    func request<T: Decodable>(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async -> Result<T, NetworkError>
    
    /// 异步发送请求并返回原始数据
    @available(iOS 15.0, *)
    func requestData(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async -> Result<Data, NetworkError>
} 