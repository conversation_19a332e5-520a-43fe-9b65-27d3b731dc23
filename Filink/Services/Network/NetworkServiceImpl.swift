import Foundation
import Combine

/// NetworkService的实现类
@MainActor
class NetworkServiceImpl: NetworkService, ObservableObject {
    private let settingsManager: SettingsManager
    private let cookieManager: CookieManager
    private var userAgent: String = ""
    
    // 添加可观察状态
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    init(cookieManager: <PERSON>ieManager, settingsManager: SettingsManager) {
        self.cookieManager = cookieManager
        self.settingsManager = settingsManager
        print("【调试】NetworkServiceImpl 已初始化")
    }
    
    /// 设置User-Agent
    func updateUserAgent(_ newUserAgent: String) {
        print("【调试】🌍 NetworkServiceImpl: 更新 User-Agent 为: \(newUserAgent)")
        self.userAgent = newUserAgent
    }
    
    /// 基础请求构建方法
    private func createRequest(for url: URL, method: HTTPMethod, headers: [String: String]? = nil) -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加基本的Content-Type和Accept头
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        // 如果有自定义headers，添加它们
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // 添加User-Agent（如果有）
        if !userAgent.isEmpty {
            request.setValue(userAgent, forHTTPHeaderField: "User-Agent")
        }
        
        // 尝试添加Cookie（如果有活跃用户）
        if let activeIdentifier = cookieManager.activeUserIdentifier {
            let cookies = cookieManager.activeCookies
            if !cookies.isEmpty && areAllRequiredCookiesPresent(in: cookies) {
                let cookieString = cookies.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
                request.setValue(cookieString, forHTTPHeaderField: "Cookie")
                print("【调试】🌍 NetworkServiceImpl: 使用活动用户 '\(activeIdentifier)' 的 cookies")
            } else {
                print("【调试】⚠️ NetworkServiceImpl: 活动用户 '\(activeIdentifier)' 没有所有必需的 cookies")
            }
        } else {
            print("【调试】ℹ️ NetworkServiceImpl: 没有活动用户，将作为匿名用户请求")
        }
        
        return request
    }
    
    /// 检查所有必需的Cookie是否存在
    private func areAllRequiredCookiesPresent(in cookies: [String: String]) -> Bool {
        // 获取当前活动用户标识符
        guard let activeIdentifier = cookieManager.activeUserIdentifier else {
            return false
        }
        
        // 对于游客用户（guest_session），只检查 cf_clearance cookie
        if activeIdentifier == "guest_session" {
            return cookies["cf_clearance"] != nil && !(cookies["cf_clearance"]?.isEmpty ?? true)
        } else {
            // 对于已登录用户，检查所有必需的 cookies
            return cookieManager.requiredCookieNames.allSatisfy { requiredName in
                cookies[requiredName] != nil && !(cookies[requiredName]?.isEmpty ?? true)
            }
        }
    }
    
    // MARK: - Async/Await API (iOS 15+)
    
    @available(iOS 15.0, *)
    func request<T: Decodable>(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async -> Result<T, NetworkError> {
        let dataResult = await requestData(
            endpoint: endpoint,
            method: method,
            parameters: parameters,
            headers: headers
        )
        
        switch dataResult {
        case .success(let data):
            return ResponseHandler.decodeJSON(data, as: T.self)
        case .failure(let error):
            GlobalToastPresenter.shared.showError(error.localizedDescription)
            return .failure(error)
        }
    }
    
    @available(iOS 15.0, *)
    func requestData(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) async -> Result<Data, NetworkError> {
        // 设置状态
        isLoading = true
        errorMessage = nil
        
        // 使用 defer 确保无论哪个路径返回，都会重置 isLoading
        defer { isLoading = false }
        
        // 确保有网站URL
        guard !settingsManager.website.isEmpty else {
            print("【调试】❌ NetworkServiceImpl: Website URL 为空")
            errorMessage = "应用设置无效或服务器地址错误"
            GlobalToastPresenter.shared.showError("应用设置无效或服务器地址错误")
            return .failure(.invalidSettings)
        }
        
        // 构建完整URL
        var urlString = settingsManager.website
        if !urlString.hasSuffix("/") && !endpoint.hasPrefix("/") {
            urlString += "/"
        }
        urlString += endpoint
        
        guard let url = URL(string: urlString) else {
            print("【调试】❌ NetworkServiceImpl: 无效的URL: \(urlString)")
            errorMessage = "无效的URL"
            GlobalToastPresenter.shared.showError("无效的URL: \(urlString)")
            return .failure(.invalidURL)
        }
        
        var request = createRequest(for: url, method: method, headers: headers)
        
        // 处理请求参数
        if let parameters = parameters {
            if method == .get {
                // 对于GET请求，将参数添加到URL
                var components = URLComponents(url: url, resolvingAgainstBaseURL: false)!
                var queryItems: [URLQueryItem] = []
                
                print("【调试】NetworkServiceImpl: 处理GET参数: \(parameters)")
                
                for (key, value) in parameters {
                    if let arrayValue = value as? [Any] {
                        // 对于数组值，为每个元素创建相同名称的查询参数
                        print("【调试】NetworkServiceImpl: 处理数组参数: \(key) = \(arrayValue)")
                        
                        // 检查键名是否已经包含方括号
                        let paramName = key.hasSuffix("[]") ? key : "\(key)[]"
                        
                        for item in arrayValue {
                            print("【调试】NetworkServiceImpl: 添加数组项: \(paramName) = \(item)")
                            queryItems.append(URLQueryItem(name: paramName, value: "\(item)"))
                        }
                    } else {
                        // 非数组值保持不变
                        print("【调试】NetworkServiceImpl: 添加普通参数: \(key) = \(value)")
                        queryItems.append(URLQueryItem(name: key, value: "\(value)"))
                    }
                }
                
                components.queryItems = (components.queryItems ?? []) + queryItems
                if let newURL = components.url {
                    request.url = newURL
                    print("【调试】NetworkServiceImpl: 构建的GET URL: \(newURL.absoluteString)")
                }
            } else {
                // 对于其他请求，将参数添加到请求体
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: parameters)
                    request.httpBody = jsonData
                    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                } catch {
                    print("【调试】❌ NetworkServiceImpl: 参数序列化错误: \(error)")
                    errorMessage = "参数序列化错误: \(error.localizedDescription)"
                    GlobalToastPresenter.shared.showError("参数序列化错误: \(error.localizedDescription)")
                    return .failure(.decodingError(error))
                }
            }
        }
        
        print("【调试】🌍 NetworkServiceImpl: 发送 \(method.rawValue) 请求到 \(request.url?.absoluteString ?? urlString)")
        
        // 发送异步请求
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // 使用ResponseHandler处理HTTP响应
            let result = ResponseHandler.handleResponse(data: data, response: response, error: nil)
            
            // 如果成功，处理用户名更新
            if case .success = result, let httpResponse = response as? HTTPURLResponse {
                // 更新用户名（如果响应头中包含）
                if let activeIdentifier = cookieManager.activeUserIdentifier {
                    let headerKey = "x-discourse-username"
                    if let extractedUsername = ResponseHandler.extractHeader(from: httpResponse, headerField: headerKey) {
                        cookieManager.updateUsernameForUser(
                            identifier: activeIdentifier, 
                            username: extractedUsername
                        )
                        print("【调试】✅ NetworkServiceImpl: 更新用户 '\(activeIdentifier)' 的用户名为 '\(extractedUsername)'")
                    }
                }
                
                // 处理特殊的认证失败情况
                if httpResponse.statusCode == 401 || httpResponse.statusCode == 403 {
                    // 如果有活动用户，清除用户名
                    if let activeIdentifier = cookieManager.activeUserIdentifier {
                        cookieManager.updateUsernameForUser(identifier: activeIdentifier, username: nil)
                    }
                }
            }
            
            // 更新错误消息（如果有）
            if case .failure(let error) = result {
                errorMessage = error.localizedDescription
            } else {
                errorMessage = nil
            }
            
            return result
            
        } catch {
            print("【调试】❌ NetworkServiceImpl: 网络错误: \(error)")
            errorMessage = "网络错误: \(error.localizedDescription)"
            GlobalToastPresenter.shared.showError("网络错误: \(error.localizedDescription)")
            return .failure(.networkError(error))
        }
    }
    
    func requestObject<T: Decodable>(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async -> Result<T, NetworkError> {
        let dataResult = await requestData(
            endpoint: endpoint,
            method: method,
            parameters: parameters,
            headers: headers
        )
        
        switch dataResult {
        case .success(let data):
            return ResponseHandler.decodeJSON(data, as: T.self)
        case .failure(let error):
            return .failure(error)
        }
    }
} 