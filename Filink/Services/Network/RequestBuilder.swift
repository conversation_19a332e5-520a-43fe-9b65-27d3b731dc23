import Foundation

/// 请求构建器，用于构建URLRequest
class RequestBuilder {
    private let baseURL: URL
    private var endpoint: String
    private var method: HTTPMethod
    private var parameters: [String: Any]?
    private var headers: [String: String] = [:]
    private var cookies: [String: String]?
    private var userAgent: String?
    
    /// 初始化请求构建器
    /// - Parameters:
    ///   - baseURL: 基础URL
    ///   - endpoint: 接口路径
    ///   - method: HTTP方法
    init(baseURL: URL, endpoint: String, method: HTTPMethod = .get) {
        self.baseURL = baseURL
        self.endpoint = endpoint
        self.method = method
        
        // 设置默认的Accept头
        self.headers["Accept"] = "application/json"
    }
    
    /// 设置请求参数
    /// - Parameter parameters: 参数字典
    /// - Returns: 请求构建器自身，支持链式调用
    func withParameters(_ parameters: [String: Any]?) -> RequestBuilder {
        self.parameters = parameters
        return self
    }
    
    /// 设置请求头
    /// - Parameter headers: 头信息字典
    /// - Returns: 请求构建器自身，支持链式调用
    func withHeaders(_ headers: [String: String]) -> RequestBuilder {
        headers.forEach { self.headers[$0.key] = $0.value }
        return self
    }
    
    /// 设置单个请求头
    /// - Parameters:
    ///   - name: 头名称
    ///   - value: 头值
    /// - Returns: 请求构建器自身，支持链式调用
    func withHeader(name: String, value: String) -> RequestBuilder {
        self.headers[name] = value
        return self
    }
    
    /// 设置Cookies
    /// - Parameter cookies: Cookie字典
    /// - Returns: 请求构建器自身，支持链式调用
    func withCookies(_ cookies: [String: String]?) -> RequestBuilder {
        self.cookies = cookies
        return self
    }
    
    /// 设置User-Agent
    /// - Parameter userAgent: User-Agent字符串
    /// - Returns: 请求构建器自身，支持链式调用
    func withUserAgent(_ userAgent: String?) -> RequestBuilder {
        self.userAgent = userAgent
        return self
    }
    
    /// 构建URLRequest
    /// - Returns: 构建好的URLRequest
    /// - Throws: NetworkError.invalidURL 如果URL无效
    func build() throws -> URLRequest {
        // 构建完整URL
        var urlComponents = URLComponents(url: baseURL, resolvingAgainstBaseURL: true)
        
        // 添加路径
        if !endpoint.isEmpty {
            let path = urlComponents?.path ?? ""
            let newPath = path + (path.hasSuffix("/") || endpoint.hasPrefix("/") ? "" : "/") + endpoint
            urlComponents?.path = newPath
        }
        
        // 对于GET请求，将参数添加到URL
        if method == .get, let parameters = parameters {
            var queryItems: [URLQueryItem] = []
            
            print("【调试】RequestBuilder: 处理GET参数: \(parameters)")
            
            for (key, value) in parameters {
                if let arrayValue = value as? [Any] {
                    // 对于数组值，为每个元素创建相同名称的查询参数
                    print("【调试】RequestBuilder: 处理数组参数: \(key) = \(arrayValue)")
                    for item in arrayValue {
                        // 检查键名是否已经包含方括号，如果没有则添加
                        let paramName = key.hasSuffix("[]") ? key : "\(key)[]"
                        print("【调试】RequestBuilder: 添加数组项: \(paramName) = \(item)")
                        queryItems.append(URLQueryItem(name: paramName, value: "\(item)"))
                    }
                } else {
                    // 非数组值保持不变
                    print("【调试】RequestBuilder: 添加普通参数: \(key) = \(value)")
                    queryItems.append(URLQueryItem(name: key, value: "\(value)"))
                }
            }
            
            // 创建本地变量避免重叠访问
            var existingItems = urlComponents?.queryItems ?? []
            existingItems.append(contentsOf: queryItems)
            urlComponents?.queryItems = existingItems
            
            if let url = urlComponents?.url {
                print("【调试】RequestBuilder: 构建的GET URL: \(url.absoluteString)")
            }
        }
        
        guard let url = urlComponents?.url else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加头信息
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // 添加User-Agent
        if let userAgent = userAgent, !userAgent.isEmpty {
            request.setValue(userAgent, forHTTPHeaderField: "User-Agent")
        }
        
        // 添加Cookies
        if let cookies = cookies, !cookies.isEmpty {
            let cookieString = cookies.map { "\($0.key)=\($0.value)" }.joined(separator: "; ")
            request.setValue(cookieString, forHTTPHeaderField: "Cookie")
        }
        
        // 对于非GET请求，将参数添加到请求体
        if method != .get, let parameters = parameters {
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: parameters)
                request.httpBody = jsonData
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            } catch {
                throw NetworkError.decodingError(error)
            }
        }
        
        return request
    }
}
