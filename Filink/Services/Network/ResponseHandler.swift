import Foundation

/// 响应处理器，用于处理网络响应
class ResponseHandler {
    /// 处理网络响应并转换为Data
    /// - Parameters:
    ///   - data: 响应数据
    ///   - response: 响应对象
    ///   - error: 错误对象
    /// - Returns: 处理结果
    static func handleResponse(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, NetworkError> {
        // 处理网络错误
        if let error = error {
            print("【调试】❌ ResponseHandler: 网络错误: \(error)")
            GlobalToastPresenter.shared.showError("网络错误: \(error.localizedDescription)")
            return .failure(.networkError(error))
        }
        
        // 检查响应是否是HTTPURLResponse
        guard let httpResponse = response as? HTTPURLResponse else {
            print("【调试】❌ ResponseHandler: 无效的服务器响应")
            GlobalToastPresenter.shared.showError("无效的服务器响应")
            return .failure(.networkError(URLError(.badServerResponse)))
        }
        
        print("【调试】ℹ️ ResponseHandler: 收到HTTP状态码: \(httpResponse.statusCode)")
        
        // 检查HTTP状态码
        guard (200...299).contains(httpResponse.statusCode) else {
            if httpResponse.statusCode == 401 || httpResponse.statusCode == 403 {
                print("【调试】⚠️ ResponseHandler: 收到 \(httpResponse.statusCode) 未授权/禁止访问")
                GlobalToastPresenter.shared.showError("认证失败或无权限 (状态码: \(httpResponse.statusCode))")
            } else {
                print("【调试】⚠️ ResponseHandler: 请求失败，状态码: \(httpResponse.statusCode)")
                GlobalToastPresenter.shared.showError("请求失败 (状态码: \(httpResponse.statusCode))")
            }
            return .failure(.requestFailed(statusCode: httpResponse.statusCode))
        }
        
        // 确保有数据
        guard let data = data else {
            print("【调试】❌ ResponseHandler: 服务器未返回数据")
            GlobalToastPresenter.shared.showError("服务器未返回数据")
            return .failure(.networkError(URLError(.cannotParseResponse)))
        }
        
        return .success(data)
    }
    
    /// 处理网络响应并解析为指定类型
    /// - Parameters:
    ///   - data: 响应数据
    ///   - response: 响应对象
    ///   - error: 错误对象
    ///   - type: 解析目标类型
    /// - Returns: 处理结果
    static func handleResponse<T: Decodable>(data: Data?, response: URLResponse?, error: Error?, as type: T.Type) -> Result<T, NetworkError> {
        let dataResult = handleResponse(data: data, response: response, error: error)
        
        switch dataResult {
        case .success(let data):
            return decodeJSON(data, as: type)
        case .failure(let error):
            return .failure(error)
        }
    }
    
    /// 解析JSON数据为指定类型
    /// - Parameters:
    ///   - data: JSON数据
    ///   - type: 解析目标类型
    /// - Returns: 处理结果
    static func decodeJSON<T: Decodable>(_ data: Data, as type: T.Type) -> Result<T, NetworkError> {
        do {
            let decoder = JSONDecoder.withStandardDateDecoding()
            let decoded = try decoder.decode(T.self, from: data)
            return .success(decoded)
        } catch {
            print("【调试】❌ ResponseHandler: 解码错误: \(error)")
            GlobalToastPresenter.shared.showError("解码错误: \(error.localizedDescription)")
            return .failure(.decodingError(error))
        }
    }
    
    /// 从响应头中提取特定信息
    /// - Parameters:
    ///   - response: HTTP响应
    ///   - headerField: 头字段名称
    /// - Returns: 头字段值，如果不存在则返回nil
    static func extractHeader(from response: HTTPURLResponse, headerField: String) -> String? {
        return response.value(forHTTPHeaderField: headerField)
    }
} 