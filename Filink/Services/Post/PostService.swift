import Foundation
import CoreData
import Combine

/// 帖子服务接口
@MainActor
protocol PostService {
    /// 获取最新帖子列表
    /// - Parameters:
    ///   - page: 页码，从0开始
    ///   - noDefinitions: 是否排除定义
    ///   - preserveCurrentItems: 是否保留当前项目
    ///   - isLoadingMore: 是否为加载更多操作，默认为false
    func fetchLatestPosts(
        page: Int,
        noDefinitions: Bool,
        preserveCurrentItems: Bool,
        isLoadingMore: Bool
    ) async
    
    /// 获取热门帖子列表
    /// - Parameters:
    ///   - page: 页码，从0开始
    ///   - noDefinitions: 是否排除定义
    ///   - preserveCurrentItems: 是否保留当前项目
    ///   - isLoadingMore: 是否为加载更多操作，默认为false
    func fetchHotPosts(
        page: Int,
        noDefinitions: Bool,
        preserveCurrentItems: Bool,
        isLoadingMore: Bool
    ) async

    /// 获取特定标签的帖子列表
    /// - Parameters:
    ///   - tagName: 标签名称（中文）
    ///   - page: 页码，从0开始
    ///   - preserveCurrentItems: 是否保留当前项目
    ///   - isLoadingMore: 是否为加载更多操作，默认为false
    func fetchTagPosts(
        tagName: String,
        page: Int,
        preserveCurrentItems: Bool,
        isLoadingMore: Bool
    ) async

    /// 获取特定分类的帖子列表
    /// - Parameters:
    ///   - slug: 分类的slug
    ///   - categoryId: 分类ID
    ///   - page: 页码，从0开始
    ///   - parameters: 请求参数
    ///   - preserveCurrentItems: 是否保留当前项目
    ///   - isLoadingMore: 是否为加载更多操作，默认为false
    func fetchCategoryPosts(
        slug: String,
        categoryId: Int,
        page: Int,
        parameters: [String: Any],
        preserveCurrentItems: Bool,
        isLoadingMore: Bool
    ) async
    
    /// 异步获取帖子详情
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - needsUserInfo: 是否需要用户信息，默认为false
    /// - Returns: 帖子详情结果
    @available(iOS 15.0, *)
    func fetchPostDetail(
        postId: String,
        needsUserInfo: Bool
    ) async -> Result<
        (
            mainPost: String,
            allCommentIds: [Int64],
            linkCounts: [String: LinkCount]?,
            userInfo: DiscourseUser?,
            postCreatedAt: Date?
        ),
        NetworkError
    >
    
    /// 发布观察状态
    var isLoading: Bool { get }
    var errorMessage: String? { get }
    
    /// 发布者
    var loadingPublisher: Published<Bool>.Publisher { get }
    var errorMessagePublisher: Published<String?>.Publisher { get }
    
    /// 新获取帖子ID的发布者
    var newlyFetchedPostIdsPublisher: AnyPublisher<Set<Int64>, Never> { get }



    /// 搜索帖子
    /// - Parameters:
    ///   - term: 搜索关键词
    ///   - sortOrder: 排序方式
    /// - Returns: 搜索结果
    func fetchSearchResults(for term: String, sortOrder: SearchSortOrder) async throws -> SearchResult

    /// 清空所有帖子实体
    func clearAllPosts() async throws
}
