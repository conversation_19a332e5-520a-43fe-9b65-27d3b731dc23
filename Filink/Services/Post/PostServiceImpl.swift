import Foundation
import CoreData
import Combine

/// PostService实现类
@MainActor
class PostServiceImpl: PostService, ObservableObject, ErrorHandler, NonisolatedCustomErrorMessageProvider {
    // 依赖注入
    private let networkService: NetworkService
    private let viewContext: NSManagedObjectContext
    
    // 状态管理
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    var loadingPublisher: Published<Bool>.Publisher { $isLoading }
    var errorMessagePublisher: Published<String?>.Publisher { $errorMessage }
    
    /// 新获取帖子ID的发布者
    private let _newlyFetchedPostIdsSubject = PassthroughSubject<Set<Int64>, Never>()
    var newlyFetchedPostIdsPublisher: AnyPublisher<Set<Int64>, Never> {
        _newlyFetchedPostIdsSubject.eraseToAnyPublisher()
    }


    
    // 初始化
    init(networkService: NetworkService, viewContext: NSManagedObjectContext) {
        self.networkService = networkService
        self.viewContext = viewContext
    }
    
    // MARK: - ErrorHandler Protocol Implementation
    
    func getImplementationName() -> String {
        return "PostServiceImpl"
    }
    
    // MARK: - NonisolatedCustomErrorMessageProvider Protocol Implementation
    
    nonisolated func customErrorMessageForStatusCode(_ statusCode: Int) -> String? {
        if statusCode == 404 {
            return "资源未找到 (状态码: \(statusCode))"
        }
        return nil
    }
    
    // MARK: - PostService Protocol Implementation
    
    func fetchLatestPosts(
        page: Int = 0,
        noDefinitions: Bool = false,
        preserveCurrentItems: Bool = false,
        isLoadingMore: Bool = false
    ) async {
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 构建参数
        var parameters: [String: Any] = [:]
        if page > 0 {
            parameters["page"] = page
        }
        if noDefinitions {
            parameters["no_definitions"] = "true"
        }


        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "latest.json",
            method: .get,
            parameters: parameters,
            headers: nil
        )
        
        switch result {
        case .success(let data):
            do {
                // 手动解析JSON数据
                let parsedResult = try parseLatestDataManually(from: data, page: page)

                // 只有在非加载更多操作时才发送新获取的帖子ID
                // 这样可以避免加载更多时触发清理旧帖子的逻辑
                if !isLoadingMore {
                    let newPostIds = Set(parsedResult.map { $0.id })
                    _newlyFetchedPostIdsSubject.send(newPostIds)
                }

            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
            }

        case .failure(let error):
            handleNetworkError(error)
        }
        
        // 更新状态
        isLoading = false
    }

    func fetchHotPosts(
        page: Int = 0,
        noDefinitions: Bool = false,
        preserveCurrentItems: Bool = false,
        isLoadingMore: Bool = false
    ) async {
        // 更新状态
        isLoading = true
        errorMessage = nil

        // 构建参数
        var parameters: [String: Any] = [:]
        if page > 0 {
            parameters["page"] = page
        }
        if noDefinitions {
            parameters["no_definitions"] = "true"
        }

        // 发送请求
        let result = await networkService.requestData(
            endpoint: "hot.json",
            method: .get,
            parameters: parameters,
            headers: nil
        )

        switch result {
        case .success(let data):
            do {
                // 手动解析JSON数据
                let parsedResult = try parseLatestDataManually(from: data, page: page)

                // 只有在非加载更多操作时才发送新获取的帖子ID
                if !isLoadingMore {
                    let newPostIds = Set(parsedResult.map { $0.id })
                    _newlyFetchedPostIdsSubject.send(newPostIds)
                } else {
                }

            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
            }

        case .failure(let error):
            handleNetworkError(error)
        }

        // 更新状态
        isLoading = false
    }

    func fetchTagPosts(
        tagName: String,
        page: Int = 0,
        preserveCurrentItems: Bool = false,
        isLoadingMore: Bool = false
    ) async {
        // 更新状态
        isLoading = true
        errorMessage = nil

        // 将中文标签名转换为URL编码
        guard let encodedTagName = tagName.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) else {
            errorMessage = "标签名编码失败"
            isLoading = false
            return
        }

        // 构建参数
        var parameters: [String: Any] = [:]
        if page > 0 {
            parameters["page"] = page
        }

        // 构建URL路径
        let endpoint = "tag/\(encodedTagName)/l/latest.json"

        // 发送请求
        let result = await networkService.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: parameters,
            headers: nil
        )

        switch result {
        case .success(let data):
            do {
                // 手动解析JSON数据
                let parsedResult = try parseLatestDataManually(from: data, page: page)

                // 只有在非加载更多操作时才发送新获取的帖子ID
                if !isLoadingMore {
                    let newPostIds = Set(parsedResult.map { $0.id })
                    _newlyFetchedPostIdsSubject.send(newPostIds)
                } else {
                }

            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
            }

        case .failure(let error):
            handleNetworkError(error)
        }

        // 更新状态
        isLoading = false
    }

    func fetchPostDetail(
        postId: String,
        needsUserInfo: Bool
    ) async -> Result<
        (
            mainPost: String,
            allCommentIds: [Int64],
            linkCounts: [String: LinkCount]?,
            userInfo: DiscourseUser?,
            postCreatedAt: Date?
        ),
        NetworkError
    > {
        // 先检查缓存
        if let cachedResult = checkPostCache(postId: postId, needsUserInfo: needsUserInfo) {
            return cachedResult
        }

        // 没有缓存，发送网络请求
        let result = await networkService.requestData(
            endpoint: "t/\(postId).json",
            method: .get,
            parameters: nil,
            headers: nil
        )

        switch result {
        case .success(let data):
            return processPostDetailData(postId: postId, data: data, needsUserInfo: needsUserInfo)
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }

    // 便利方法：不需要用户信息的版本
    func fetchPostDetail(postId: String) async -> Result<(mainPost: String, allCommentIds: [Int64], linkCounts: [String: LinkCount]?), NetworkError> {
        let result = await fetchPostDetail(postId: postId, needsUserInfo: false)
        switch result {
        case .success(let (mainPost, allCommentIds, linkCounts, _, _)):
            return .success((mainPost: mainPost, allCommentIds: allCommentIds, linkCounts: linkCounts))
        case .failure(let error):
            return .failure(error)
        }
    }

    // 为了向后兼容，保留回调版本，内部调用异步版本
    func fetchPostDetail(
        postId: String,
        completion: @escaping (
            Result<
                (
                    mainPost: String,
                    allCommentIds: [Int64],
                    linkCounts: [String: LinkCount]?
                ),
                NetworkError
            >
        ) -> Void
    ) {
        // 使用Task包装异步调用
        Task {
            let result = await fetchPostDetail(postId: postId)
            completion(result)
        }
    }
    
    // 添加获取特定分类帖子的方法
    func fetchCategoryPosts(
        slug: String,
        categoryId: Int,
        page: Int = 0,
        parameters: [String: Any],
        preserveCurrentItems: Bool = false,
        isLoadingMore: Bool = false
    ) async {
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 构建参数
        var requestParameters = parameters
        if page > 0 {
            requestParameters["page"] = page
        }
        
        // 构建URL路径
        let endpoint = "c/\(slug)/\(categoryId)/l/latest.json"
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: requestParameters,
            headers: nil
        )
        
        switch result {
        case .success(let data):
            do {
                // 手动解析JSON数据
                let parsedResult = try parseLatestDataManually(from: data, page: page)
                
                // 只有在非加载更多操作时才发送新获取的帖子ID
                if !isLoadingMore {
                    let newPostIds = Set(parsedResult.map { $0.id })
                    _newlyFetchedPostIdsSubject.send(newPostIds)
                } else {
                }
                
            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
            }
            
        case .failure(let error):
            handleNetworkError(error)
        }
        
        // 更新状态
        isLoading = false
    }
    
    // MARK: - Private Helper Methods

    /// 高效的双数组ID匹配函数
    /// - Parameters:
    ///   - sourceIds: 源ID数组（来自API的帖子ID）
    ///   - targetIds: 目标ID数组（当前首页显示的帖子ID）
    /// - Returns: 存在于目标数组中的源ID集合
    private func findMatchingIds(sourceIds: [Int64], targetIds: [Int64]) -> Set<Int64> {
        // 将目标数组转换为Set以提高查找效率 O(1)
        let targetSet = Set(targetIds)

        // 过滤出存在于目标集合中的源ID
        let matchingIds = sourceIds.filter { targetSet.contains($0) }

        return Set(matchingIds)
    }
    
    private func checkPostCache(postId: String, needsUserInfo: Bool = false) -> Result<(mainPost: String, allCommentIds: [Int64], linkCounts: [String: LinkCount]?, userInfo: DiscourseUser?, postCreatedAt: Date?), NetworkError>? {
        do {
            // 查找帖子
            let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %lld", Int64(postId) ?? 0)
            fetchRequest.fetchLimit = 1

            let existingPosts = try viewContext.fetch(fetchRequest)

            if let cachedPost = existingPosts.first,
               let contentHTML = cachedPost.contentHTML,
               let lastFetched = cachedPost.lastFetched {

                // 检查缓存新鲜度
                if let updatedAt = cachedPost.updated_at, lastFetched >= updatedAt {
                    var cachedCommentIds: [Int64] = []
                    if let commentsSet = cachedPost.comment as? Set<Comment> {
                        cachedCommentIds = commentsSet.map { $0.id }.sorted()
                    }

                    let cachedLinkCounts: [String: LinkCount]? = nil

                    // 如果需要用户信息，从关联的User实体中获取
                    var userInfo: DiscourseUser? = nil
                    if needsUserInfo, let user = cachedPost.user {
                        // 检查用户是否有存储的完整头像模板路径
                        // 注意：这里需要一个新的字段来存储完整的头像模板，暂时使用传统方式
                        let avatarTemplate: String?
                        if let avatarFilename = user.avatar, !avatarFilename.isEmpty,
                           let username = user.username, !username.isEmpty {
                            // 如果头像文件名看起来像完整路径（以/开头），直接使用
                            if avatarFilename.hasPrefix("/") {
                                avatarTemplate = avatarFilename
                            } else {
                                // 否则构建传统格式
                                avatarTemplate = "/user_avatar/linux.do/\(username)/{size}/\(avatarFilename)"
                            }
                        } else {
                            avatarTemplate = nil
                        }

                        userInfo = DiscourseUser(
                            id: user.id,
                            username: user.username ?? "",
                            name: user.name,
                            avatarTemplate: avatarTemplate,
                            displayUsername: user.displayUsername,
                            title: user.usertitle
                        )
                    }

                    return .success((
                        mainPost: contentHTML,
                        allCommentIds: cachedCommentIds,
                        linkCounts: cachedLinkCounts,
                        userInfo: userInfo,
                        postCreatedAt: cachedPost.created_at
                    ))
                } else {
                }
            }
        } catch {
        }

        return nil
    }
    
    private func processPostDetailData(
        postId: String,
        data: Data,
        needsUserInfo: Bool = false
    ) -> Result<(mainPost: String, allCommentIds: [Int64], linkCounts: [String: LinkCount]?, userInfo: DiscourseUser?, postCreatedAt: Date?), NetworkError> {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            
            // 解码为 PostDetailResponse
            let response = try decoder.decode(PostDetailResponse.self, from: data)
            
            // 确保有主贴
            guard let mainPost = response.postStream.posts.first else {
                return .failure(.decodingError(NSError(domain: "ParsingError", code: 3, userInfo: [NSLocalizedDescriptionKey: "缺少主贴内容"])))
            }
            
            // 提取评论ID - 从stream字段获取
            var commentIds: [Int64] = []
            if let streamArray = response.postStream.stream, streamArray.count > 1 {
                // 从索引1开始（跳过帖子ID）
                commentIds = streamArray[1...].map { Int64($0) }
            }
            
            // 处理评论数据 - 从posts数组中提取评论（跳过第一个，因为第一个是主贴）
            if response.postStream.posts.count > 1 {
                let commentPosts = Array(response.postStream.posts.dropFirst()) // 排除第一个主贴
                
                // 创建CommentServiceImpl实例
                let commentService = CommentServiceImpl(
                    networkService: networkService,
                    viewContext: viewContext
                )
                
                // 处理每条评论
                var processedComments = 0
                let postIdInt64 = Int64(postId) ?? 0
                
                for commentData in commentPosts {
                    do {
                        // 直接使用PostData版本的方法，避免转换为字典导致日期信息丢失
                        let _ = try commentService.saveOrUpdateCommentEntity(
                            from: commentData,
                            for: postIdInt64
                        )
                        processedComments += 1
                    } catch {
                    }
                }
                
                // 保存Context
                if viewContext.hasChanges {
                    do {
                        try viewContext.save()
                    } catch {
                    }
                }
            }
            
            // 处理链接统计
            var linkCountsDict: [String: LinkCount]? = nil
            if let linkCountsArray = mainPost.linkCounts, !linkCountsArray.isEmpty {
                linkCountsDict = linkCountsArray.reduce(into: [:]) { dict, linkCount in
                    dict[linkCount.url] = linkCount
                }
            }
            
            // 处理用户信息（如果需要）
            var userInfo: DiscourseUser? = nil
            if needsUserInfo {
                // 从主贴数据中提取用户信息
                userInfo = DiscourseUser(
                    id: mainPost.userId,
                    username: mainPost.username,
                    name: mainPost.name,
                    avatarTemplate: mainPost.avatarTemplate,
                    displayUsername: mainPost.displayUsername,
                    title: mainPost.userTitle
                )

                // 同时创建或更新User实体
                let fetchRequestUser: NSFetchRequest<User> = User.fetchRequest()
                fetchRequestUser.predicate = NSPredicate(format: "id == %lld", mainPost.userId)
                fetchRequestUser.fetchLimit = 1

                do {
                    let existingUsers = try viewContext.fetch(fetchRequestUser)
                    let user: User
                    if let existingUser = existingUsers.first {
                        user = existingUser
                    } else {
                        user = User(context: viewContext)
                        user.id = mainPost.userId
                    }

                    // 更新用户属性
                    user.name = mainPost.name
                    user.username = mainPost.username
                    user.displayUsername = mainPost.displayUsername
                    user.usertitle = mainPost.userTitle

                    // 处理头像URL - 保存完整的头像模板路径
                    if let avatarTemplate = mainPost.avatarTemplate {
                        user.avatar = avatarTemplate  // 保存完整模板路径
                    } else {
                        user.avatar = nil
                    }
                } catch {
                }
            }

            // 更新帖子实体 (不再存储linkCountsJson到Core Data)
            let postIdInt64 = Int64(postId) ?? 0
            let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %lld", postIdInt64)
            fetchRequest.fetchLimit = 1

            if let post = try? viewContext.fetch(fetchRequest).first {
                post.contentHTML = mainPost.cooked
                post.lastFetched = Date()

                // 如果需要用户信息，同时更新帖子的基本信息
                if needsUserInfo {
                    post.title = response.title ?? "无标题" // 从API响应中获取标题
                    post.created_at = mainPost.createdAt
                    post.userId = mainPost.userId

                    // 关联用户实体
                    let fetchRequestUser: NSFetchRequest<User> = User.fetchRequest()
                    fetchRequestUser.predicate = NSPredicate(format: "id == %lld", mainPost.userId)
                    fetchRequestUser.fetchLimit = 1

                    if let user = try? viewContext.fetch(fetchRequestUser).first {
                        post.user = user
                    }
                }

                // 保存更改
                do {
                    try viewContext.save()
                } catch {
                }
            }

            return .success((mainPost: mainPost.cooked, allCommentIds: commentIds, linkCounts: linkCountsDict, userInfo: userInfo, postCreatedAt: mainPost.createdAt))
            
        } catch {
            return .failure(.decodingError(error))
        }
    }
    
    /// 手动解析最新帖子数据
    ///
    /// 核心功能：确保加载更多时帖子位置不发生变化
    ///
    /// 实现原理：
    /// 1. 刷新操作：重新设置所有帖子的时间戳，按API返回顺序排列
    /// 2. 加载更多操作：
    ///    - 分析API返回的数据，区分新旧帖子
    ///    - 旧帖子（已在首页显示）：保持原有时间戳不变，维持原有位置
    ///    - 新帖子（不在首页显示）：基于首页最后帖子的时间戳递增设置，确保排在末尾
    ///
    /// 关键技术点：
    /// - 使用last_fetched字段作为排序依据，移除了last_posted_at等可变字段的排序影响
    /// - 通过ID匹配算法高效识别新旧帖子
    /// - 动态获取首页最后帖子的时间戳作为新帖子的基准时间
    ///
    /// - Parameters:
    ///   - data: JSON数据
    ///   - page: 页码
    /// - Returns: 解析后的帖子数组
    private func parseLatestDataManually(from data: Data, page: Int = 0) throws -> [Post] {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            
            // 解码为 LatestPostsResponse
            let response = try decoder.decode(LatestPostsResponse.self, from: data)
            
            // 处理用户数据
            try processUsers(from: response.users)
            
            // 获取主题数据
            let topicsArray = response.topicList.topics
            
            // 处理每个主题
            var results: [Post] = []
            
            // 确定是否为加载更多操作
            let isLoadingMore = page > 0

            // 获取时间戳基准点
            // 对于刷新操作：使用当前时间作为基准
            // 对于加载更多操作：使用当前首页最后一个帖子的时间戳作为基准，确保新数据排在末尾
            var baseTime = Date()

            if isLoadingMore {
                let homeRequest: NSFetchRequest<Post> = Post.fetchRequest()
                homeRequest.predicate = NSPredicate(format: "isActiveInHome == true")
                homeRequest.sortDescriptors = [
                    NSSortDescriptor(keyPath: \Post.last_fetched, ascending: false) // 降序排列，获取最新的帖子
                ]
                homeRequest.fetchLimit = 1 // 只需要最后一个帖子

                // 尝试获取当前首页最后一个帖子的时间戳
                if let latestPost = try? viewContext.fetch(homeRequest).first,
                   let latestTime = latestPost.last_fetched {
                    baseTime = latestTime
                }
                // 如果获取失败，继续使用当前时间作为基准
            }
            
            // 获取当前批次数据中最新帖子的时间戳
            var batchNewestPostDate: Date? = nil
            
            // 首先遍历一遍找出这批数据中最新的时间
            for topic in topicsArray {
                if let lastPostedDate = topic.lastPostedAt {
                    if batchNewestPostDate == nil || lastPostedDate > batchNewestPostDate! {
                        batchNewestPostDate = lastPostedDate
                    }
                }
            }
            
            // 如果是加载更多操作，先分析数据新旧情况
            var oldPostIds: [Int64] = []
            var newPostIds: [Int64] = []

            if isLoadingMore {
                // 分析API返回的数据，区分新旧帖子
                // 这是确保帖子位置不变的关键逻辑：
                // - 旧帖子：已在首页显示的帖子，保持原有时间戳不变
                // - 新帖子：不在首页显示的帖子，设置新的时间戳排在末尾

                let homeRequest: NSFetchRequest<Post> = Post.fetchRequest()
                homeRequest.predicate = NSPredicate(format: "isActiveInHome == true")
                homeRequest.sortDescriptors = [
                    NSSortDescriptor(keyPath: \Post.last_fetched, ascending: true),
                    NSSortDescriptor(keyPath: \Post.id, ascending: false)
                ]

                do {
                    let currentHomePosts = try viewContext.fetch(homeRequest)
                    let currentHomePostIds = currentHomePosts.map { $0.id }
                    let apiPostIds = topicsArray.map { $0.id }

                    // 使用高效的ID匹配算法找出重叠的帖子
                    let matchingIds = findMatchingIds(sourceIds: apiPostIds, targetIds: currentHomePostIds)

                    // 分类帖子：
                    // oldPostIds: 在当前首页显示的帖子，需要保持原有位置
                    // newPostIds: 不在当前首页显示的帖子，需要追加到末尾
                    oldPostIds = Array(matchingIds)
                    newPostIds = apiPostIds.filter { !matchingIds.contains($0) }

                } catch {
                    // 查询失败时的降级处理：将所有数据当作新数据
                    newPostIds = topicsArray.map { $0.id }
                }

                // 注意：不再需要隐藏重复帖子的逻辑
                // 因为旧帖子保持原有时间戳和位置，不会造成视觉上的重复或跳动

            }

            // 处理每个帖子数据
            // 核心逻辑：确保已显示帖子的位置绝对不变，新帖子追加到末尾
            for (index, topic) in topicsArray.enumerated() {

                let fetchRequest: NSFetchRequest<Post> = Post.fetchRequest()
                fetchRequest.predicate = NSPredicate(format: "id == %lld", topic.id)
                fetchRequest.fetchLimit = 1

                var post: Post
                do {
                    let existingPosts = try viewContext.fetch(fetchRequest)
                    if let existingPost = existingPosts.first {
                        // 更新现有Post
                        post = existingPost
                    } else {
                        // 创建新的Post
                        post = Post(context: viewContext)
                        post.id = topic.id
                    }
                } catch {
                    continue
                }

                // 设置last_fetched时间戳
                // 这是确保帖子排序稳定的核心逻辑
                if isLoadingMore {
                    if newPostIds.contains(topic.id) {
                        // 新帖子：基于首页最后帖子的时间戳递增设置
                        // 确保新帖子排在所有现有帖子之后，维持列表的时间顺序
                        let incrementedTime = baseTime.addingTimeInterval(Double(index + 1))
                        post.last_fetched = incrementedTime
                    } else {
                        // 旧帖子：完全不修改last_fetched时间戳
                        // 这是防止帖子位置变化的关键：保持原有时间戳不变
                        // 无需任何操作，保持现有的时间戳
                    }
                } else {
                    // 刷新操作：重新设置所有帖子的时间戳
                    // 按照API返回的顺序设置递增的时间戳
                    let baseOffset = baseTime.timeIntervalSince1970
                    let decrementedTime = Date(timeIntervalSince1970: baseOffset + Double(index))
                    post.last_fetched = decrementedTime
                }

                // 更新帖子的其他属性（标题、内容、用户信息等）
                updatePostProperties(post: &post, from: topic)

                // 添加到结果数组
                results.append(post)
            }
            
            // 保存Core Data更改
            if viewContext.hasChanges {
                do {
                    try viewContext.save()
                } catch {
                    throw error
                }
            }
            
            return results
            
        } catch {
            throw NetworkError.decodingError(error)
        }
    }
    
    // 处理用户数据
    private func processUsers(from users: [DiscourseUser]) throws {
        var createdUsersCount = 0
        var updatedUsersCount = 0
        
        
        
        // 遍历处理每个用户
        for userData in users {
            // 检查是否已存在
            let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %lld", userData.id)
            fetchRequest.fetchLimit = 1

            var user: User
            do {
                let existingUsers = try viewContext.fetch(fetchRequest)
                if let existingUser = existingUsers.first {
                    // 更新现有User
                    user = existingUser
                    updatedUsersCount += 1
                } else {
                    // 创建新的User
                    user = User(context: viewContext)
                    user.id = userData.id
                    createdUsersCount += 1
                }
            } catch {
                continue
            }

            // 更新用户属性
            user.name = userData.name
            user.username = userData.username
            user.displayUsername = userData.displayUsername
            user.usertitle = userData.title

            // 处理头像URL - 保存完整的头像模板路径
            if let avatarTemplate = userData.avatarTemplate {
                user.avatar = avatarTemplate  // 保存完整模板路径
            } else {
                user.avatar = nil
            }

        }
        
    }
    
    // 更新帖子属性
    private func updatePostProperties(post: inout Post, from topic: TopicData, isOldPost: Bool = false) {
        // 更新标题
        post.title = topic.title

        // 如果title为nil，则跳过
        guard post.title != nil else {
            return
        }

        // 更新计数
        post.posts_count = topic.postsCount
        post.reply_count = topic.replyCount
        post.highest_post_number = topic.highestPostNumber
        post.image_url = topic.imageUrl

        // 更新置顶状态
        post.isPined = topic.isPinned

        // 调试：检查置顶状态
        if topic.isPinned {
            print("【调试】发现置顶帖子: id=\(topic.id), title=\(topic.title.prefix(20)), isPinned=\(topic.isPinned)")
        }

        // 日期字段
        post.created_at = topic.createdAt
        post.last_posted_at = topic.lastPostedAt

        // 其他字段
        post.excerpt = topic.excerpt

        let tagsString = topic.tags?.joined(separator: ",")
        post.tags = tagsString

        post.views = topic.views
        post.like_count = topic.likeCount
        post.category_id = topic.categoryId

        // 布尔值字段
        post.can_vote = topic.canVote

        // 本地状态
        post.isRead = post.isRead
        post.isActiveInHome = true
        
        // 关联用户
        if let firstPoster = topic.posters?.first {
            let userId = firstPoster.userId

            // 尝试查找现有User
            let fetchRequestUser: NSFetchRequest<User> = User.fetchRequest()
            fetchRequestUser.predicate = NSPredicate(format: "id == %lld", userId)
            fetchRequestUser.fetchLimit = 1

            do {
                let existingUsers = try viewContext.fetch(fetchRequestUser)
                if let existingUser = existingUsers.first {
                    // 关联现有User
                    post.user = existingUser
                    post.userId = userId
                } else {
                    // 如果User不存在，只设置userId
                    post.userId = userId
                }
            } catch {
                post.userId = userId // 至少设置userId
            }
        } else {
        }
    }

    func fetchSearchResults(for term: String, sortOrder: SearchSortOrder) async throws -> SearchResult {
        // 更新状态
        isLoading = true
        errorMessage = nil

        // 构建查询字符串
        var queryString = "q=\(term.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")"
        if let order = sortOrder.queryValue {
            queryString += "&order=\(order)"
        }
        
        // 构建端点
        let endpoint = "search?\(queryString)"

        // 发送请求
        let result = await networkService.requestData(
            endpoint: endpoint,
            method: .get,
            parameters: nil, // 参数已经包含在端点中
            headers: ["Accept": "application/json"]
        )

        // 更新状态
        isLoading = false

        switch result {
        case .success(let data):
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let searchResult = try decoder.decode(SearchResult.self, from: data)
                return searchResult
            } catch {
                throw NetworkError.decodingError(error)
            }
            
        case .failure(let error):
            handleNetworkError(error)
            throw error
        }
    }

    // MARK: - 清空所有帖子实体
    func clearAllPosts() async throws {
        do {
            // 方法1：使用批量删除（更高效）
            let fetchRequest: NSFetchRequest<NSFetchRequestResult> = Post.fetchRequest()
            let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
            batchDeleteRequest.resultType = .resultTypeObjectIDs // 获取被删除对象的ID

            // 执行批量删除
            let result = try viewContext.execute(batchDeleteRequest) as? NSBatchDeleteResult
            let objectIDs = result?.result as? [NSManagedObjectID] ?? []

            // 关键：手动通知 Core Data 上下文对象已被删除
            // 这样 @FetchRequest 就能收到更新通知
            let changes = [NSDeletedObjectsKey: objectIDs]
            NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [viewContext])

        } catch {
            throw error
        }
    }
}
