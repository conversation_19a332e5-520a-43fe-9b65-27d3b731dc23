import Foundation
import CoreData
import Combine

/// 用户服务接口
@MainActor
protocol UserService {
    /// 获取用户资料（异步）
    /// - Parameter userId: 用户ID
    /// - Returns: 用户资料结果
    @available(iOS 15.0, *)
    func fetchUserProfile(
        userId: Int64
    ) async -> Result<User, NetworkError>
    
    /// 获取当前登录用户资料（异步）
    /// - Returns: 用户资料结果
    @available(iOS 15.0, *)
    func fetchCurrentUserProfile() async -> Result<User, NetworkError>
    
    /// 更新用户资料（异步）
    /// - Parameter user: 用户对象
    /// - Returns: 更新结果
    @available(iOS 15.0, *)
    func updateUserProfile(
        user: User
    ) async -> Result<Void, NetworkError>

    /// 获取用户摘要信息（异步）
    /// - Parameter username: 用户名
    /// - Returns: 用户摘要信息
    @available(iOS 15.0, *)
    func fetchUserProfileSummary(
        username: String
    ) async throws -> UserSummaryResponse
    
    /// 从帖子中提取用户信息
    /// - Parameters:
    ///   - json: 包含用户信息的JSON数据
    ///   - context: CoreData上下文
    /// - Returns: 提取的User对象，如果提取失败则返回nil
    static func extractUserFromPost(
        from json: [String: Any],
        context: NSManagedObjectContext
    ) -> User?
    
    /// 发布观察状态
    var isLoading: Bool { get }
    var errorMessage: String? { get }
    
    /// 发布者
    var loadingPublisher: Published<Bool>.Publisher { get }
    var errorMessagePublisher: Published<String?>.Publisher { get }
}
