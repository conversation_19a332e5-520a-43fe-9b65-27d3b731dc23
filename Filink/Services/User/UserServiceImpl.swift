import Foundation
import CoreData
import Combine

/// UserService实现类
@MainActor
class UserServiceImpl: UserService, ObservableObject, ErrorHandler, NonisolatedCustomErrorMessageProvider {
    // 依赖注入
    private let networkService: NetworkService
    private let cookieManager: CookieManager
    private let viewContext: NSManagedObjectContext
    
    // 状态管理
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    
    var loadingPublisher: Published<Bool>.Publisher { $isLoading }
    var errorMessagePublisher: Published<String?>.Publisher { $errorMessage }
    
    // 初始化
    init(networkService: NetworkService, cookieManager: CookieManager, viewContext: NSManagedObjectContext) {
        self.networkService = networkService
        self.cookieManager = cookieManager
        self.viewContext = viewContext
        print("【调试】UserServiceImpl 已初始化")
    }
    
    // MARK: - ErrorHandler Protocol Implementation
    
    func getImplementationName() -> String {
        return "UserServiceImpl"
    }
    
    // MARK: - NonisolatedCustomErrorMessageProvider Protocol Implementation
    
    nonisolated func customErrorMessageForStatusCode(_ statusCode: Int) -> String? {
        if statusCode == 404 {
            return "用户不存在 (状态码: \(statusCode))"
        }
        return nil
    }
    
    // MARK: - UserService Protocol Implementation
    
    @available(iOS 15.0, *)
    func fetchUserProfile(
        userId: Int64
    ) async -> Result<User, NetworkError> {
        print("【调试】🌍 UserServiceImpl: fetchUserProfile(userId: \(userId)) - async")
        
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 尝试从缓存获取
        if let cachedUser = getUserFromCache(userId: userId) {
            print("【调试】✅ UserServiceImpl: 从缓存中获取用户 ID=\(userId)")
            isLoading = false
            return .success(cachedUser)
        }
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "users/\(userId).json",
            method: .get,
            parameters: nil,
            headers: nil
        )
        
        // 重置状态
        isLoading = false
        
        switch result {
        case .success(let data):
            do {
                // 解析数据
                let user = try processUserProfileData(data: data)
                return .success(user)
            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
                print("【调试】❌ UserServiceImpl: 解析数据失败: \(error)")
                return .failure(.decodingError(error))
            }
            
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }
    
    @available(iOS 15.0, *)
    func fetchCurrentUserProfile() async -> Result<User, NetworkError> {
        print("【调试】🌍 UserServiceImpl: fetchCurrentUserProfile() - async")
        
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 检查是否有活动用户
        if cookieManager.activeUserIdentifier == nil {
            isLoading = false
            errorMessage = "没有活动用户"
            print("【调试】❌ UserServiceImpl: 没有活动用户")
            return .failure(.noCookies)
        }
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "session/current.json",
            method: .get,
            parameters: nil,
            headers: nil
        )
        
        // 重置状态
        isLoading = false
        
        switch result {
        case .success(let data):
            do {
                // 解析数据
                let user = try processCurrentUserData(data: data)
                return .success(user)
            } catch {
                errorMessage = "解析数据失败: \(error.localizedDescription)"
                print("【调试】❌ UserServiceImpl: 解析数据失败: \(error)")
                return .failure(.decodingError(error))
            }
            
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }
    
    @available(iOS 15.0, *)
    func updateUserProfile(
        user: User
    ) async -> Result<Void, NetworkError> {
        print("【调试】🌍 UserServiceImpl: updateUserProfile(user: \(user.id)) - async")
        
        // 更新状态
        isLoading = true
        errorMessage = nil
        
        // 构建参数
        var parameters: [String: Any] = [:]
        
        if let name = user.name {
            parameters["name"] = name
        }
        
        if let bio = user.usersignature {
            parameters["bio_raw"] = bio
        }
        
        // 发送请求
        let result = await networkService.requestData(
            endpoint: "users/\(user.id)",
            method: .put,
            parameters: parameters,
            headers: nil
        )
        
        // 重置状态
        isLoading = false
        
        switch result {
        case .success(_):
            // 更新成功，保存到Core Data
            do {
                try viewContext.save()
                print("【调试】✅ UserServiceImpl: 用户资料更新成功，已保存到Core Data")
                return .success(())
            } catch {
                errorMessage = "保存数据失败: \(error.localizedDescription)"
                print("【调试】❌ UserServiceImpl: 保存数据失败: \(error)")
                return .failure(.decodingError(error))
            }
            
        case .failure(let error):
            handleNetworkError(error)
            return .failure(error)
        }
    }

    @available(iOS 15.0, *)
    func fetchUserProfileSummary(username: String) async throws -> UserSummaryResponse {
        print("【调试】🌍 UserServiceImpl: fetchUserProfileSummary(username: \(username)) - async")
        
        isLoading = true
        errorMessage = nil
        
        let result = await networkService.requestData(
            endpoint: "u/\(username)/summary.json",
            method: .get,
            parameters: nil,
            headers: nil
        )
        
        isLoading = false
        
        switch result {
        case .success(let data):
            do {
                let decoder = JSONDecoder()
                let response = try decoder.decode(UserSummaryResponse.self, from: data)
                print("【调试】✅ UserServiceImpl: 成功获取用户摘要信息 for \(username)")
                return response
            } catch {
                errorMessage = "解析用户摘要数据失败: \(error.localizedDescription)"
                print("【调试】❌ UserServiceImpl: 解析用户摘要数据失败: \(error)")
                throw NetworkError.decodingError(error)
            }
            
        case .failure(let error):
            handleNetworkError(error)
            throw error
        }
    }
    
    static func extractUserFromPost(
        from json: [String: Any],
        context: NSManagedObjectContext
    ) -> User? {
        // 提取用户ID
        guard let userId = json["user_id"] as? Int64 else { return nil }
        
        // 提取用户名和显示名称
        guard let username = json["username"] as? String,
              let name = json["name"] as? String else { return nil }
        
        // 提取头像信息 - 保存完整的头像模板路径
        let avatarTemplate = json["avatar_template"] as? String ?? ""
        let avatar = avatarTemplate // 保存完整模板路径，而不是只保留文件名
        
        // 提取用户头衔和显示用户名
        let userTitle = json["user_title"] as? String ?? ""
        let displayUsername = json["display_username"] as? String ?? ""
        
        // 检查数据库中是否已存在此用户
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", userId)
        fetchRequest.fetchLimit = 1
        
        do {
            let existingUsers = try context.fetch(fetchRequest)
            
            if let existingUser = existingUsers.first {
                // 更新已有用户信息
                existingUser.name = name
                existingUser.username = username
                existingUser.avatar = avatar
                existingUser.usertitle = userTitle
                existingUser.usersignature = userTitle
                existingUser.displayUsername = displayUsername
                
                return existingUser
            } else {
                // 创建新用户
                let user = User(context: context)
                user.id = userId
                user.name = name
                user.username = username
                user.avatar = avatar
                user.usertitle = userTitle
                user.usersignature = userTitle
                user.displayUsername = displayUsername
                
                return user
            }
        } catch {
            print("【调试】Error fetching user: \(error)")
            return nil
        }
    }
    
    // MARK: - Private Helper Methods
    
    private func getUserFromCache(userId: Int64) -> User? {
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %lld", userId)
        fetchRequest.fetchLimit = 1
        
        do {
            let users = try viewContext.fetch(fetchRequest)
            return users.first
        } catch {
            print("【调试】❌ UserServiceImpl: 从缓存获取用户失败: \(error)")
            return nil
        }
    }
    
    private func processUserProfileData(data: Data) throws -> User {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            
            // 解码为 UserProfileAPIResponse
            let response = try decoder.decode(UserProfileAPIResponse.self, from: data)
            let userData = response.user
            
            // 查找或创建用户
            let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %lld", userData.id)
            fetchRequest.fetchLimit = 1
            
            let users = try viewContext.fetch(fetchRequest)
            let user: User
            
            if let existingUser = users.first {
                // 更新现有用户
                user = existingUser
            } else {
                // 创建新用户
                user = User(context: viewContext)
                user.id = userData.id
            }
            
            // 更新用户属性
            user.username = userData.username
            user.name = userData.name
            
            // 处理头像URL - 保存完整的头像模板路径
            user.avatar = userData.avatarTemplate
            
            user.displayUsername = userData.name
            user.usertitle = "" // API响应中没有此信息
            user.usersignature = "" // API响应中没有此信息
            
            // 保存到Core Data
            try viewContext.save()
            
            print("【调试】✅ UserServiceImpl: 成功处理用户资料数据 ID=\(userData.id)")
            return user
            
        } catch {
            print("【调试】❌ UserServiceImpl: 处理用户资料数据失败: \(error)")
            throw error
        }
    }
    
    private func processCurrentUserData(data: Data) throws -> User {
        do {
            // 使用预配置的 JSONDecoder
            let decoder = JSONDecoder.withStandardDateDecoding()
            
            // 解码为 CurrentUserResponse
            let response = try decoder.decode(CurrentUserResponse.self, from: data)
            let userData = response.currentUser
            
            // 查找或创建用户
            let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "id == %lld", userData.id)
            fetchRequest.fetchLimit = 1
            
            let users = try viewContext.fetch(fetchRequest)
            let user: User
            
            if let existingUser = users.first {
                // 更新现有用户
                user = existingUser
            } else {
                // 创建新用户
                user = User(context: viewContext)
                user.id = userData.id
            }
            
            // 更新用户属性
            user.username = userData.username
            user.name = userData.name ?? userData.username
            
            // 处理头像URL - 保存完整的头像模板路径
            user.avatar = userData.avatarTemplate
            
            user.displayUsername = userData.displayUsername ?? userData.name ?? userData.username
            user.usertitle = userData.title ?? ""
            user.usersignature = userData.bioRaw ?? userData.title ?? ""
            
            // 保存到Core Data
            try viewContext.save()
            
            print("【调试】✅ UserServiceImpl: 成功处理当前用户资料数据 ID=\(userData.id)")
            return user
            
        } catch {
            print("【调试】❌ UserServiceImpl: 处理当前用户资料数据失败: \(error)")
            throw error
        }
    }
}
