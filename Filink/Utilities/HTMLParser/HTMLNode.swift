//
//  HTMLNode.swift
//  Lovelo
//
//  HTML节点数据模型 - 整合版本
//

import Foundation
import UIKit

/// HTML节点结构
/// 用于表示解析后的HTML文档树结构
struct HTMLNode: Identifiable {
    /// 唯一标识符（基于内容哈希，避免UUID开销）
    let id: Int

    /// 标签名称（已规范化为小写）
    let tagName: String

    /// 属性字典
    let attributes: [String: String]
    
    /// 从 style 属性解析的样式字典
    let styles: [String: String]

    /// 子节点数组
    var children: [HTMLNode] = []

    /// 内容文本
    let content: String

    /// 是否为文本节点
    let isTextNode: Bool

    /// 图片宽度（如果是img标签）- 优化版本
    var imageWidth: CGFloat? {
        guard tagName == "img" else { return nil }

        if let widthStr = attributes["width"] {
            return CGFloat(Int(widthStr) ?? 50)
        } else {
            return 50 // 默认宽度
        }
    }

    /// 图片高度（如果是img标签）- 优化版本
    var imageHeight: CGFloat? {
        guard tagName == "img" else { return nil }

        if let heightStr = attributes["height"] {
            return CGFloat(Int(heightStr) ?? 50)
        } else {
            return 50 // 默认高度
        }
    }

    /// 判断是否为块级元素 - 优化版本
    var isBlockElement: Bool {
        return Self.blockElements.contains(tagName)
    }

    // MARK: - 静态常量
    private static let blockElements: Set<String> = [
        "p", "h1", "h2", "h3", "h4", "h5", "h6", "div", "ul", "ol", "li",
        "blockquote", "pre", "hr", "table", "form"
    ]

    // MARK: - 初始化方法

    /// 创建元素节点
    /// - Parameters:
    ///   - tagName: 标签名（将自动转换为小写）
    ///   - attributes: 属性字典
    ///   - children: 子节点数组
    init(tagName: String, attributes: [String: String] = [:], children: [HTMLNode] = [], styles: [String: String] = [:]) {
        let normalizedTagName = tagName.lowercased()
        self.tagName = normalizedTagName
        self.attributes = attributes
        self.children = children
        self.content = ""
        self.isTextNode = false
        self.styles = styles

        // 生成基于内容的轻量级ID
        self.id = Self.generateNodeId(tagName: normalizedTagName, attributes: attributes, content: "", isTextNode: false)
    }

    /// 创建文本节点
    /// - Parameter content: 文本内容
    init(textContent: String) {
        self.tagName = ""
        self.attributes = [:]
        self.children = []
        self.content = textContent
        self.isTextNode = true
        self.styles = [:]

        // 生成基于内容的轻量级ID
        self.id = Self.generateNodeId(tagName: "", attributes: [:], content: textContent, isTextNode: true)
    }

    // MARK: - 私有方法

    /// 生成节点ID（基于内容哈希，比UUID更轻量）
    private static func generateNodeId(tagName: String, attributes: [String: String], content: String, isTextNode: Bool) -> Int {
        var hasher = Hasher()
        hasher.combine(tagName)
        hasher.combine(content)
        hasher.combine(isTextNode)

        // 只对关键属性进行哈希，减少计算开销
        let keyAttributes = ["src", "href", "class", "id", "width", "height"]
        for key in keyAttributes {
            if let value = attributes[key] {
                hasher.combine(key)
                hasher.combine(value)
            }
        }

        return hasher.finalize()
    }

    /// 递归获取节点及其所有子节点的文本内容
    var textContent: String {
        if isTextNode {
            return content
        } else {
            return children.map { $0.textContent }.joined()
        }
    }
    
    /// 从 <pre> 节点中提取其完整、未修改的原始文本内容
    /// 遍历当前 HTMLNode 实例（即 <pre> 节点）的所有 children。
    /// 对于每一个子节点：
    /// - 如果子节点是文本节点 (`isTextNode == true`)，则将其 `content` 属性的字符串值直接拼接到最终结果字符串。
    /// - 如果子节点是 `<br>` 元素，则在结果字符串中追加一个换行符 (`\n`)。
    /// - 如果子节点是其他类型的元素节点（例如常见的 `<code>` 标签，或可能存在的 `<span>` 等内联标签），
    ///   则递归调用该子节点自身的 `rawTextContentForPre()` 方法，并将其返回的字符串拼接到结果中。
    ///   这确保了深层嵌套的文本内容也能被正确提取并保留格式。
    func rawTextContentForPre() -> String {
        var result = ""
        for child in children {
            if child.isTextNode {
                result += child.content
            } else if child.tagName == "br" {
                result += "\n"
            } else {
                // 递归处理其他元素节点，例如 <code>, <span> 等
                result += child.rawTextContentForPre()
            }
        }
        return result
    }
}

// MARK: - HTMLNode Array Extensions

// 用于 HTMLNode 到 ContentItem 的转换扩展
extension Array where Element == HTMLNode {
    /// 将 HTMLNode 数组转换为 ContentItem 数组，以便在 AttributedTextView 中显示
    func toContentItems(baseURL: URL? = nil) -> [ContentItem] {
        var contentItems: [ContentItem] = []
        var listItemNumber = 1
        for node in self {
            node.processIntoContentItems(into: &contentItems, baseURL: baseURL, parentLink: nil, isBold: false, isItalic: false, isUnderline: false, listLevel: 0, isOrderedList: false, listItemNumber: &listItemNumber)
        }
        return optimizeContentItems(contentItems)
    }
    
    /// 优化内容项列表，合并相邻文本，处理换行等
    private func optimizeContentItems(_ items: [ContentItem]) -> [ContentItem] {
        var result: [ContentItem] = []
        var accumulatedText: String? = nil // 用于累积相同格式的文本
        var isAccumulatedTextBold: Bool = false
        var isAccumulatedTextItalic: Bool = false
        var isAccumulatedTextUnderline: Bool = false

        func flushAccumulatedText() {
            if let text = accumulatedText {
                if !text.isEmpty {
                    result.append(.text(text, isBold: isAccumulatedTextBold, isItalic: isAccumulatedTextItalic, isUnderline: isAccumulatedTextUnderline))
                }
                accumulatedText = nil
            }
        }

        for item in items {
            switch item {
            case .text(let text, let isBold, let isItalic, let isUnderline):
                let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines) // 先修剪
                if trimmedText.isEmpty && text.contains("\n") { // 如果修剪后为空但原始包含换行
                    flushAccumulatedText()
                    result.append(.newline) // 视为纯换行
                } else if !trimmedText.isEmpty {
                    if accumulatedText != nil && isBold == isAccumulatedTextBold && isItalic == isAccumulatedTextItalic && isUnderline == isAccumulatedTextUnderline {
                        accumulatedText! += trimmedText // 追加到累积文本
                    } else {
                        flushAccumulatedText() // 先处理掉之前的累积文本
                        accumulatedText = trimmedText // 开始新的累积
                        isAccumulatedTextBold = isBold
                        isAccumulatedTextItalic = isItalic
                        isAccumulatedTextUnderline = isUnderline
                    }
                }
            case .link, .image, .newline, .heading1, .heading2, .heading3, .listItem, .footnoteReference, .footnoteContent, .spoiler, .horizontalRule, .poll:
                flushAccumulatedText() // 遇到非文本项，处理掉累积的文本
                result.append(item) // 直接添加非文本项
            }
        }
        flushAccumulatedText() // 处理循环结束后可能剩余的累积文本
        
        return result
    }
}

// MARK: - HTMLNode ContentItem Processing

extension HTMLNode {
    /// 将单个 HTMLNode 处理成 ContentItem 并添加到指定数组
    func processIntoContentItems(into contentItems: inout [ContentItem], baseURL: URL? = nil, parentLink: URL? = nil, isBold: Bool = false, isItalic: Bool = false, isUnderline: Bool = false, listLevel: Int, isOrderedList: Bool, listItemNumber: inout Int) {
        var currentLink = parentLink
        let effectiveIsBold = isBold || tagName == "strong" || tagName == "b"
        let effectiveIsItalic = isItalic || tagName == "i" || tagName == "em"
        let effectiveIsUnderline = isUnderline || tagName == "u"

        // 检查当前节点是否是链接
        var isLinkNode = false
        if tagName == "a", let rawHref = attributes["href"],
           let processedHref = HTMLParser.processURL(rawHref),
           let url = URL(string: processedHref, relativeTo: baseURL) {
            currentLink = url
            isLinkNode = true
        }

        // 检查是否是需要忽略的特殊span标签（在div内且有特定class）
        let isIgnoredSpan = tagName == "span" &&
                            (attributes["class"]?.contains("filename") == true ||
                             attributes["class"]?.contains("infomations") == true)

        // 检查是否是包含被忽略span的div（在链接内）
        let isContainerDiv = isLinkNode && tagName == "div" &&
                             children.contains {
                                 $0.tagName == "span" &&
                                 ($0.attributes["class"]?.contains("filename") == true ||
                                  $0.attributes["class"]?.contains("infomations") == true)
                             }

        // 如果是链接内的div>span(class="filename/infomations")结构，忽略整个div
        if isContainerDiv {
            return // 直接忽略这个div及其内容
        }

        // 如果是需要忽略的span标签，直接忽略
        if isIgnoredSpan {
            return // 直接忽略这个span及其内容
        }

        // 如果是链接节点，检查它是否只包含图片和需要忽略的元素
        if isLinkNode {
            // 找出所有的图片子节点
            let imgNodes = children.filter { $0.tagName == "img" }

            // 找出所有需要忽略的div子节点（包含特定class的span）
            let ignoredDivs = children.filter { child -> Bool in
                if child.tagName == "div" {
                    return child.children.contains {
                        $0.tagName == "span" &&
                        ($0.attributes["class"]?.contains("filename") == true ||
                         $0.attributes["class"]?.contains("infomations") == true)
                    }
                }
                return false
            }

            // 如果链接内只有图片和需要忽略的div，则只处理图片
            if !imgNodes.isEmpty && children.count == imgNodes.count + ignoredDivs.count {
                // 只处理图片节点，忽略其他内容
                for imgNode in imgNodes {
                    if let rawSrc = imgNode.attributes["src"],
                       let processedSrc = HTMLParser.processURL(rawSrc),
                       let imgURL = URL(string: processedSrc, relativeTo: baseURL) {
                        let alt = imgNode.attributes["alt"] ?? ""

                        // 获取图片的类别信息
                        let imgClass = imgNode.attributes["class"]?.lowercased() ?? ""
                        let alignAttr = imgNode.attributes["align"]?.lowercased() ?? ""
                        let dataDisplay = imgNode.attributes["data-display"]?.lowercased() ?? ""
                        let shouldCenter = imgClass.contains("center") || alignAttr == "center"
                        let isBlockDisplay = shouldCenter || dataDisplay == "block"

                        // 检查是否为 emoji 图片 - 通过 class 属性或图片 URL 和 alt 文本判断
                        let srcLower = rawSrc.lowercased()
                        let altLower = alt.lowercased()
                        let isEmoji = imgClass.contains("emoji") ||
                                      srcLower.contains("emoji") ||
                                      altLower.contains("emoji")

                        // 处理块级图片的换行
                        let previousItem = contentItems.last
                        if isBlockDisplay {
                            if previousItem == .newline {
                                contentItems.append(.newline)
                            } else if previousItem != nil {
                                contentItems.append(.newline)
                                contentItems.append(.newline)
                            }
                        }

                        // 添加图片，并传递链接URL作为附加信息
                        contentItems.append(.image(url: imgURL, altText: alt, width: imgNode.imageWidth, height: imgNode.imageHeight, linkURL: currentLink, isEmoji: isEmoji))

                        // 如果是块级图片，添加双换行
                        if isBlockDisplay {
                            contentItems.append(.newline)
                            contentItems.append(.newline)
                        }
                    }
                }
                return // 提前返回，不处理其他内容
            }
        }

        if isTextNode {
            let nodeContent = content
            if nodeContent.isEmpty { return }

            // 检查文本内容是否匹配脚注标记
            let footnoteRefRegex = try! NSRegularExpression(pattern: "\\[footnote-ref:(\\d+):([^\\]]*)\\]")
            let footnoteItemRegex = try! NSRegularExpression(pattern: "\\[footnote-item:(\\d+)\\]([\\s\\S]*?)\\[/footnote-item\\]")

            let refMatches = footnoteRefRegex.matches(in: nodeContent, range: NSRange(nodeContent.startIndex..., in: nodeContent))
            let itemMatches = footnoteItemRegex.matches(in: nodeContent, range: NSRange(nodeContent.startIndex..., in: nodeContent))

            if let match = refMatches.first {
                guard let indexRange = Range(match.range(at: 1), in: nodeContent),
                      let refTextRange = Range(match.range(at: 2), in: nodeContent) else {
                    return
                }
                let index = String(nodeContent[indexRange])
                let refText = String(nodeContent[refTextRange])
                
                // 创建一个单一的 ContentItem.footnoteReference
                contentItems.append(.footnoteReference(index: index, refText: refText))
                
            } else if let match = itemMatches.first {
                guard let indexRange = Range(match.range(at: 1), in: nodeContent),
                      let contentRange = Range(match.range(at: 2), in: nodeContent) else {
                    return
                }
                let index = String(nodeContent[indexRange])
                let content = String(nodeContent[contentRange])
                contentItems.append(.footnoteContent(index: index, content: content))
            } else {
                // 默认文本处理
                if let url = currentLink {
                    contentItems.append(.link(text: nodeContent, url: url))
                } else {
                    contentItems.append(.text(nodeContent, isBold: effectiveIsBold, isItalic: effectiveIsItalic, isUnderline: effectiveIsUnderline))
                }
            }
        } else if tagName == "img" {
            if let rawSrc = attributes["src"],
               let processedSrc = HTMLParser.processURL(rawSrc),
               let url = URL(string: processedSrc, relativeTo: baseURL) {
                let alt = attributes["alt"] ?? ""

                // 获取图片的类别信息 - 如果有 class 属性含有 "centered" 或 "center" 则作为居中图片
                let imgClass = attributes["class"]?.lowercased() ?? ""
                let alignAttr = attributes["align"]?.lowercased() ?? ""
                let dataDisplay = attributes["data-display"]?.lowercased() ?? ""
                let shouldCenter = imgClass.contains("center") || alignAttr == "center"
                let isBlockDisplay = shouldCenter || dataDisplay == "block"

                // 检查是否为 emoji 图片 - 通过 class 属性或图片 URL 和 alt 文本判断
                let srcLower = rawSrc.lowercased()
                let altLower = alt.lowercased()
                let isEmoji = imgClass.contains("emoji") ||
                              srcLower.contains("emoji") ||
                              altLower.contains("emoji")

                // 判断是否需要在图片前添加换行
                let previousItem = contentItems.last

                // 如果是块级图片，添加双换行（除非已经有换行符）
                if isBlockDisplay {
                    if previousItem == .newline {
                        // 如果最后一个元素已经是换行符，再添加一个
                        contentItems.append(.newline)
                    } else if previousItem != nil {
                        // 如果有前一个元素且不是换行符，添加两个
                        contentItems.append(.newline)
                        contentItems.append(.newline)
                    }
                }

                // 添加图片，如果图片在链接内，保存链接URL作为附加信息
                contentItems.append(.image(url: url, altText: alt, width: imageWidth, height: imageHeight, linkURL: currentLink, isEmoji: isEmoji))

                // 如果是块级图片，添加双换行
                if isBlockDisplay {
                    contentItems.append(.newline)
                    contentItems.append(.newline)
                }
            }
        } else if tagName == "span" && attributes["class"]?.contains("spoiler") == true {
            // 处理剧透标签
            var spoilerText = ""
            for child in children {
                if child.isTextNode {
                    spoilerText += child.content
                } else {
                    // 递归处理子节点以获取文本内容
                    var childContentItems: [ContentItem] = []
                    var childListItemNumber = 1
                    child.processIntoContentItems(into: &childContentItems, baseURL: baseURL, parentLink: currentLink, isBold: effectiveIsBold, isItalic: effectiveIsItalic, isUnderline: effectiveIsUnderline, listLevel: listLevel, isOrderedList: isOrderedList, listItemNumber: &childListItemNumber)

                    // 提取文本内容
                    for item in childContentItems {
                        switch item {
                        case .text(let text, _, _, _):
                            spoilerText += text
                        case .link(let text, _):
                            spoilerText += text
                        default:
                            break
                        }
                    }
                }
            }

            if !spoilerText.isEmpty {
                contentItems.append(.spoiler(text: spoilerText, isRevealed: false))
            }
        } else if tagName == "hr" {
            contentItems.append(.horizontalRule)
        } else if tagName == "blockquote" {
            // blockquote现在在MixedContentItem层面处理，这里跳过
            // 不处理blockquote，让它在toMixedContentItems中处理
            return
        } else if tagName == "li" {
            // 列表项处理 - 修复：正确处理li标签内部的HTML元素
            var listItemContentItems: [ContentItem] = []
            var childListItemNumber = 1

            // 递归处理li标签的所有子节点，保持格式信息
            for child in children {
                child.processIntoContentItems(into: &listItemContentItems, baseURL: baseURL, parentLink: currentLink, isBold: effectiveIsBold, isItalic: effectiveIsItalic, isUnderline: effectiveIsUnderline, listLevel: listLevel, isOrderedList: isOrderedList, listItemNumber: &childListItemNumber)
            }

            // 将处理后的内容项转换为纯文本，保留格式标记
            let listItemText = listItemContentItems.compactMap { item -> String? in
                switch item {
                case .text(let text, let isBold, let isItalic, let isUnderline):
                    // 保留格式信息的文本表示
                    var formattedText = text
                    if isBold { formattedText = "**\(formattedText)**" }
                    if isItalic { formattedText = "*\(formattedText)*" }
                    if isUnderline { formattedText = "_\(formattedText)_" }
                    return formattedText
                case .link(let text, _):
                    return text
                default:
                    return nil
                }
            }.joined()

            // 对于有序列表，使用当前序号并递增
            let currentNumber = isOrderedList ? listItemNumber : nil
            if isOrderedList {
                listItemNumber += 1
            }
            
            contentItems.append(.listItem(text: listItemText, level: listLevel, isOrdered: isOrderedList, number: currentNumber))
        } else {
            // 递归处理子节点
            let newIsOrderedList = tagName == "ol" || isOrderedList
            let newListLevel = (tagName == "ol" || tagName == "ul") ? listLevel + 1 : listLevel
            
            // 对于列表容器（ol/ul），重置列表项计数器
            if tagName == "ol" || tagName == "ul" {
                var childListItemNumber = 1
                for child in children {
                    child.processIntoContentItems(into: &contentItems, baseURL: baseURL, parentLink: currentLink, isBold: effectiveIsBold, isItalic: effectiveIsItalic, isUnderline: effectiveIsUnderline, listLevel: newListLevel, isOrderedList: newIsOrderedList, listItemNumber: &childListItemNumber)
                }
            } else {
                for child in children {
                    child.processIntoContentItems(into: &contentItems, baseURL: baseURL, parentLink: currentLink, isBold: effectiveIsBold, isItalic: effectiveIsItalic, isUnderline: effectiveIsUnderline, listLevel: newListLevel, isOrderedList: newIsOrderedList, listItemNumber: &listItemNumber)
                }
            }

            // 块级元素或 <br> 后添加换行，但仅当不在链接内部时
            if (Self.blockElements.contains(tagName) || tagName == "br") && currentLink == nil {
                // 对于块级元素，添加两个换行符，对于<br>仍然保持一个换行符
                if tagName == "br" {
                    // <br> 标签只添加一个换行符
                    if contentItems.last != .newline {
                        contentItems.append(.newline)
                    }
                } else if Self.blockElements.contains(tagName) {
                    // 块级元素添加两个换行符，除非已经有换行符
                    if contentItems.last == .newline {
                        // 如果最后一个元素已经是换行符，再添加一个
                        contentItems.append(.newline)
                    } else {
                        // 如果最后一个元素不是换行符，添加两个
                        contentItems.append(.newline)
                        contentItems.append(.newline)
                    }
                }
            }
        }
    }
}

// MARK: - Node Searching Extensions
extension HTMLNode {
    /// 查找具有特定标签名的所有后代节点（深度优先）
    func findAllNodes(withTagName tagName: String) -> [HTMLNode] {
        var results: [HTMLNode] = []
        for child in children {
            if child.tagName.lowercased() == tagName.lowercased() {
                results.append(child)
            }
            results.append(contentsOf: child.findAllNodes(withTagName: tagName))
        }
        return results
    }

    /// 查找具有特定标签名的第一个子节点（深度优先）
    func findNode(withTagName tagName: String) -> HTMLNode? {
        for child in children {
            if child.tagName.lowercased() == tagName.lowercased() {
                return child
            }
            if let found = child.findNode(withTagName: tagName) {
                return found
            }
        }
        return nil
    }

    /// 查找具有特定类的第一个子节点（深度优先）
    func findNode(withClass className: String) -> HTMLNode? {
        for child in children {
            if let classes = child.attributes["class"], classes.components(separatedBy: .whitespaces).contains(className) {
                return child
            }
            if let found = child.findNode(withClass: className) {
                return found
            }
        }
        return nil
    }
}
