//
//  HTMLOptimizations.swift
//  Lovelo
//
//  HTML解析器优化功能 - 简化高效版本
//

import Foundation

// MARK: - HTML解析器缓存

/// HTML解析器缓存 - 简化高效版本
class HTMLParserCache {

    // MARK: - Cache Configuration
    private let maxCacheSize: Int = 50
    private let maxContentLength: Int = 20000

    // MARK: - Cache Storage
    private var cache: [Int: CacheEntry] = [:]
    private var accessOrder: [Int] = []
    private let cacheQueue = DispatchQueue(label: "html.parser.cache", attributes: .concurrent)

    // MARK: - Cache Entry
    private struct CacheEntry {
        let content: String
        let timestamp: Date

        init(content: String) {
            self.content = content
            self.timestamp = Date()
        }

        /// 检查是否过期（5分钟）
        var isExpired: Bool {
            return Date().timeIntervalSince(timestamp) > 300
        }
    }
    
    // MARK: - Public Methods

    /// 获取缓存的解析结果
    /// - Parameter html: HTML内容
    /// - Returns: 缓存的解析结果，如果不存在则返回nil
    func getCachedResult(for html: String) -> String? {
        let key = generateCacheKey(for: html)

        return cacheQueue.sync {
            guard let entry = cache[key] else { return nil }

            // 检查是否过期
            if entry.isExpired {
                removeCacheEntry(key: key)
                return nil
            }

            // 更新访问记录
            updateAccessOrder(key: key)

            return entry.content
        }
    }

    /// 缓存解析结果
    /// - Parameters:
    ///   - html: HTML内容
    ///   - result: 解析结果
    func cacheResult(_ result: String, for html: String) {
        // 检查内容长度限制
        guard html.count <= maxContentLength else {
            return
        }

        let key = generateCacheKey(for: html)
        let entry = CacheEntry(content: result)

        cacheQueue.async(flags: .barrier) {
            // 添加到缓存
            self.cache[key] = entry
            self.updateAccessOrder(key: key)

            // 检查缓存大小限制
            self.enforceMaxCacheSize()
        }
    }

    /// 清空缓存
    func clearCache() {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeAll()
            self.accessOrder.removeAll()
        }
    }

    /// 获取缓存统计信息
    /// - Returns: 缓存统计信息
    func getCacheStats() -> (count: Int, totalMemory: Int) {
        return cacheQueue.sync {
            let totalMemory = cache.values.reduce(0) { $0 + $1.content.utf8.count }
            return (count: cache.count, totalMemory: totalMemory)
        }
    }

    // MARK: - Private Methods

    /// 生成缓存键 - 使用轻量级哈希
    /// - Parameter html: HTML内容
    /// - Returns: 缓存键
    private func generateCacheKey(for html: String) -> Int {
        return html.hashValue
    }

    /// 更新访问顺序
    /// - Parameter key: 缓存键
    private func updateAccessOrder(key: Int) {
        // 移除旧位置
        accessOrder.removeAll { $0 == key }
        // 添加到最前面
        accessOrder.insert(key, at: 0)
    }

    /// 强制执行最大缓存大小限制
    private func enforceMaxCacheSize() {
        while cache.count > maxCacheSize {
            // 移除最少使用的条目
            if let lruKey = accessOrder.last {
                removeCacheEntry(key: lruKey)
            }
        }
    }

    /// 移除缓存条目
    /// - Parameter key: 缓存键
    private func removeCacheEntry(key: Int) {
        cache.removeValue(forKey: key)
        accessOrder.removeAll { $0 == key }
    }
}
