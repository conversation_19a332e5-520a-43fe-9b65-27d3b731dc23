//
//  HTMLParser.swift
//  Lovelo
//
//  HTML解析服务 - 整合版本
//

import Foundation
import CryptoKit

// MARK: - HTML解析器配置

/// HTML解析器配置
class HTMLParserConfiguration {

    // MARK: - Properties

    /// 需要忽略的HTML标签
    var ignoreTags: Set<String>

    /// 需要忽略的CSS类名关键词
    var ignoreClasses: Set<String>

    /// HTML void元素列表
    var voidElements: Set<String>

    /// 块级元素列表
    var blockElements: Set<String>

    /// HTML实体映射表
    var htmlEntities: [String: String]

    /// 社交媒体分享链接模式
    var socialPatterns: [String]

    /// 需要忽略的链接类名
    var ignoreLinkClasses: Set<String>

    // MARK: - Initialization

    init() {
        self.ignoreTags = HTMLParserConstants.ignoreTags
        self.ignoreClasses = HTMLParserConstants.ignoreClasses
        self.voidElements = HTMLParserConstants.voidElements
        self.blockElements = HTMLParserConstants.blockElements
        self.htmlEntities = HTMLParserConstants.htmlEntities
        self.socialPatterns = HTMLParserConstants.socialPatterns
        self.ignoreLinkClasses = HTMLParserConstants.ignoreLinkClasses
    }

    // MARK: - Helper Methods

    /// 判断是否为void元素
    func isVoidElement(_ tagName: String) -> Bool {
        return voidElements.contains(tagName.lowercased())
    }

    /// 判断是否为块级元素
    func isBlockElement(_ tagName: String) -> Bool {
        return blockElements.contains(tagName.lowercased())
    }

    /// 判断是否应该忽略该标签
    func shouldIgnoreTag(_ tagName: String) -> Bool {
        return ignoreTags.contains(tagName.lowercased())
    }

    /// 判断是否应该忽略该类名
    func shouldIgnoreClass(_ className: String) -> Bool {
        let lowerClassName = className.lowercased()
        return ignoreClasses.contains { lowerClassName.contains($0) }
    }

    /// 判断是否应该忽略该链接类名
    func shouldIgnoreLinkClass(_ className: String) -> Bool {
        let lowerClassName = className.lowercased()
        return ignoreLinkClasses.contains { lowerClassName.contains($0) }
    }

    /// 判断是否为社交分享链接
    func isSocialShareLink(_ href: String) -> Bool {
        return socialPatterns.contains { href.contains($0) }
    }

    /// 处理和标准化URL - 解码HTML实体并进行清理
    /// - Parameter url: 原始URL字符串
    /// - Returns: 处理后的URL字符串
    func processURL(_ url: String?) -> String? {
        guard let url = url, !url.isEmpty else { return nil }

        // 解码HTML实体
        var processedURL = url
        for (entity, replacement) in htmlEntities {
            processedURL = processedURL.replacingOccurrences(of: entity, with: replacement)
        }

        // 去除首尾空白字符
        processedURL = processedURL.trimmingCharacters(in: .whitespacesAndNewlines)

        return processedURL.isEmpty ? nil : processedURL
    }
}

// MARK: - HTML标签解析器

/// HTML标签解析器
class HTMLTagParser {
    private let configuration = HTMLParserConfiguration()

    func parseTag(html: String, startIndex: String.Index) throws -> (HTMLNode, String.Index)? {
        guard startIndex < html.endIndex, html[startIndex] == "<" else { return nil }

        var currentPosition = html.index(after: startIndex)

        // 检查是否是注释或文档类型声明
        if currentPosition < html.endIndex {
            if html[currentPosition] == "!" {
                return try parseSpecialTag(html: html, startIndex: startIndex)
            }

            // 检查是否是结束标签
            if html[currentPosition] == "/" {
                return nil // 结束标签由父级处理
            }
        }

        // 解析开始标签名 - 使用优化的解析工具
        guard let (tagName, tagEndPosition) = FastHTMLParsingUtils.parseTagName(in: html, from: currentPosition) else {
            throw HTMLParser.ParserError.parsingError("无法解析标签名")
        }
        // print("【调试】HTMLTagParser: Parsed tag name: \(tagName)")

        currentPosition = tagEndPosition

        // 解析属性
        var attributes: [String: String] = [:]
        while currentPosition < html.endIndex && html[currentPosition] != ">" {
            if html[currentPosition].isWhitespace {
                currentPosition = html.index(after: currentPosition)
                continue
            }

            if html[currentPosition] == "/" {
                // 自闭合标签
                currentPosition = html.index(after: currentPosition)
                continue
            }

            // 解析属性
            if let (attrName, attrValue, newPos) = parseAttribute(html: html, startIndex: currentPosition) {
                attributes[attrName] = attrValue
                currentPosition = newPos
            } else {
                currentPosition = html.index(after: currentPosition)
            }
        }

        // 跳过 ">"
        if currentPosition < html.endIndex && html[currentPosition] == ">" {
            currentPosition = html.index(after: currentPosition)
        }

        // 创建节点
        var node = HTMLNode(tagName: tagName, attributes: attributes)

        // 如果是void元素，直接返回
        if configuration.isVoidElement(tagName) {
            return (node, currentPosition)
        }

        // 解析子节点
        var children: [HTMLNode] = []
        while currentPosition < html.endIndex {
            // 检查是否遇到结束标签
            if currentPosition < html.endIndex && html[currentPosition] == "<" {
                let nextIndex = html.index(after: currentPosition)
                if nextIndex < html.endIndex && html[nextIndex] == "/" {
                    // 找到结束标签，检查是否匹配
                    let endTagStart = html.index(nextIndex, offsetBy: 1)
                    var endTagEnd = endTagStart
                    while endTagEnd < html.endIndex && html[endTagEnd] != ">" {
                        endTagEnd = html.index(after: endTagEnd)
                    }

                    if endTagEnd < html.endIndex {
                        let endTagName = String(html[endTagStart..<endTagEnd]).trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
                        if endTagName == tagName {
                            // 匹配的结束标签
                            currentPosition = html.index(after: endTagEnd)
                            break
                        }
                    }
                }
            }

            // 解析子节点
            if html[currentPosition] == "<" {
                if let (childNode, newPos) = try parseTag(html: html, startIndex: currentPosition) {
                    children.append(childNode)
                    currentPosition = newPos
                } else {
                    currentPosition = html.index(after: currentPosition)
                }
            } else {
                if let (textNode, newPos) = parseTextNode(html: html, startIndex: currentPosition) {
                    children.append(textNode)
                    currentPosition = newPos
                } else {
                    currentPosition = html.index(after: currentPosition)
                }
            }
        }

        node.children = children
        return (node, currentPosition)
    }

    func parseTextNode(html: String, startIndex: String.Index) -> (HTMLNode, String.Index)? {
        // 使用优化的文本解析工具
        guard let (content, endIndex) = FastHTMLParsingUtils.parseTextContent(in: html, from: startIndex) else {
            return nil
        }

        let textNode = HTMLNode(textContent: content)
        return (textNode, endIndex)
    }

    private func parseSpecialTag(html: String, startIndex: String.Index) throws -> (HTMLNode, String.Index)? {
        // 处理注释和DOCTYPE等特殊标签
        var currentPosition = startIndex

        // 寻找结束位置
        while currentPosition < html.endIndex && html[currentPosition] != ">" {
            currentPosition = html.index(after: currentPosition)
        }

        if currentPosition < html.endIndex {
            currentPosition = html.index(after: currentPosition)
        }

        // 返回空节点（忽略特殊标签）
        return nil
    }

    private func parseAttribute(html: String, startIndex: String.Index) -> (String, String, String.Index)? {
        // 跳过空白字符 - 使用优化方法
        var currentPosition = html.fastSkipWhitespace(from: startIndex)

        guard currentPosition < html.endIndex else { return nil }

        // 解析属性名 - 使用优化的解析工具
        guard let (attributeName, nameEndPosition) = FastHTMLParsingUtils.parseAttributeName(in: html, from: currentPosition) else {
            return nil
        }

        currentPosition = nameEndPosition

        // 跳过空白字符
        currentPosition = html.fastSkipWhitespace(from: currentPosition)

        // 检查是否有等号
        guard currentPosition < html.endIndex && html[currentPosition] == "=" else {
            return (attributeName, "", currentPosition)
        }

        currentPosition = html.index(after: currentPosition)

        // 跳过空白字符
        currentPosition = html.fastSkipWhitespace(from: currentPosition)

        guard currentPosition < html.endIndex else { return (attributeName, "", currentPosition) }

        // 解析属性值 - 使用优化的解析工具
        guard let (attributeValue, valueEndPosition) = FastHTMLParsingUtils.parseAttributeValue(in: html, from: currentPosition) else {
            return (attributeName, "", currentPosition)
        }

        return (attributeName, attributeValue, valueEndPosition)
    }


}

/// HTML解析器
/// 解析HTML字符串为节点树结构
class HTMLParser {
    enum ParserError: Error {
        case invalidHTML
        case parsingError(String)
    }

    // MARK: - Dependencies
    private let tagParser = HTMLTagParser()
    private let configuration = HTMLParserConfiguration()

    // MARK: - Performance Optimizations
    private let unifiedProcessor = HTMLUnifiedProcessor()
    internal let cache = HTMLParserCache()

    // MARK: - Configuration
    internal let useCaching: Bool = true

    // MARK: - Public Methods

    /// 解析HTML字符串为节点树结构
    func parseToNodes(html: String) throws -> [HTMLNode] {
        var nodes: [HTMLNode] = []
        var currentPosition = html.startIndex

        while currentPosition < html.endIndex {
            currentPosition = html.fastSkipWhitespace(from: currentPosition)
            guard currentPosition < html.endIndex else { break }

            if html[currentPosition] == "<" {
                if let (node, newPosition) = try tagParser.parseTag(html: html, startIndex: currentPosition) {
                    nodes.append(node)
                    currentPosition = newPosition
                } else {
                    // 无法解析标签时，可以选择跳过或抛出错误
                    // 为了健壮性，我们先尝试跳过这个字符
                    currentPosition = html.index(after: currentPosition)
                }
            } else {
                if let (textNode, newPosition) = tagParser.parseTextNode(html: html, startIndex: currentPosition) {
                    nodes.append(textNode)
                    currentPosition = newPosition
                } else {
                    // 无法解析文本，跳过
                    currentPosition = html.index(after: currentPosition)
                }
            }
        }
        return nodes
    }

    func parse(html: String) throws -> [ContentItem] {
        // 检查缓存
        // if useCaching, let cachedResult = cache.getCachedContentItems(for: html) {
        //     return cachedResult
        // }

        let nodes = try parseToNodes(html: html)
        let result = parse(nodes: nodes)

        // 缓存结果
        // if useCaching {
        //     cache.cacheContentItems(result, for: html)
        // }

        return result
    }

    private func parse(nodes: [HTMLNode]) -> [ContentItem] {
        var items: [ContentItem] = []
        for node in nodes {
            items.append(contentsOf: parseNode(node))
        }
        return items
    }

    private func parseNode(_ node: HTMLNode) -> [ContentItem] {
        // 首先检查当前节点是否是投票节点
        let tagName = node.tagName
        let classes = node.attributes["class"]?.components(separatedBy: .whitespaces) ?? []

        if tagName == "div" && classes.contains("poll") {
            if let pollItem = parsePoll(node: node) {
                // 如果是投票节点，处理并返回，停止对该分支的进一步递归
                return [.poll(pollItem)]
            } else {
                // 如果解析失败，返回空数组
                return []
            }
        }

        // 如果不是投票节点，再处理其子节点
        if node.isTextNode {
            let cleanText = node.content.trimmingCharacters(in: .whitespacesAndNewlines)
            return cleanText.isEmpty ? [] : [.text(cleanText)]
        }
        
        var items: [ContentItem] = []
        for child in node.children {
            items.append(contentsOf: parseNode(child))
        }
        
        if configuration.isBlockElement(tagName) && !items.isEmpty {
            if let last = items.last, last != .newline {
                items.append(.newline)
            }
        }

        return items
    }

    private func parsePoll(node: HTMLNode) -> PollItem? {
        print("【调试】Entering parsePoll for node: \(node.tagName), children count: \(node.children.count)")

        guard let name = node.attributes["data-poll-name"],
              let type = node.attributes["data-poll-type"],
              let status = node.attributes["data-poll-status"],
              let resultsVisibility = node.attributes["data-poll-results"],
              let isPublicStr = node.attributes["data-poll-public"],
              let isPublic = Bool(isPublicStr),
              let minSelectionsStr = node.attributes["data-poll-min"],
              let minSelections = Int(minSelectionsStr),
              let maxSelectionsStr = node.attributes["data-poll-max"],
              let maxSelections = Int(maxSelectionsStr)
        else {
            return nil
        }

        var options: [PollOption] = []
        // 查找所有后代 li 节点，而不仅仅是直接子节点
        let liNodes = node.findAllNodes(withTagName: "li")
        print("【调试】Found liNodes count: \(liNodes.count)")

        for itemNode in liNodes {
            if let optionId = itemNode.attributes["data-poll-option-id"] {
                let optionLabel = itemNode.textContent.trimmingCharacters(in: .whitespacesAndNewlines)
                if !optionLabel.isEmpty {
                    options.append(PollOption(id: optionId, label: optionLabel))
                }
            }
        }

        var voterCount = 0
        if let infoNode = node.findNode(withClass: "poll-info_counts-count"),
           let numberNode = infoNode.findNode(withClass: "info-number") {
            let countStr = numberNode.textContent.trimmingCharacters(in: .whitespacesAndNewlines)
            if let count = Int(countStr) {
                voterCount = count
            }
        }

        let pollItem = PollItem(
            name: name,
            options: options,
            type: type,
            minSelections: minSelections,
            maxSelections: maxSelections,
            status: status,
            isPublic: isPublic,
            resultsVisibility: resultsVisibility,
            voterCount: voterCount
        )
        print("【调试】Parsed Poll Item: \(pollItem)")
        return pollItem
    }

    /// 从HTML中提取纯文本内容
    /// - Parameter html: HTML字符串
    /// - Returns: 提取的纯文本内容
    /// - Throws: 解析错误
    @available(*, deprecated, message: "Use parse(html:) to get ContentItems instead.")
    func extractContent(from html: String) throws -> String {
        let items = try parse(html: html)
        return items.map {
            switch $0 {
            case .text(let text, _, _, _): return text
            case .link(let text, _): return text
            default: return ""
            }
        }.joined(separator: " ")
    }

    // This method is now obsolete as parsing is done directly on nodes.
    // It can be removed or left as deprecated.
    @available(*, deprecated, message: "Parsing is now done directly from HTML nodes, not from intermediate text.")
    func parseToContentItems(from text: String) -> [ContentItem] {
        return [.text(text)]
    }

    /// 获取缓存统计信息
    /// - Returns: 缓存统计
    func getCacheStats() -> (count: Int, totalMemory: Int) {
        return cache.getCacheStats()
    }

    /// 清理缓存
    func clearCache() {
        cache.clearCache()
    }

    /// 静态方法：处理和标准化URL
    /// - Parameter url: 原始URL字符串
    /// - Returns: 处理后的URL字符串
    static func processURL(_ url: String?) -> String? {
        let config = HTMLParserConfiguration()
        return config.processURL(url)
    }
}
