//
//  HTMLParserConstants.swift
//  Lovelo
//
//  HTML解析器常量定义
//

import Foundation

/// HTML解析器常量
struct HTMLParserConstants {
    
    // MARK: - Void Elements
    
    /// HTML void元素列表（自闭合标签）
    static let voidElements: Set<String> = [
        "area", "base", "br", "col", "embed", "hr", "img", 
        "input", "link", "meta", "param", "source", "track", "wbr"
    ]
    
    // MARK: - Ignore Tags
    
    /// 需要忽略的HTML标签
    static let ignoreTags: Set<String> = [
        "nav", "footer", "header", "script", "style", "noscript", 
        "iframe", "form", "button", "input", "select", "option", 
        "meta", "link", "head", "svg", "path", "button"
    ]
    
    // MARK: - Ignore Classes
    
    /// 需要忽略的CSS类名关键词
    static let ignoreClasses: Set<String> = [
        "ad", "ads", "advertisement", "banner", "sidebar", "comment", "comments",
        "hidden", "nav", "navigation", "menu", "footer", "header", "social",
        "share", "related", "promo", "promotion", "sponsored", "widget", "cookie",
        "popup", "modal", "overlay", "subscribe", "newsletter", "login"
    ]
    
    // MARK: - Block Elements
    
    /// 块级元素列表
    static let blockElements: Set<String> = [
        "p", "h1", "h2", "h3", "h4", "h5", "h6", "div", "ul", "ol", "li", 
        "blockquote", "pre", "hr", "table", "form"
    ]
    
    // MARK: - HTML Entities
    
    /// HTML实体映射表
    static let htmlEntities: [String: String] = [
        "&nbsp;": " ", "&lt;": "<", "&gt;": ">", "&amp;": "&",
        "&quot;": "\"", "&apos;": "'", "&cent;": "¢", "&pound;": "£",
        "&yen;": "¥", "&euro;": "€", "&copy;": "©", "&reg;": "®"
    ]
    
    // MARK: - Social Patterns
    
    /// 社交媒体分享链接模式
    static let socialPatterns: [String] = [
        "facebook.com/share", "twitter.com/share", "linkedin.com/share", 
        "t.me/share", "/share", "/sharer", "weibo.com/share"
    ]
    
    // MARK: - Ignore Link Classes
    
    /// 需要忽略的链接类名
    static let ignoreLinkClasses: Set<String> = [
        "share", "social", "print", "email", "comment", "login", "signup"
    ]
}
