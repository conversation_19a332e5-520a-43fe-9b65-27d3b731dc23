//
//  HTMLProcessor.swift
//  Lovelo
//
//  HTML处理器 - 整合版本
//

import Foundation

// MARK: - 统一HTML处理器

/// 统一HTML处理器
/// 在单次遍历中完成预处理、文本提取和后处理
class HTMLUnifiedProcessor {
    
    // MARK: - Dependencies
    private let configuration = HTMLParserConfiguration()
    
    // MARK: - Processing Context
    struct ProcessingContext {
        var result: String = ""
        var shouldIgnoreNode: Bool = false
        var currentDepth: Int = 0
        var maxDepth: Int = 50 // 防止栈溢出
        
        // 性能统计
        var processedNodes: Int = 0
        var ignoredNodes: Int = 0
    }
    
    // MARK: - Public Methods
    
    /// 统一处理HTML节点，单次遍历完成所有操作
    /// - Parameter nodes: HTML节点数组
    /// - Returns: 处理后的文本内容
    func processNodes(_ nodes: [HTMLNode]) -> String {
        var context = ProcessingContext()

        for node in nodes {
            processNode(node, context: &context)
        }

        // 后处理文本
        let finalText = postprocessText(context.result)

        return finalText
    }
    
    // MARK: - Private Methods
    
    /// 处理单个节点
    /// - Parameters:
    ///   - node: HTML节点
    ///   - context: 处理上下文
    private func processNode(_ node: HTMLNode, context: inout ProcessingContext) {
        // 防止栈溢出
        guard context.currentDepth < context.maxDepth else {
            return
        }
        
        context.currentDepth += 1
        context.processedNodes += 1
        
        defer {
            context.currentDepth -= 1
        }
        
        // 检查是否应该忽略该节点
        if shouldIgnoreNode(node) {
            context.ignoredNodes += 1
            return
        }
        
        // 处理文本节点
        if node.isTextNode {
            let cleanText = node.content.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanText.isEmpty {
                context.result += cleanText + " "
            }
            return
        }
        
        // 处理元素节点
        processElementNode(node, context: &context)
    }
    
    /// 处理元素节点
    /// - Parameters:
    ///   - node: 元素节点
    ///   - context: 处理上下文
    private func processElementNode(_ node: HTMLNode, context: inout ProcessingContext) {
        let tagName = node.tagName // tagName已经在创建时规范化为小写
        let nodeClasses = node.attributes["class"]?.components(separatedBy: " ") ?? []
        
        switch tagName {
        case "pre":
            // 预格式化文本特殊处理
            let preContent = node.rawTextContentForPre()
            if !preContent.isEmpty {
                context.result += "\n\n" + preContent + "\n\n"
            }
            
        case "h1", "h2", "h3", "h4", "h5", "h6":
            // 标题处理
            let headingText = extractTextFromChildren(node.children)
            if !headingText.isEmpty {
                context.result += headingText + "\n\n"
            }
            
        case "p":
            // 段落处理
            if let specialText = handleSpecialParagraph(node, classes: nodeClasses) {
                context.result += specialText
            } else {
                let paragraphText = extractTextFromChildren(node.children)
                if !paragraphText.isEmpty {
                    context.result += paragraphText + "\n\n"
                }
            }
            
        case "a":
            // 链接处理
            let linkText = extractTextFromChildren(node.children)
            if let href = node.attributes["href"], shouldProcessLink(href, classes: nodeClasses) {
                context.result += linkText + " [" + href + "] "
            } else {
                context.result += linkText + " "
            }
            
        case "br":
            context.result += "\n"
            
        case "aside":
            // aside标签处理（非onebox类型）
            if !nodeClasses.contains("onebox") {
                let asideContent = extractTextFromChildren(node.children)
                if !asideContent.isEmpty {
                    let asideType = node.attributes["type"] ?? "note"
                    context.result += "\n[aside:\(asideType)]\n" + asideContent + "\n[/aside]\n\n"
                }
            }
            // onebox类型在MixedContentItem中处理
            
        case "div", "span", "main", "article", "section":
            // 容器元素处理
            if let specialText = handleSpecialContainer(node, classes: nodeClasses) {
                context.result += specialText
            } else {
                // 递归处理子节点
                for child in node.children {
                    processNode(child, context: &context)
                }
            }
            
        default:
            // 递归处理其他元素的子节点
            for child in node.children {
                processNode(child, context: &context)
            }
        }
    }
    
    /// 从子节点中提取文本
    /// - Parameter children: 子节点数组
    /// - Returns: 提取的文本
    private func extractTextFromChildren(_ children: [HTMLNode]) -> String {
        var context = ProcessingContext()
        for child in children {
            processNode(child, context: &context)
        }
        return context.result.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 判断是否应该忽略节点
    /// - Parameter node: HTML节点
    /// - Returns: 是否忽略
    private func shouldIgnoreNode(_ node: HTMLNode) -> Bool {
        // 根据标签忽略
        if configuration.shouldIgnoreTag(node.tagName) {
            return true
        }
        
        // 根据class和id忽略
        let nodeClasses = node.attributes["class"]?.components(separatedBy: " ") ?? []
        for cls in nodeClasses {
            if configuration.shouldIgnoreClass(cls) {
                return true
            }
        }
        
        // 检查id
        if let id = node.attributes["id"]?.lowercased() {
            if configuration.shouldIgnoreClass(id) {
                return true
            }
        }
        
        // 检查隐藏属性
        if let style = node.attributes["style"]?.lowercased() {
            if style.contains("display: none") || style.contains("visibility: hidden") {
                return true
            }
        }
        
        if node.attributes["hidden"] != nil || node.attributes["aria-hidden"]?.lowercased() == "true" {
            return true
        }
        
        return false
    }
    
    /// 处理特殊段落
    /// - Parameters:
    ///   - node: 段落节点
    ///   - classes: 节点的class列表
    /// - Returns: 特殊处理结果，nil表示使用默认处理
    private func handleSpecialParagraph(_ node: HTMLNode, classes: [String]) -> String? {
        // 处理引用段落
        if classes.contains(where: { $0.contains("quote") || $0.contains("blockquote") }) ||
           node.attributes["id"]?.contains("quote") == true {
            let quoteText = extractTextFromChildren(node.children)
            if !quoteText.isEmpty {
                return "\n> " + quoteText.replacingOccurrences(of: "\n", with: "\n> ") + "\n\n"
            }
        }
        
        return nil
    }
    
    /// 处理特殊容器
    /// - Parameters:
    ///   - node: 容器节点
    ///   - classes: 节点的class列表
    /// - Returns: 特殊处理结果，nil表示使用默认处理
    private func handleSpecialContainer(_ node: HTMLNode, classes: [String]) -> String? {
        // 处理剧透内容
        if classes.contains("spoiler") {
            let spoilerContent = extractTextFromChildren(node.children)
            if !spoilerContent.isEmpty {
                return "[spoiler]" + spoilerContent + "[/spoiler]"
            }
        }
        
        // 处理代码块
        if classes.contains(where: { $0.contains("code") || $0.contains("pre") }) ||
           node.tagName.lowercased() == "pre" {
            let codeText = extractTextFromChildren(node.children)
            if !codeText.isEmpty {
                return "\n```\n" + codeText + "\n```\n\n"
            }
        }
        
        // 处理引用块
        if classes.contains(where: { $0.contains("quote") || $0.contains("blockquote") }) ||
           node.tagName.lowercased() == "blockquote" {
            let quoteText = extractTextFromChildren(node.children)
            if !quoteText.isEmpty {
                return "\n> " + quoteText.replacingOccurrences(of: "\n", with: "\n> ") + "\n\n"
            }
        }
        
        return nil
    }
    
    /// 判断是否应该处理链接
    /// - Parameters:
    ///   - href: 链接地址
    ///   - classes: 节点的class列表
    /// - Returns: 是否处理
    private func shouldProcessLink(_ href: String, classes: [String]) -> Bool {
        // 检查是否为社交分享链接
        if configuration.isSocialShareLink(href) {
            return false
        }
        
        // 检查是否为忽略的链接类
        for cls in classes {
            if configuration.shouldIgnoreLinkClass(cls) {
                return false
            }
        }
        
        return true
    }
    
    /// 后处理文本
    /// - Parameter text: 原始文本
    /// - Returns: 处理后的文本
    private func postprocessText(_ text: String) -> String {
        var result = text
        
        // 首先处理脚注
        result = processFootnotes(in: result)
        
        // 规范化空白字符
        result = result.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        
        // 规范化多个连续换行
        result = result.replacingOccurrences(of: "\n{3,}", with: "\n\n", options: .regularExpression)
        
        // 清理URL显示格式
        result = result.replacingOccurrences(of: "\\s*\\[http(s)?://[^\\]]+\\]\\s*", with: " ", options: .regularExpression)
        
        // 删除HTML实体字符
        for (entity, replacement) in configuration.htmlEntities {
            result = result.replacingOccurrences(of: entity, with: replacement)
        }
        
        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 处理文本中的脚注
    /// - Parameter text: 原始文本
    /// - Returns: 处理脚注后的文本
    private func processFootnotes(in text: String) -> String {
        var processedText = text
        var footnotes: [(content: String, refText: String)] = []
        
        // 更新正则表达式以匹配 ^[内容]文本 格式
        let regex = try! NSRegularExpression(pattern: "\\^\\[([^\\]]+)\\]([^\\[\\s]*)")
        
        // 从后往前查找并替换，以避免在循环中修改字符串导致范围失效
        let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text)).reversed()
        
        for match in matches {
            // 捕获组 1 是脚注内容，捕获组 2 是引用文本
            guard let contentRange = Range(match.range(at: 1), in: processedText),
                  let refTextRange = Range(match.range(at: 2), in: processedText) else { continue }
            
            let content = String(processedText[contentRange])
            let refText = String(processedText[refTextRange])
            
            // 将解析出的脚注信息（内容和引用文本）添加到数组中
            // 因为我们是反向遍历，所以在这里插入到数组的开头
            footnotes.insert((content: content, refText: refText), at: 0)
            
            // 在原文中替换整个匹配项
            guard let matchRange = Range(match.range, in: processedText) else { continue }
            // 替换时，我们只留下一个临时的占位符，引用文本将在下一步被重新组合
            let placeholder = "[footnote-placeholder]"
            processedText.replaceSubrange(matchRange, with: placeholder)
        }
        
        // 如果没有找到脚注，直接返回原始文本
        if footnotes.isEmpty {
            return processedText
        }
        
        // 第二步：将占位符替换为最终的标记，并构建脚注列表
        var finalResult = ""
        var footnoteListText = "\n\n[footnotes-list]\n"
        var footnoteIndex = 1
        
        // 再次遍历文本，这次是为了替换占位符
        let components = processedText.components(separatedBy: "[footnote-placeholder]")
        for (i, component) in components.enumerated() {
            finalResult += component
            if i < footnotes.count {
                let footnote = footnotes[i]
                // 将引用文本编码到标记中
                let marker = "[footnote-ref:\(footnoteIndex):\(footnote.refText)]"
                finalResult += marker
                
                // 构建脚注列表项
                footnoteListText += "[footnote-item:\(footnoteIndex)]\(footnote.content)[/footnote-item]\n"
                footnoteIndex += 1
            }
        }
        
        footnoteListText += "[/footnotes-list]\n"
        finalResult.append(footnoteListText)
        
        return finalResult
    }
}
