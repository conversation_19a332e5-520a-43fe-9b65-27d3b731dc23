//
//  MixedContentItem.swift
//  Lovelo
//
//  混合内容项处理 - 整合版本
//

import SwiftUI

/// 混合内容项枚举，用于支持不同类型的内容渲染
enum MixedContentItem: Identifiable {
    /// 常规内容，使用AttributedTextView渲染
    case regularContent([ContentItem])

    /// 代码块内容，使用CodeBlockView渲染
    case codeBlock(content: String, theme: CodeBlockTheme)

    /// 链接卡片内容，使用LinkCardView渲染
    case linkCard(imageURL: String?, url: String?, domain: String?, title: String?, description: String?)

    /// 投票内容，使用PollView渲染
    case poll(PollItem)

    /// 引用块内容，使用BlockquoteView渲染
    case blockquote([ContentItem])

    /// 唯一标识符
    var id: String {
        switch self {
        case .regularContent(let items):
            return "regular_" + items.map { $0.id }.joined(separator: "_")
        case .codeBlock(let content, let theme):
            return "code_" + content.hashValue.description + "_" + String(describing: theme)
        case .linkCard(let imageURL, let url, let domain, let title, let description):
            return "linkcard_" + [imageURL, url, domain, title, description].compactMap { $0 }.joined(separator: "_").hashValue.description
        case .poll(let item):
            return "poll_\(item.id)"
        case .blockquote(let items):
            return "blockquote_" + items.map { $0.id }.joined(separator: "_").hashValue.description
        }
    }
}

/// 用于将HTMLNode数组转换为MixedContentItem数组的扩展
extension Array where Element == HTMLNode {
    /// 将HTML节点转换为混合内容项
    func toMixedContentItems(baseURL: URL? = nil) -> [MixedContentItem] {
        var mixedItems: [MixedContentItem] = []
        var currentRegularNodes: [HTMLNode] = []
        
        // 辅助函数：将累积的常规内容节点转换为ContentItem并添加到结果中
        func flushRegularNodes() {
            if !currentRegularNodes.isEmpty {
                let contentItems = currentRegularNodes.toContentItems(baseURL: baseURL)
                if !contentItems.isEmpty {
                    mixedItems.append(.regularContent(contentItems))
                }
                currentRegularNodes = []
            }
        }
        
        // 遍历所有节点
        for node in self {
            let tagName = node.tagName.lowercased()
            let classes = node.attributes["class"]?.components(separatedBy: .whitespaces) ?? []

            // 处理代码块 (<pre> 标签)
            if tagName == "pre" {
                // 先处理之前累积的常规内容
                flushRegularNodes()

                // 提取代码块内容并添加为代码块项
                let codeContent = node.rawTextContentForPre()
                // 根据节点的class或其他属性选择合适的主题
                let theme = determineCodeBlockTheme(node)
                mixedItems.append(.codeBlock(content: codeContent, theme: theme))
            }
            // 处理投票
            else if tagName == "div" && classes.contains("poll") {
                flushRegularNodes()
                if let pollItem = parsePoll(node: node) {
                    mixedItems.append(.poll(pollItem))
                }
            }
            // 处理blockquote标签
            else if tagName == "blockquote" {
                flushRegularNodes()

                // 解析blockquote内容为ContentItem数组
                var blockquoteContentItems: [ContentItem] = []
                var blockquoteListItemNumber = 1
                node.processIntoContentItems(into: &blockquoteContentItems, baseURL: nil, parentLink: nil, isBold: false, isItalic: false, isUnderline: false, listLevel: 0, isOrderedList: false, listItemNumber: &blockquoteListItemNumber)

                // 如果有内容，添加为blockquote项
                if !blockquoteContentItems.isEmpty {
                    mixedItems.append(.blockquote(blockquoteContentItems))
                }
            }
            // 处理aside标签 (onebox类型)
            else if tagName == "aside" {
                if classes.contains("onebox") {
                    // 先处理之前累积的常规内容
                    flushRegularNodes()

                    // 直接处理onebox aside标签
                    if let linkCardItem = handleOneboxAsideNode(node) {
                        print("【调试】toMixedContentItems: 成功处理onebox aside，添加linkcard")
                        mixedItems.append(linkCardItem)
                    } else {
                        print("【调试】toMixedContentItems: onebox aside处理失败，作为常规内容处理")
                        // 如果处理失败，作为常规内容处理
                        currentRegularNodes.append(node)
                    }
                } else {
                    // 非onebox的aside标签，作为常规内容处理
                    currentRegularNodes.append(node)
                }
            } else {
                // 累积常规内容节点
                currentRegularNodes.append(node)
            }
        }
        
        // 确保处理完成后的常规内容也被添加
        flushRegularNodes()
        
        return mixedItems
    }
    
    /// 根据节点属性确定代码块主题
    private func determineCodeBlockTheme(_ node: HTMLNode) -> CodeBlockTheme {
        // 默认主题
        var theme: CodeBlockTheme = .blue
        
        // 检查节点的class属性
        if let classAttr = node.attributes["class"] {
            let classes = classAttr.lowercased().split(separator: " ")
            
            // 根据class选择主题
            if classes.contains("language-swift") || classes.contains("swift") {
                theme = .orange
            } else if classes.contains("language-javascript") || classes.contains("javascript") || classes.contains("js") {
                theme = .blue
            } else if classes.contains("language-python") || classes.contains("python") {
                theme = .green
            } else if classes.contains("language-ruby") || classes.contains("ruby") {
                theme = .purple
            }
        }
        
        return theme
    }

    /// 处理onebox类型的aside节点
    /// - Parameter node: aside节点
    /// - Returns: LinkCard类型的MixedContentItem
    private func handleOneboxAsideNode(_ node: HTMLNode) -> MixedContentItem? {
        print("【调试】handleOneboxAsideNode: 开始处理onebox aside节点")

        // 提取数据并使用全局URL处理方法
        let rawDataOneboxSrc = node.attributes["data-onebox-src"]
        let dataOneboxSrc = HTMLParser.processURL(rawDataOneboxSrc)

        print("【调试】handleOneboxAsideNode: 原始URL: \(rawDataOneboxSrc ?? "nil")")
        print("【调试】handleOneboxAsideNode: 处理后URL: \(dataOneboxSrc ?? "nil")")

        var headerImageURL: String?
        var headerLinkText: String?
        var articleTitle: String?
        var articleDescription: String?

        // 查找header和article部分
        for child in node.children {
            if child.tagName.lowercased() == "header" {
                let headerClasses = child.attributes["class"]?.components(separatedBy: " ") ?? []
                if headerClasses.contains("source") {
                    // 在header中查找图片和链接文本
                    extractHeaderContent(from: child, imageURL: &headerImageURL, linkText: &headerLinkText)
                }
            } else if child.tagName.lowercased() == "article" {
                let articleClasses = child.attributes["class"]?.components(separatedBy: " ") ?? []
                if articleClasses.contains("onebox-body") {
                    // 在article中递归查找标题和描述
                    extractArticleContent(from: child, title: &articleTitle, description: &articleDescription)
                }
            }
        }

        print("【调试】handleOneboxAsideNode: 提取数据完成 - title: \(articleTitle ?? "nil"), description: \(articleDescription ?? "nil")")

        return .linkCard(imageURL: headerImageURL, url: dataOneboxSrc, domain: headerLinkText, title: articleTitle, description: articleDescription)
    }

    /// 从header中提取图片URL和链接文本
    /// - Parameters:
    ///   - headerNode: header节点
    ///   - imageURL: 图片URL（输出参数）
    ///   - linkText: 链接文本（输出参数）
    private func extractHeaderContent(from headerNode: HTMLNode, imageURL: inout String?, linkText: inout String?) {
        for headerChild in headerNode.children {
            if headerChild.tagName.lowercased() == "img" {
                imageURL = headerChild.attributes["src"]
            }
            if headerChild.tagName.lowercased() == "a" {
                linkText = extractTextFromNode(headerChild)
            }
        }
    }

    /// 从article中递归提取标题和描述
    /// - Parameters:
    ///   - articleNode: article节点
    ///   - title: 标题（输出参数）
    ///   - description: 描述（输出参数）
    private func extractArticleContent(from articleNode: HTMLNode, title: inout String?, description: inout String?) {
        // 递归查找h3和p标签
        func findTitleAndDescription(in node: HTMLNode) {
            // 查找标题 - h3标签
            if node.tagName.lowercased() == "h3" {
                if let aTag = node.children.first(where: { $0.tagName.lowercased() == "a" }) {
                    title = extractTextFromNode(aTag)
                } else {
                    title = extractTextFromNode(node)
                }
                print("【调试】找到标题: \(title ?? "nil")")
            }
            // 查找描述 - p标签
            else if node.tagName.lowercased() == "p" {
                description = extractTextFromNode(node)
                print("【调试】找到描述: \(description ?? "nil")")
            }

            // 递归处理子节点
            for child in node.children {
                findTitleAndDescription(in: child)
            }
        }

        findTitleAndDescription(in: articleNode)
    }

    /// 从HTMLNode提取纯文本内容
    private func extractTextFromNode(_ node: HTMLNode) -> String {
        return node.textContent.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 解析投票节点
    private func parsePoll(node: HTMLNode) -> PollItem? {
        guard let name = node.attributes["data-poll-name"],
              let type = node.attributes["data-poll-type"],
              let status = node.attributes["data-poll-status"],
              let resultsVisibility = node.attributes["data-poll-results"],
              let isPublicStr = node.attributes["data-poll-public"],
              let isPublic = Bool(isPublicStr),
              let minSelectionsStr = node.attributes["data-poll-min"],
              let minSelections = Int(minSelectionsStr),
              let maxSelectionsStr = node.attributes["data-poll-max"],
              let maxSelections = Int(maxSelectionsStr)
        else {
            return nil
        }

        var options: [PollOption] = []
        if let listNode = node.children.first(where: { $0.tagName == "ul" }) {
            for itemNode in listNode.children where itemNode.tagName == "li" {
                if let optionId = itemNode.attributes["data-poll-option-id"] {
                    let optionLabel = itemNode.textContent.trimmingCharacters(in: .whitespacesAndNewlines)
                    if !optionLabel.isEmpty {
                        options.append(PollOption(id: optionId, label: optionLabel))
                    }
                }
            }
        }

        var voterCount = 0
        if let infoNode = node.findNode(withClass: "poll-info_counts-count"),
           let numberNode = infoNode.findNode(withClass: "info-number") {
            let countStr = numberNode.textContent.trimmingCharacters(in: .whitespacesAndNewlines)
            if let count = Int(countStr) {
                voterCount = count
            }
        }

        return PollItem(
            name: name,
            options: options,
            type: type,
            minSelections: minSelections,
            maxSelections: maxSelections,
            status: status,
isPublic: isPublic,
            resultsVisibility: resultsVisibility,
            voterCount: voterCount
        )
    }
}
