//
//  StringParsingOptimizations.swift
//  Lovelo
//
//  字符串解析性能优化扩展
//

import Foundation

// MARK: - String解析性能优化扩展

extension String {
    
    /// 高效查找字符位置 - 避免创建Substring
    /// - Parameters:
    ///   - character: 要查找的字符
    ///   - startIndex: 开始位置
    /// - Returns: 字符位置，如果未找到返回nil
    func fastFirstIndex(of character: Character, from startIndex: String.Index) -> String.Index? {
        var currentIndex = startIndex
        
        while currentIndex < endIndex {
            if self[currentIndex] == character {
                return currentIndex
            }
            currentIndex = index(after: currentIndex)
        }
        
        return nil
    }
    
    /// 高效查找字符集中任意字符的位置
    /// - Parameters:
    ///   - characters: 字符集
    ///   - startIndex: 开始位置
    /// - Returns: 字符位置，如果未找到返回nil
    func fastFirstIndex(ofAny characters: Set<Character>, from startIndex: String.Index) -> String.Index? {
        var currentIndex = startIndex
        
        while currentIndex < endIndex {
            if characters.contains(self[currentIndex]) {
                return currentIndex
            }
            currentIndex = index(after: currentIndex)
        }
        
        return nil
    }
    
    /// 高效跳过字符集中的字符
    /// - Parameters:
    ///   - characters: 要跳过的字符集
    ///   - startIndex: 开始位置
    /// - Returns: 跳过后的位置
    func fastSkip(characters: Set<Character>, from startIndex: String.Index) -> String.Index {
        var currentIndex = startIndex
        
        while currentIndex < endIndex && characters.contains(self[currentIndex]) {
            currentIndex = index(after: currentIndex)
        }
        
        return currentIndex
    }
    
    /// 高效跳过空白字符
    /// - Parameter startIndex: 开始位置
    /// - Returns: 跳过空白字符后的位置
    func fastSkipWhitespace(from startIndex: String.Index) -> String.Index {
        var currentIndex = startIndex
        
        while currentIndex < endIndex && self[currentIndex].isWhitespace {
            currentIndex = index(after: currentIndex)
        }
        
        return currentIndex
    }
    
    /// 高效提取子字符串 - 减少String.Index操作
    /// - Parameters:
    ///   - startIndex: 开始位置
    ///   - endIndex: 结束位置
    /// - Returns: 提取的字符串
    func fastSubstring(from startIndex: String.Index, to endIndex: String.Index) -> String {
        return String(self[startIndex..<endIndex])
    }
    
    /// 高效查找标签结束位置
    /// - Parameter startIndex: 开始位置（应该指向'<'字符）
    /// - Returns: '>'字符的位置，如果未找到返回nil
    func fastFindTagEnd(from startIndex: String.Index) -> String.Index? {
        var currentIndex = startIndex
        var inQuotes = false
        var quoteChar: Character = "\""
        
        // 跳过开始的'<'
        if currentIndex < endIndex && self[currentIndex] == "<" {
            currentIndex = index(after: currentIndex)
        }
        
        while currentIndex < endIndex {
            let char = self[currentIndex]
            
            if char == "\"" || char == "'" {
                if !inQuotes {
                    inQuotes = true
                    quoteChar = char
                } else if char == quoteChar {
                    inQuotes = false
                }
            } else if char == ">" && !inQuotes {
                return currentIndex
            }
            
            currentIndex = index(after: currentIndex)
        }
        
        return nil
    }
}

// MARK: - HTML解析专用字符集

struct HTMLParsingCharacterSets {
    /// 空白字符集
    static let whitespace: Set<Character> = [" ", "\t", "\n", "\r"]
    
    /// 标签名结束字符集
    static let tagNameTerminators: Set<Character> = [" ", "\t", "\n", "\r", ">", "/"]
    
    /// 属性名结束字符集
    static let attributeNameTerminators: Set<Character> = [" ", "\t", "\n", "\r", "=", ">"]
    
    /// 属性值结束字符集（不带引号）
    static let attributeValueTerminators: Set<Character> = [" ", "\t", "\n", "\r", ">"]
    
    /// 引号字符集
    static let quotes: Set<Character> = ["\"", "'"]
    
    /// HTML特殊字符
    static let htmlSpecialChars: Set<Character> = ["<", ">", "&"]
}

// MARK: - 高性能HTML解析辅助工具

struct FastHTMLParsingUtils {
    
    /// 快速解析标签名
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - startIndex: 开始位置（应该指向标签名的第一个字符）
    /// - Returns: (标签名, 标签名结束位置)
    static func parseTagName(in html: String, from startIndex: String.Index) -> (String, String.Index)? {
        guard startIndex < html.endIndex else { return nil }
        
        let endIndex = html.fastFirstIndex(ofAny: HTMLParsingCharacterSets.tagNameTerminators, from: startIndex) ?? html.endIndex
        
        guard endIndex > startIndex else { return nil }
        
        let tagName = html.fastSubstring(from: startIndex, to: endIndex)
        return (tagName.lowercased(), endIndex)
    }
    
    /// 快速解析属性名
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - startIndex: 开始位置
    /// - Returns: (属性名, 属性名结束位置)
    static func parseAttributeName(in html: String, from startIndex: String.Index) -> (String, String.Index)? {
        guard startIndex < html.endIndex else { return nil }
        
        let endIndex = html.fastFirstIndex(ofAny: HTMLParsingCharacterSets.attributeNameTerminators, from: startIndex) ?? html.endIndex
        
        guard endIndex > startIndex else { return nil }
        
        let attributeName = html.fastSubstring(from: startIndex, to: endIndex)
        return (attributeName, endIndex)
    }
    
    /// 快速解析属性值
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - startIndex: 开始位置（应该指向属性值的第一个字符或引号）
    /// - Returns: (属性值, 属性值结束位置)
    static func parseAttributeValue(in html: String, from startIndex: String.Index) -> (String, String.Index)? {
        guard startIndex < html.endIndex else { return nil }
        
        let firstChar = html[startIndex]
        
        if HTMLParsingCharacterSets.quotes.contains(firstChar) {
            // 带引号的属性值
            let valueStart = html.index(after: startIndex)
            let endIndex = html.fastFirstIndex(of: firstChar, from: valueStart) ?? html.endIndex
            
            let attributeValue = html.fastSubstring(from: valueStart, to: endIndex)
            let nextIndex = endIndex < html.endIndex ? html.index(after: endIndex) : html.endIndex
            
            return (attributeValue, nextIndex)
        } else {
            // 不带引号的属性值
            let endIndex = html.fastFirstIndex(ofAny: HTMLParsingCharacterSets.attributeValueTerminators, from: startIndex) ?? html.endIndex
            
            let attributeValue = html.fastSubstring(from: startIndex, to: endIndex)
            return (attributeValue, endIndex)
        }
    }
    
    /// 快速提取文本内容（直到遇到'<'）
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - startIndex: 开始位置
    /// - Returns: (文本内容, 文本结束位置)
    static func parseTextContent(in html: String, from startIndex: String.Index) -> (String, String.Index)? {
        guard startIndex < html.endIndex else { return nil }
        
        let endIndex = html.fastFirstIndex(of: "<", from: startIndex) ?? html.endIndex
        
        guard endIndex > startIndex else { return nil }
        
        let content = html.fastSubstring(from: startIndex, to: endIndex).trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !content.isEmpty else { return nil }
        
        return (content, endIndex)
    }
}
