import UIKit

// 图片缓存管理器 - 保持向后兼容性的包装器
@MainActor
class ImageCache {
    static let shared = ImageCache()

    private init() {}

    func setImage(_ image: UIImage, forKey key: String) {
        // 直接存储到内存缓存中，key是缓存键而不是URL
        ImageManager.shared.memoryCache.setObject(image, forKey: key as NSString)
    }

    func image(forKey key: String) -> UIImage? {
        // 直接从内存缓存获取图片，key是缓存键而不是URL
        return ImageManager.shared.memoryCache.object(forKey: key as NSString)
    }

    func placeholderImage(forKey key: String, size: CGSize, text: String) -> UIImage {
        return ImageManager.shared.getPlaceholderImage(size: size, text: text)
    }

    func clearCache() {
        ImageManager.shared.clearCache()
    }
}