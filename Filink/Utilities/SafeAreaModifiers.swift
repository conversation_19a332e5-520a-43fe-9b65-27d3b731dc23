import SwiftUI
import Combine

// MARK: - 方案1：使用GeometryReader + safeAreaInsets + onAppear

// 方案1修饰符
struct SafeAreaCapture1Modifier: ViewModifier {
    @Binding var safeAreaInsets: EdgeInsets
    
    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            self.safeAreaInsets = geometry.safeAreaInsets
                        }
                        .onChange(of: geometry.safeAreaInsets) { newValue in
                            self.safeAreaInsets = newValue
                        }
                }
            )
    }
}

// MARK: - 方案2：使用UIKit API直接获取安全区域

// 静态方法获取安全区域
struct SafeAreaHelper {
    // 获取UIKit安全区域
    static var uiKitSafeAreaInsets: UIEdgeInsets {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .compactMap { $0 as? UIWindowScene }
            .first?.windows
            .filter { $0.isKeyWindow }
            .first
        
        return keyWindow?.safeAreaInsets ?? .zero
    }
    
    // 转换为SwiftUI EdgeInsets
    static var swiftUISafeAreaInsets: EdgeInsets {
        let uiKitInsets = uiKitSafeAreaInsets
        return EdgeInsets(
            top: uiKitInsets.top,
            leading: uiKitInsets.left,
            bottom: uiKitInsets.bottom,
            trailing: uiKitInsets.right
        )
    }
    
    // 单独获取顶部安全区域
    static var topSafeArea: CGFloat {
        return uiKitSafeAreaInsets.top
    }
    
    // 单独获取底部安全区域
    static var bottomSafeArea: CGFloat {
        return uiKitSafeAreaInsets.bottom
    }
}

// 方案2修饰符 - 将UIKit安全区域转换为SwiftUI修饰符
struct SafeAreaCapture2Modifier: ViewModifier {
    @Binding var safeAreaInsets: EdgeInsets
    @Binding var topInset: CGFloat
    @Binding var bottomInset: CGFloat
    
    // 可以只绑定EdgeInsets
    init(safeAreaInsets: Binding<EdgeInsets>) {
        self._safeAreaInsets = safeAreaInsets
        self._topInset = .constant(0)
        self._bottomInset = .constant(0)
        self.bindTopBottom = false
    }
    
    // 可以只绑定top和bottom
    init(top: Binding<CGFloat>, bottom: Binding<CGFloat>) {
        self._safeAreaInsets = .constant(.init())
        self._topInset = top
        self._bottomInset = bottom
        self.bindEdgeInsets = false
    }
    
    // 可以同时绑定
    init(safeAreaInsets: Binding<EdgeInsets>, top: Binding<CGFloat>, bottom: Binding<CGFloat>) {
        self._safeAreaInsets = safeAreaInsets
        self._topInset = top
        self._bottomInset = bottom
    }
    
    private var bindEdgeInsets = true
    private var bindTopBottom = true
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                updateSafeAreaValues()
                
                // 添加通知观察者，监听设备旋转
                NotificationCenter.default.addObserver(
                    forName: UIDevice.orientationDidChangeNotification,
                    object: nil,
                    queue: .main
                ) { _ in
                    // 设备旋转时更新安全区域值
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        updateSafeAreaValues()
                    }
                }
            }
    }
    
    private func updateSafeAreaValues() {
        let uiKitInsets = SafeAreaHelper.uiKitSafeAreaInsets
        
        if bindEdgeInsets {
            safeAreaInsets = EdgeInsets(
                top: uiKitInsets.top,
                leading: uiKitInsets.left,
                bottom: uiKitInsets.bottom,
                trailing: uiKitInsets.right
            )
        }
        
        if bindTopBottom {
            topInset = uiKitInsets.top
            bottomInset = uiKitInsets.bottom
        }
    }
}

// MARK: - 方案3：使用GeometryReader + 背景

// 方案3修饰符
struct SafeAreaCapture3Modifier: ViewModifier {
    @Binding var safeAreaInsets: EdgeInsets
    
    func body(content: Content) -> some View {
        content
            .background(
                Color.clear
                    .background(
                        GeometryReader { geometry in
                            Color.clear
                                .onAppear {
                                    safeAreaInsets = geometry.safeAreaInsets
                                }
                                .onChange(of: geometry.safeAreaInsets) { newValue in
                                    safeAreaInsets = newValue
                                }
                        }
                    )
            )
    }
}

// MARK: - 方案4：使用safeAreaInset修饰符（iOS 15新特性）

// 方案4修饰符
struct SafeAreaCapture4Modifier: ViewModifier {
    @Binding var topSafeArea: CGFloat
    @Binding var bottomSafeArea: CGFloat
    
    func body(content: Content) -> some View {
        content
            .background(
                VStack {
                    GeometryReader { geometry in
                        Color.clear
                            .onAppear {
                                topSafeArea = geometry.safeAreaInsets.top
                                bottomSafeArea = geometry.safeAreaInsets.bottom
                            }
                            .onChange(of: geometry.safeAreaInsets) { newValue in
                                topSafeArea = newValue.top
                                bottomSafeArea = newValue.bottom
                            }
                    }
                }
                .safeAreaInset(edge: .top) {
                    Color.clear.frame(height: 0)
                }
                .safeAreaInset(edge: .bottom) {
                    Color.clear.frame(height: 0)
                }
            )
    }
}

// MARK: - 方案5：使用GeometryReader + ignoresSafeArea

// 方案5修饰符
struct SafeAreaCapture5Modifier: ViewModifier {
    @Binding var safeAreaInsets: EdgeInsets
    
    func body(content: Content) -> some View {
        content
            .background(
                ZStack {
                    GeometryReader { outerGeometry in
                        Color.clear.ignoresSafeArea()
                            .overlay(
                                GeometryReader { innerGeometry in
                                    Color.clear
                                        .onAppear {
                                            // 计算安全区域
                                            safeAreaInsets = innerGeometry.safeAreaInsets
                                        }
                                        .onChange(of: innerGeometry.safeAreaInsets) { newValue in
                                            safeAreaInsets = newValue
                                        }
                                }
                            )
                    }
                }
            )
    }
}

// MARK: - 安全区域顶部变化监听器

// 用于监听顶部安全区域变化的视图
struct SafeAreaTopChangeListener: View {
    let action: (CGFloat) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            Color.clear
                .onAppear {
                    action(geometry.safeAreaInsets.top)
                }
                .onChange(of: geometry.safeAreaInsets) { newValue in
                    action(newValue.top)
                }
        }
    }
}

// MARK: - 公共扩展

extension View {
    /// 方案1: 使用GeometryReader + safeAreaInsets + onAppear
    func captureSafeArea1(to safeAreaInsets: Binding<EdgeInsets>) -> some View {
        self.modifier(SafeAreaCapture1Modifier(safeAreaInsets: safeAreaInsets))
    }
    
    /// 方案2: 使用UIKit API获取安全区域 (绑定EdgeInsets)
    func captureSafeArea2(to safeAreaInsets: Binding<EdgeInsets>) -> some View {
        self.modifier(SafeAreaCapture2Modifier(safeAreaInsets: safeAreaInsets))
    }
    
    /// 方案2: 使用UIKit API获取安全区域 (绑定top和bottom)
    func captureSafeArea2(top: Binding<CGFloat>, bottom: Binding<CGFloat>) -> some View {
        self.modifier(SafeAreaCapture2Modifier(top: top, bottom: bottom))
    }
    
    /// 方案2: 使用UIKit API获取安全区域 (同时绑定EdgeInsets和top/bottom)
    func captureSafeArea2(to safeAreaInsets: Binding<EdgeInsets>, top: Binding<CGFloat>, bottom: Binding<CGFloat>) -> some View {
        self.modifier(SafeAreaCapture2Modifier(safeAreaInsets: safeAreaInsets, top: top, bottom: bottom))
    }
    
    /// 方案3: 使用GeometryReader + 背景视图
    func captureSafeArea3(to safeAreaInsets: Binding<EdgeInsets>) -> some View {
        self.modifier(SafeAreaCapture3Modifier(safeAreaInsets: safeAreaInsets))
    }
    
    /// 方案4: 使用safeAreaInset修饰符（iOS 15新特性）
    func captureSafeArea4(top: Binding<CGFloat>, bottom: Binding<CGFloat>) -> some View {
        self.modifier(SafeAreaCapture4Modifier(topSafeArea: top, bottomSafeArea: bottom))
    }
    
    /// 方案5: 使用GeometryReader + ignoresSafeArea组合
    func captureSafeArea5(to safeAreaInsets: Binding<EdgeInsets>) -> some View {
        self.modifier(SafeAreaCapture5Modifier(safeAreaInsets: safeAreaInsets))
    }
    
    /// 监听顶部安全区域变化的修饰符
    func onSafeAreaTopChange(perform action: @escaping (CGFloat) -> Void) -> some View {
        self.background(SafeAreaTopChangeListener(action: action))
    }
} 