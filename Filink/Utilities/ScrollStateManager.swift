import SwiftUI
import Combine

/// 滚动状态管理器 - 用于优化滚动性能
class ScrollStateManager: ObservableObject {
    // 单例模式，让所有组件都可以访问同一个实例
    static let shared = ScrollStateManager()
    
    enum ScrollState {
        case idle       // 静止状态
        case scrolling  // 正常滚动
        case rapidScrolling  // 快速滚动
        case decelerating    // 减速中
    }
    
    @Published var state: ScrollState = .idle
    private var lastOffset: CGFloat = 0
    private var lastUpdateTime: Date = Date()
    private var velocityTracker: [(CGFloat, Date)] = []
    private var lastState: ScrollState = .idle
    private var lastPublishedState: ScrollState = .idle
    
    // 速度阈值，超过此值认为是快速滚动
    private let rapidScrollThreshold: CGFloat = 30
    // 最小更新间隔（秒），防止过于频繁的状态更新 - 已由FeedView控制
    private let minUpdateInterval: TimeInterval = 0.016 // ~60fps
    // 状态稳定时间，防止状态抖动 - 减少延迟以提升响应性
    private let stateStabilityDuration: TimeInterval = 0.05

    // 性能优化：减少不必要的状态发布
    private var pendingStateUpdate: DispatchWorkItem?
    
    // 私有初始化方法，确保只能通过shared访问
    private init() {}
    
    // 更新滚动状态
    func updateScrollState(currentOffset: CGFloat) {
        let now = Date()
        let timeDelta = now.timeIntervalSince(lastUpdateTime)
        
        // 避免过于频繁的更新
        guard timeDelta >= minUpdateInterval else {
            return
        }
        
        // 避免除零错误
        guard timeDelta > 0.001 else {
            return
        }
        
        // 计算滚动速度
        let offsetDelta = currentOffset - lastOffset
        let velocity = abs(offsetDelta) / CGFloat(timeDelta)
        
        // 更新速度追踪器
        velocityTracker.append((velocity, now))
        if velocityTracker.count > 5 {
            velocityTracker.removeFirst()
        }
        
        // 计算平均速度
        let avgVelocity = calculateAverageVelocity()
        
        // 计算新状态
        var newState: ScrollState
        if abs(offsetDelta) < 0.1 {
            newState = .idle
        } else if avgVelocity > rapidScrollThreshold {
            newState = .rapidScrolling
        } else {
            newState = .scrolling
        }

        // 检测减速
        if offsetDelta == 0 && (lastState == .scrolling || lastState == .rapidScrolling) {
            newState = .decelerating
        }

        // 只有在状态真正变化且与上次发布的状态不同时才更新
        if newState != lastPublishedState {
            // 取消之前的待处理更新
            pendingStateUpdate?.cancel()

            // 创建新的延迟更新任务
            let workItem = DispatchWorkItem { [weak self] in
                guard let self = self else { return }

                // 再次检查状态是否稳定
                if self.shouldPublishState(newState) {
                    self.publishState(newState, velocity: avgVelocity)
                }
            }

            pendingStateUpdate = workItem
            DispatchQueue.main.asyncAfter(deadline: .now() + stateStabilityDuration, execute: workItem)
        }

        // 更新记录
        lastState = newState
        lastOffset = currentOffset
        lastUpdateTime = now
    }
    
    private func calculateAverageVelocity() -> CGFloat {
        guard !velocityTracker.isEmpty else { return 0 }
        
        // 计算加权平均速度，最近的速度权重更高
        var totalWeight: CGFloat = 0
        var weightedSum: CGFloat = 0
        
        for (index, (velocity, _)) in velocityTracker.enumerated() {
            let weight = CGFloat(index + 1)
            weightedSum += velocity * weight
            totalWeight += weight
        }
        
        return totalWeight > 0 ? weightedSum / totalWeight : 0
    }
    
    // 检查是否应该发布状态
    private func shouldPublishState(_ newState: ScrollState) -> Bool {
        return newState != lastPublishedState
    }
    
    // 发布状态更新
    private func publishState(_ newState: ScrollState, velocity: CGFloat) {
        // 只有当状态真正变化且与上次发布的状态不同时才打印调试信息
        if newState != lastPublishedState {
            print("【调试】ScrollStateManager: 滚动状态变化 \(lastPublishedState) -> \(newState), 速度: \(String(format: "%.1f", velocity))")
        }
        state = newState
        lastPublishedState = newState
    }
    
    // 重置状态
    func resetScrollState() {
        state = .idle
        lastState = .idle
        lastPublishedState = .idle
        lastOffset = 0
        lastUpdateTime = Date()
        velocityTracker.removeAll()
    }
}
