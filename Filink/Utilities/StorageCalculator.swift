import Foundation
import WebKit

/// 存储数据结构
struct StorageData: Identifiable {
    let id = UUID()
    let name: String
    let size: Int64
    let type: StorageType
    
    enum StorageType {
        case cookies
        case coreData
        case imageCache
        case webViewCache
        case userDefaults
        case tempFiles
        case urlCache
        case other
        case fonts
    }
    
    /// 格式化存储大小，根据大小自动选择合适的单位
    var formattedSize: String {
        let bytes = Double(size)
        let gb = bytes / 1_073_741_824 // 1024^3
        let mb = bytes / 1_048_576 // 1024^2
        let kb = bytes / 1_024
        
        if gb >= 1.0 {
            return String(format: "%.2f GB", gb)
        } else if mb >= 1.0 {
            return String(format: "%.2f MB", mb)
        } else {
            return String(format: "%.2f KB", kb)
        }
    }
}

/// 存储空间计算器
class StorageCalculator {
    static let shared = StorageCalculator()
    private init() {}
    
    /// 获取所有存储数据
    /// - Returns: 存储数据数组
    func getAllStorageData() async -> [StorageData] {
        var results: [StorageData] = []
        
        // 并发获取各种存储数据
        async let cookiesSize = calculateCookiesSize()
        async let coreDataSize = calculateCoreDataSize()
        async let imageCacheSize = calculateImageCacheSize()
        async let webViewCacheSize = calculateWebViewCacheSize()
        async let userDefaultsSize = calculateUserDefaultsSize()
        async let tempFilesSize = calculateTempFilesSize()
        async let urlCacheSize = calculateURLCacheSize()
        async let fontsSize = calculateFontsSize()
        
        // 等待所有计算完成
        let (cookies, coreData, imageCache, webViewCache, userDefaults, tempFiles, urlCache, fonts) = await (
            cookiesSize, coreDataSize, imageCacheSize, webViewCacheSize, userDefaultsSize, tempFilesSize, urlCacheSize, fontsSize
        )
        
        // 添加结果
        results.append(StorageData(name: "Cookies", size: cookies, type: .cookies))
        results.append(StorageData(name: "Core Data", size: coreData, type: .coreData))
        results.append(StorageData(name: "图片缓存", size: imageCache, type: .imageCache))
        results.append(StorageData(name: "WebView 缓存", size: webViewCache, type: .webViewCache))
        results.append(StorageData(name: "UserDefaults", size: userDefaults, type: .userDefaults))
        results.append(StorageData(name: "临时文件", size: tempFiles, type: .tempFiles))
        results.append(StorageData(name: "URL 缓存", size: urlCache, type: .urlCache))
        results.append(StorageData(name: "字体", size: fonts, type: .fonts))
        
        return results
    }
    
    // MARK: - Private Methods
    
    /// 计算 Cookies 大小
    private func calculateCookiesSize() async -> Int64 {
        let cookiesURL = FileManager.default.urls(for: .libraryDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("Cookies")
        
        return await calculateDirectorySize(at: cookiesURL)
    }
    
    /// 计算 Core Data 大小
    private func calculateCoreDataSize() async -> Int64 {
        let coreDataURL = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask)[0]
        
        return await calculateDirectorySize(at: coreDataURL)
    }
    
    /// 计算图片缓存大小
    private func calculateImageCacheSize() async -> Int64 {
        // 使用 ImageManager 计算图片缓存大小
        let mbSize = await ImageManager.shared.calculateCacheSize()
        return Int64(mbSize * 1024 * 1024) // 转换为字节
    }
    
    /// 计算 WebView 缓存大小
    private func calculateWebViewCacheSize() async -> Int64 {
        let webKitURL = FileManager.default.urls(for: .libraryDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("WebKit")
        
        return await calculateDirectorySize(at: webKitURL)
    }
    
    /// 计算 UserDefaults 大小
    private func calculateUserDefaultsSize() async -> Int64 {
        let libraryURL = FileManager.default.urls(for: .libraryDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("Preferences")
        
        return await calculateDirectorySize(at: libraryURL)
    }
    
    /// 计算临时文件大小
    private func calculateTempFilesSize() async -> Int64 {
        let tempURL = FileManager.default.temporaryDirectory
        
        return await calculateDirectorySize(at: tempURL)
    }
    
    /// 计算 URL 缓存大小
    private func calculateURLCacheSize() async -> Int64 {
        let urlCacheSize = URLCache.shared.currentDiskUsage
        
        return Int64(urlCacheSize)
    }

    /// 计算字体文件大小
    private func calculateFontsSize() async -> Int64 {
        let fontsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            .appendingPathComponent("Fonts")
        
        return await calculateDirectorySize(at: fontsURL)
    }
    
    /// 计算目录大小
    /// - Parameter url: 目录 URL
    /// - Returns: 目录大小（字节）
    private func calculateDirectorySize(at url: URL) async -> Int64 {
        let fileManager = FileManager.default
        guard let enumerator = fileManager.enumerator(at: url, includingPropertiesForKeys: [.totalFileAllocatedSizeKey]) else {
            print("⚠️ 无法访问目录：\(url.path)")
            return 0
        }
        
        var totalSize: Int64 = 0
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.totalFileAllocatedSizeKey])
                if let size = resourceValues.totalFileAllocatedSize {
                    totalSize += Int64(size)
                }
            } catch {
                print("⚠️ 计算文件大小出错：\(fileURL.path), 错误：\(error)")
            }
        }
        
        print("📊 目录大小：\(url.lastPathComponent) = \(totalSize.formattedSize)")
        return totalSize
    }
}

// MARK: - 格式化工具
extension Int64 {
    /// 格式化存储大小，根据大小自动选择合适的单位
    var formattedSize: String {
        let bytes = Double(self)
        let gb = bytes / 1_073_741_824 // 1024^3
        let mb = bytes / 1_048_576 // 1024^2
        let kb = bytes / 1_024
        
        if gb >= 1.0 {
            return String(format: "%.2f GB", gb)
        } else if mb >= 1.0 {
            return String(format: "%.2f MB", mb)
        } else {
            return String(format: "%.2f KB", kb)
        }
    }
}
