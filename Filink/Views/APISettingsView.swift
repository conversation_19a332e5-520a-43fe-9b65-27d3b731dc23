// APISettingsView.swift
import SwiftUI

struct APISettingsView: View {
    @EnvironmentObject var settingsManager: SettingsManager

    var body: some View {
        Form {
            Section {
                VStack(alignment: .leading, spacing: 4) {
                    Text("服务器地址 (含 http/https 和端口)").font(.caption).foregroundColor(.secondary)
                    TextField("例如: http://192.168.1.100:5000", text: $settingsManager.website)
                        .keyboardType(.URL).textContentType(.URL).autocapitalization(.none).disableAutocorrection(true)
                }.padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("登录路径").font(.caption).foregroundColor(.secondary)
                    TextField("例如: /login", text: $settingsManager.loginPath)
                        .textContentType(.URL).autocapitalization(.none).disableAutocorrection(true)
                }.padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("测试 API 路径").font(.caption).foregroundColor(.secondary)
                    TextField("例如: /api/status", text: $settingsManager.testApiPath)
                        .textContentType(.URL).autocapitalization(.none).disableAutocorrection(true)
                }.padding(.vertical, 4)

            } header: { Text("服务器和路径") }
              footer: { Text("请确保服务器地址完整，路径以 / 开头。") }
              
            Section {
                VStack(alignment: .leading, spacing: 4) {
                    Text("CloudFlare 站点密钥").font(.caption).foregroundColor(.secondary)
                    TextField("CloudFlare 站点验证密钥", text: $settingsManager.siteCFKey)
                        .autocapitalization(.none).disableAutocorrection(true)
                }.padding(.vertical, 4)
            } header: { Text("CloudFlare 设置") }
              footer: { Text("用于验证CloudFlare请求的站点密钥，通常不需要修改。") }
        }
        .navigationTitle("API 配置")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    if #available(iOS 16.0, *) {
        NavigationStack {
            APISettingsView().environmentObject(SettingsManager())
        }
    } else {
        // Fallback on earlier versions
    }
}
