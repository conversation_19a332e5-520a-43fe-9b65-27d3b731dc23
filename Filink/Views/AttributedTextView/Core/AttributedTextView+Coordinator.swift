import UIKit
import SwiftUI

extension AttributedTextView {
    // Coordinator 类，负责处理 UITextView 的代理方法和状态管理
    public class Coordinator: NSObject, UITextViewDelegate {
        // Strong reference to parent - managed by SwiftUI lifecycle
        public let parent: AttributedTextView

        // Weak reference to prevent retain cycles
        public weak var textView: UITextView?

        // Weak reference to state manager to prevent retain cycles
        weak var viewState: AttributedTextViewState?
        
        // Image tap handling
        var onImageTap: (([URL], Int) -> Void)?
        var imageURLs: [URL] = []

        // 定义静态通知名称，避免魔法字符串，提高类型安全
        public static let batchUpdateNotificationName = NSNotification.Name("AttributedTextView.Coordinator.BatchUpdateImages")

        public init(_ parent: AttributedTextView, viewState: AttributedTextViewState, onImageTap: (([URL], Int) -> Void)?) {
            self.parent = parent
            self.viewState = viewState
            self.onImageTap = onImageTap
            super.init() // NSObject 的子类需要调用 super.init()
        }

        // 当 Coordinator 被销毁时，移除通知观察者，清理资源
        deinit {
            // Clean up notification observers to prevent memory leaks
            NotificationCenter.default.removeObserver(self)

            // Clear weak references
            textView?.delegate = nil
            textView = nil
            viewState = nil

            print("【调试】🧹 Coordinator: 内存清理完成")
        }

        // 处理链接点击
        public func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
            guard interaction == .invokeDefaultAction else {
                return true
            }

            if URL.scheme == "spoiler" {
                // URL格式: spoiler://identifier/state
                let identifier = URL.host ?? ""

                var updatedItems = parent.contentItems
                if let index = updatedItems.firstIndex(where: { item in
                    // 使用identifier来匹配对应的spoiler元素
                    return item.id == identifier
                }) {
                    if case .spoiler(let text, let isRevealed) = updatedItems[index] {
                        updatedItems[index] = .spoiler(text: text, isRevealed: !isRevealed)
                        parent.onUpdate?(updatedItems)
                    }
                }

                return false
            }

            UIApplication.shared.open(URL, options: [:], completionHandler: nil)
            return false
        }
        
        // 处理图片点击
        public func textView(_ textView: UITextView, shouldInteractWith textAttachment: NSTextAttachment, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
            // 仅当是用户主动点击图片时才处理
            guard interaction == .invokeDefaultAction else {
                return true
            }

            // 从附件的属性中获取图片URL
            guard let attributedText = textView.attributedText,
                  characterRange.location < attributedText.length,
                  let tappedURL = attributedText.attribute(.imageURL, at: characterRange.location, effectiveRange: nil) as? URL else {
                return true
            }

            // 查找被点击图片在URL数组中的索引
            if let index = imageURLs.firstIndex(of: tappedURL) {
                print("【调试】AttributedTextView: 图片被点击，URL: \(tappedURL), 索引: \(index), 总图片数: \(imageURLs.count)")
                // 触发回调
                onImageTap?(imageURLs, index)
                return false // 阻止默认行为（例如，文本选择）
            } else {
                print("【调试】AttributedTextView: 点击的图片URL未在imageURLs中找到: \(tappedURL)")
            }

            return true
        }
        
        // 批量更新图片
        @objc public func batchUpdateImagesInTextView(notification: Notification) {
            // 严格检查通知的 object 是否为当前 Coordinator 的 viewState 实例
            guard notification.object as? AttributedTextViewState === self.viewState else { return }

            DispatchQueue.main.async { [weak self] in // 确保UI更新在主线程
                guard let self = self,
                      let textView = self.textView,
                      let viewState = self.viewState else { return }

                if viewState.pendingImageUpdates.isEmpty {
                    return // 没有待更新的图片
                }

                // 获取当前文本视图的富文本，如果为空则创建一个空的
                let currentAttributedText = textView.attributedText ?? NSAttributedString()
                let newAttributedString = NSMutableAttributedString(attributedString: currentAttributedText)

                // 从后向前更新，避免因替换导致前面的NSRange失效
                let sortedUpdates = viewState.pendingImageUpdates.sorted { $0.0.location > $1.0.location }
                
                var didPerformUpdate = false
                for (range, update) in sortedUpdates {
                    // 再次检查 range 是否在当前文本长度内，防止因文本异步变化导致越界
                    if range.location < newAttributedString.length && (range.location + range.length) <= newAttributedString.length {
                        // 保存原始段落样式，以便在替换后重新应用
                        var originalParagraphStyle: NSParagraphStyle?
                        if range.location < newAttributedString.length {
                            originalParagraphStyle = newAttributedString.attribute(.paragraphStyle, at: range.location, effectiveRange: nil) as? NSParagraphStyle
                        }
                        
                        // 直接替换，因为 update 已经是 NSAttributedString 类型
                        newAttributedString.replaceCharacters(in: range, with: update)
                        didPerformUpdate = true
                        
                        // 确保替换后的内容保持原始段落样式
                        if let style = originalParagraphStyle, range.length == 1 { // 附件只占一个字符位置
                            // 确保新范围在字符串长度内
                            let newRange = NSRange(location: range.location, length: min(1, newAttributedString.length - range.location))
                            if newRange.location < newAttributedString.length && newRange.length > 0 {
                                newAttributedString.addAttribute(.paragraphStyle, value: style, range: newRange)
                            }
                        }
                    } else {
                        print("【调试】Warning: AttributedTextView - Image update range \(range) is out of bounds for text length \(newAttributedString.length). Skipping update.")
                    }
                }
                
                if didPerformUpdate {
                    // 在更新attributedText之前保存滚动位置和选择范围
                    let selectedRangeBeforeUpdate = textView.selectedRange
                    let contentOffsetBeforeUpdate = textView.contentOffset

                    // 使用新的样式管理器
                    let fontManager = RendererFontManager(fontScope: self.parent.fontScope)
                    let styleManager = StyleManager(fontManager: fontManager)
                    let style = self.parent.style
                    let paragraphStyle = styleManager.createDefaultParagraphStyle(
                        lineSpacing: style.lineSpacing,
                        font: style.font
                    )

                    // 🧹 移除已删除的 applyEmojiStyling 调用
                    // emoji样式现在在 ImageRenderer 中直接设置

                    // 有选择性地应用段落样式，避免覆盖特殊对齐
                    self.applyDefaultStyleSelectively(to: newAttributedString, defaultParagraphStyle: paragraphStyle)

                    textView.attributedText = newAttributedString // 一次性更新文本视图
                    
                    // 关键修复：强制重新计算布局
                    textView.setNeedsLayout()
                    textView.layoutIfNeeded()
                    
                    // 更新固有内容尺寸，通知SwiftUI容器高度变化
                    let hostingView = textView.superview
                    hostingView?.setNeedsLayout()
                    hostingView?.layoutIfNeeded()
                    
                    // 尝试恢复滚动位置和选择范围，减少视觉跳动
                    // 使用 DispatchQueue.main.async 确保在 TextView 完成内部布局调整后再执行
                    DispatchQueue.main.async {
                        // 再次检查 textView 是否仍然有效（例如，视图可能已被移除）
                        guard textView.window != nil else { return }

                        // 仅当选择范围在新的文本长度内时才恢复
                        if selectedRangeBeforeUpdate.location + selectedRangeBeforeUpdate.length <= textView.attributedText.length {
                            textView.selectedRange = selectedRangeBeforeUpdate
                        }
                        // 仅当 contentOffset 在合理的范围内时才恢复
                        let maxOffsetY = max(0, textView.contentSize.height - textView.bounds.height)
                        if contentOffsetBeforeUpdate.y <= maxOffsetY && contentOffsetBeforeUpdate.y >= 0 {
                             textView.setContentOffset(contentOffsetBeforeUpdate, animated: false)
                        } else if contentOffsetBeforeUpdate.y > maxOffsetY && maxOffsetY >= 0 { // 如果之前滚动到底部，则保持在新的底部
                            textView.setContentOffset(CGPoint(x: contentOffsetBeforeUpdate.x, y: maxOffsetY), animated: false)
                        }
                        
                        // 再次强制更新布局，确保在内容显示后高度计算正确
                        textView.setNeedsLayout()
                        textView.layoutIfNeeded()
                        hostingView?.setNeedsLayout()
                        hostingView?.layoutIfNeeded()
                    }
                }

                viewState.clearPendingUpdates() // 清空待更新列表
            }
        }

        /// 有选择性地应用默认段落样式，避免覆盖已设置的特殊对齐
        private func applyDefaultStyleSelectively(to attributedString: NSMutableAttributedString, defaultParagraphStyle: NSMutableParagraphStyle) {
            let fullRange = NSRange(location: 0, length: attributedString.length)

            // 检查是否已经有段落样式设置
            var hasExistingParagraphStyle = false
            attributedString.enumerateAttribute(.paragraphStyle, in: fullRange, options: []) { value, range, _ in
                if let existingStyle = value as? NSParagraphStyle {
                    // 如果已经有居中对齐，保留它
                    if existingStyle.alignment == .center {
                        hasExistingParagraphStyle = true
                    }
                }
            }

            // 如果没有特殊的段落样式，应用默认样式
            if !hasExistingParagraphStyle {
                attributedString.addAttribute(
                    .paragraphStyle,
                    value: defaultParagraphStyle,
                    range: fullRange
                )
                print("📝 Coordinator: 应用默认段落样式")
            } else {
                print("🎯 Coordinator: 保留现有的特殊对齐设置")
            }
        }
    }
}
