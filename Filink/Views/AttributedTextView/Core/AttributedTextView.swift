import SwiftUI
import UIKit

// MARK: - Error Handling
enum AttributedTextViewError: Error, LocalizedError {
    case imageLoadingFailed(URL, Error)
    case invalidContentItem(ContentItem)
    case renderingFailed(String)
    case stateUpdateFailed(String)

    var errorDescription: String? {
        switch self {
        case .imageLoadingFailed(let url, let error):
            return "图片加载失败: \(url.absoluteString) - \(error.localizedDescription)"
        case .invalidContentItem(let item):
            return "无效的内容项: \(item.id)"
        case .renderingFailed(let reason):
            return "渲染失败: \(reason)"
        case .stateUpdateFailed(let reason):
            return "状态更新失败: \(reason)"
        }
    }
}

// MARK: - Error Handler
class AttributedTextViewErrorHandler {
    static let shared = AttributedTextViewErrorHandler()

    private init() {}

    func handle(_ error: AttributedTextViewError, context: String = "") {
        let message = "【错误】AttributedTextView \(context): \(error.localizedDescription)"
        print(message)

        // In production, you might want to send this to a logging service
        // or show user-friendly error messages
    }

    func handleImageLoadingError(_ error: Error, url: URL, context: String = "") {
        let attributedError = AttributedTextViewError.imageLoadingFailed(url, error)
        handle(attributedError, context: context)
    }

    func handleRenderingError(_ reason: String, context: String = "") {
        let attributedError = AttributedTextViewError.renderingFailed(reason)
        handle(attributedError, context: context)
    }

    func handleStateUpdateError(_ reason: String, context: String = "") {
        let attributedError = AttributedTextViewError.stateUpdateFailed(reason)
        handle(attributedError, context: context)
    }
}

// MARK: - Style Configuration
public struct StyleConfiguration {
    let font: UIFont
    let boldFont: UIFont
    let h1Font: UIFont
    let h2Font: UIFont
    let h3Font: UIFont
    let textColor: UIColor
    let linkColor: UIColor
    let lineSpacing: CGFloat
    let bottomPadding: CGFloat

    public init(
        font: UIFont = .preferredFont(forTextStyle: .body),
        textColor: UIColor = .label,
        linkColor: UIColor = .systemBlue,
        lineSpacing: CGFloat = 0,
        bottomPadding: CGFloat = 0
    ) {
        self.font = font
        self.textColor = textColor
        self.linkColor = linkColor
        self.lineSpacing = lineSpacing
        self.bottomPadding = bottomPadding

        // Create derived fonts
        if let descriptor = font.fontDescriptor.withSymbolicTraits(.traitBold) {
            self.boldFont = UIFont(descriptor: descriptor, size: font.pointSize)
        } else {
            self.boldFont = font
        }

        let baseFontSize = font.pointSize
        self.h1Font = UIFont.systemFont(ofSize: baseFontSize * AttributedTextConstants.FontSizes.h1Multiplier, weight: .bold)
        self.h2Font = UIFont.systemFont(ofSize: baseFontSize * AttributedTextConstants.FontSizes.h2Multiplier, weight: .bold)
        self.h3Font = UIFont.systemFont(ofSize: baseFontSize * AttributedTextConstants.FontSizes.h3Multiplier, weight: .bold)
    }

    func isEqual(to other: StyleConfiguration) -> Bool {
        return font == other.font &&
               boldFont == other.boldFont &&
               h1Font == other.h1Font &&
               h2Font == other.h2Font &&
               h3Font == other.h3Font &&
               textColor == other.textColor &&
               linkColor == other.linkColor &&
               lineSpacing == other.lineSpacing &&
               bottomPadding == other.bottomPadding
    }
}

// MARK: - Unified State Manager
public class AttributedTextViewState: ObservableObject {
    // Content state - using private storage to avoid publishing during view updates
    private var _contentItemIds: [String] = []
    private var _style: StyleConfiguration?

    // Image loading state - these don't need to be @Published since they're internal
    var loadingImageURLs: Set<URL> = []
    var pendingImageUpdates: [(NSRange, NSAttributedString)] = []

    // UI state
    @Published var isUpdating: Bool = false

    // Performance tracking
    private var lastUpdateTime: Date = Date()
    private var updateCount: Int = 0

    // Timer for batch updates
    private var updateTimer: Timer?

    weak var coordinator: AttributedTextView.Coordinator?

    public init() {}

    // MARK: - State Management
    public func updateContentState(contentItemIds: [String], style: StyleConfiguration) {
        // Update private storage without triggering @Published
        self._contentItemIds = contentItemIds
        self._style = style
    }

    public func shouldUpdate(newContentItemIds: [String], newStyle: StyleConfiguration) -> Bool {
        guard let currentStyle = _style else { return true }
        return _contentItemIds != newContentItemIds || !currentStyle.isEqual(to: newStyle)
    }

    // MARK: - Image Loading State
    public func addLoadingImage(_ url: URL) {
        loadingImageURLs.insert(url)
    }

    public func removeLoadingImage(_ url: URL) {
        loadingImageURLs.remove(url)
    }

    public func addPendingUpdate(_ update: (NSRange, NSAttributedString)) {
        pendingImageUpdates.append(update)
        scheduleUpdate()
    }

    public func clearPendingUpdates() {
        pendingImageUpdates.removeAll()
    }

    // MARK: - Batch Update Scheduling
    private func scheduleUpdate() {
        updateCount += 1

        let timeSinceLastUpdate = Date().timeIntervalSince(lastUpdateTime)
        let baseInterval = AttributedTextConstants.Cache.updateInterval

        // Simplified batch processing logic
        let dynamicInterval: TimeInterval
        if updateCount >= 5 || timeSinceLastUpdate > 1.0 {
            dynamicInterval = 0.01
            print("🚀 AttributedTextViewState: 立即处理批量更新 (更新数量: \(updateCount))")
        } else {
            dynamicInterval = baseInterval
            print("🚀 AttributedTextViewState: 正常处理批量更新 (更新数量: \(updateCount))")
        }

        updateTimer?.invalidate()
        updateTimer = Timer.scheduledTimer(withTimeInterval: dynamicInterval, repeats: false) { [weak self] _ in
            self?.performBatchUpdate()
        }
    }

    private func performBatchUpdate() {
        guard !pendingImageUpdates.isEmpty else { return }

        updateCount = 0
        lastUpdateTime = Date()

        // Notify coordinator to perform the actual update
        NotificationCenter.default.post(
            name: AttributedTextView.Coordinator.batchUpdateNotificationName,
            object: self
        )
    }

    deinit {
        updateTimer?.invalidate()
    }
}

// MARK: - Dependency Injection Protocol
protocol ImageManagerProtocol {
    func loadImage(from url: URL) async throws -> UIImage
}

// Default implementation
extension ImageManager: ImageManagerProtocol {}

// MARK: - Image Manager Wrapper
class ImageManagerWrapper: ImageManagerProtocol {
    static let shared = ImageManagerWrapper()

    private init() {}

    func loadImage(from url: URL) async throws -> UIImage {
        // Handle MainActor isolation properly by switching to main actor context
        return try await withCheckedThrowingContinuation { continuation in
            Task { @MainActor in
                do {
                    let image = try await ImageManager.shared.loadImage(from: url)
                    continuation.resume(returning: image)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}

// MARK: - Render Context
struct RenderContext {
    let style: StyleConfiguration
    let maxWidth: CGFloat
    let textView: UITextView
    let state: AttributedTextViewState
    let imageManager: ImageManagerProtocol
    let fontScope: FontScope // 新增

    init(
        style: StyleConfiguration,
        maxWidth: CGFloat,
        textView: UITextView,
        state: AttributedTextViewState,
        imageManager: ImageManagerProtocol = ImageManagerWrapper.shared,
        fontScope: FontScope // 新增
    ) {
        self.style = style
        self.maxWidth = maxWidth
        self.textView = textView
        self.state = state
        self.imageManager = imageManager
        self.fontScope = fontScope // 新增
    }
}

// AttributedTextView - 支持文本和图片内联显示的视图
public struct AttributedTextView: UIViewRepresentable {
    @Binding var contentItems: [ContentItem]
    let style: StyleConfiguration
    let fontScope: FontScope
    var onUpdate: (([ContentItem]) -> Void)?
    var onImageTap: (([URL], Int) -> Void)?

    // Unified state manager - using StateObject for proper memory management
    @StateObject private var viewState = AttributedTextViewState()

    // MARK: - Initializers
    public init(
        contentItems: Binding<[ContentItem]>,
        style: StyleConfiguration,
        fontScope: FontScope,
        onUpdate: (([ContentItem]) -> Void)? = nil,
        onImageTap: (([URL], Int) -> Void)? = nil
    ) {
        self._contentItems = contentItems
        self.style = style
        self.fontScope = fontScope
        self.onUpdate = onUpdate
        self.onImageTap = onImageTap
    }

    // Convenience initializer for backward compatibility with Binding
    public init(
        contentItems: Binding<[ContentItem]>,
        textColor: UIColor = .label,
        linkColor: UIColor = .systemBlue,
        font: UIFont = .preferredFont(forTextStyle: .body),
        bottomPadding: CGFloat = 0,
        lineSpacing: CGFloat = 0,
        fontScope: FontScope,
        onUpdate: (([ContentItem]) -> Void)? = nil,
        onImageTap: (([URL], Int) -> Void)? = nil
    ) {
        self._contentItems = contentItems
        self.style = StyleConfiguration(
            font: font,
            textColor: textColor,
            linkColor: linkColor,
            lineSpacing: lineSpacing,
            bottomPadding: bottomPadding
        )
        self.fontScope = fontScope
        self.onUpdate = onUpdate
        self.onImageTap = onImageTap
    }

    // Read-only initializer for non-interactive content (常用于显示静态内容)
    public init(
        contentItems: [ContentItem],
        textColor: UIColor = .label,
        linkColor: UIColor = .systemBlue,
        font: UIFont = .preferredFont(forTextStyle: .body),
        bottomPadding: CGFloat = 0,
        lineSpacing: CGFloat = 0,
        fontScope: FontScope
    ) {
        self._contentItems = .constant(contentItems)
        self.style = StyleConfiguration(
            font: font,
            textColor: textColor,
            linkColor: linkColor,
            lineSpacing: lineSpacing,
            bottomPadding: bottomPadding
        )
        self.fontScope = fontScope
        self.onUpdate = nil
        self.onImageTap = nil
    }

    // MARK: - UIViewRepresentable
    public func makeUIView(context: Context) -> UITextView {
        let textView = CustomTextView()
        configureTextView(textView)
        setupCoordinator(textView, context: context)
        return textView
    }

    private func configureTextView(_ textView: UITextView) {
        textView.isEditable = false
        textView.isSelectable = true
        textView.isScrollEnabled = false
        textView.backgroundColor = .clear
        textView.textAlignment = .left  // 默认左对齐，让具体的段落样式控制对齐方式
        textView.layoutManager.allowsNonContiguousLayout = true
        textView.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)

        // Configure insets
        if style.bottomPadding > 0 {
            textView.textContainerInset = UIEdgeInsets(
                top: 0,
                left: 0,
                bottom: style.bottomPadding,
                right: 0
            )
        } else {
            textView.textContainerInset = .zero
        }

        textView.textContainer.lineFragmentPadding = 0
    }

    private func setupCoordinator(_ textView: UITextView, context: Context) {
        textView.delegate = context.coordinator
        context.coordinator.textView = textView

        // Connect state manager to coordinator with proper weak reference management
        viewState.coordinator = context.coordinator
        context.coordinator.viewState = viewState

        // Use weak self in notification to prevent retain cycles
        NotificationCenter.default.addObserver(
            context.coordinator,
            selector: #selector(Coordinator.batchUpdateImagesInTextView),
            name: Coordinator.batchUpdateNotificationName,
            object: viewState
        )
    }

    public func makeCoordinator() -> Coordinator {
        Coordinator(self, viewState: viewState, onImageTap: onImageTap)
    }

    // MARK: - Update UI View
    public func updateUIView(_ textView: UITextView, context: Context) {
        let currentContentItemIds = contentItems.map { $0.id }

        // Check if update is needed using unified state
        guard viewState.shouldUpdate(newContentItemIds: currentContentItemIds, newStyle: style) else {
            return
        }

        // Update state
        viewState.updateContentState(contentItemIds: currentContentItemIds, style: style)

        // Render content using the new engine
        renderContent(textView: textView, context: context)
    }

    private func renderContent(textView: UITextView, context: Context) {
        // Save current state
        let savedState = TextViewUtils.saveState(from: textView)

        // Extract image URLs and pass them to the coordinator (只包含非emoji的大图)
        let imageURLs = contentItems.compactMap { item -> URL? in
            if case .image(let url, _, _, _, _, let isEmoji) = item {
                // 只包含非emoji的图片
                return isEmoji ? nil : url
            }
            return nil
        }
        print("【调试】AttributedTextView: 过滤后的图片URL数量: \(imageURLs.count)")
        context.coordinator.imageURLs = imageURLs

        // Create render context with unified state
        let maxWidth = TextViewUtils.calculateEffectiveMaxWidth(for: textView)
        let renderContext = RenderContext(
            style: style,
            maxWidth: maxWidth,
            textView: textView,
            state: viewState,
            fontScope: fontScope
        )

        // Render using the engine with font manager
        let renderEngine = RenderEngine(fontScope: fontScope)
        let attributedString = renderEngine.render(contentItems, context: renderContext)

        // Update text view
        textView.attributedText = attributedString

        // Restore state asynchronously
        restoreTextViewState(textView: textView, savedState: savedState)
    }

    private func restoreTextViewState(textView: UITextView, savedState: TextViewUtils.TextViewState) {
        let shouldUpdateLayout = textView.window != nil
        if shouldUpdateLayout {
            DispatchQueue.main.async { [weak textView] in
                guard let textView = textView, textView.window != nil else { return }
                TextViewUtils.restoreState(savedState, to: textView)
            }
        }
    }
}
