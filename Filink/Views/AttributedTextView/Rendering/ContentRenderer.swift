import UIKit

// MARK: - Content Renderer Protocol
protocol ContentRenderer {
    func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext)
}

// MARK: - Base Style Renderer (减少重复代码)
class BaseStyleRenderer: ContentRenderer {
    let styleManager: StyleManager  // Swift 没有 protected，使用 internal

    init(styleManager: StyleManager) {
        self.styleManager = styleManager
    }

    // 子类必须实现
    func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        fatalError("Subclasses must implement render method")
    }
}

// MARK: - Text Renderer
class TextRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        // 1. 解构出 isBold, isItalic 和 isUnderline
        guard case .text(let text, let isBold, let isItalic, let isUnderline) = item else { return }

        // 2. 根据 isBold 和 isItalic 动态组合字体特征
        var traits: UIFontDescriptor.SymbolicTraits = []
        if isBold {
            traits.insert(.traitBold)
        }
        if isItalic {
            traits.insert(.traitItalic)
        }

        // 3. 使用 StyleManager 获取基础字体
        let baseFont = styleManager.font(for: .body)
        let font = baseFont.withTraits(traits)

        // 🎯 修复：每个文本项都设置完整的样式，包括段落样式
        let paragraphStyle = styleManager.createDefaultParagraphStyle(
            lineSpacing: context.style.lineSpacing,
            font: font
        )

        var attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: context.style.textColor,
            .paragraphStyle: paragraphStyle
        ]
        
        // 4. 如果需要，添加下划线样式
        if isUnderline {
            attributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        let textSegment = NSAttributedString(string: text, attributes: attributes)
        attributedString.append(textSegment)
        print("【调试】✅ TextRenderer: 文本样式已设置，包含段落样式（行高: \(paragraphStyle.lineSpacing)），文本: '\(text.prefix(20))...'")
    }
}

// MARK: - Link Renderer
class LinkRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .link(var text, let url) = item else { return }

        let font = styleManager.font(for: .body) // 使用 StyleManager 获取字体
        let linkColor = context.style.linkColor
        
        // 检查文本是否由编辑器生成（通过零宽空格标记）
        // 编辑器生成的文本已经包含了图标和正确的间距
        let isEditorGenerated = text.hasPrefix("\u{200B}")
        if isEditorGenerated {
            // 如果是，移除标记，因为 `ContentItem` 不应包含它
            text.removeFirst()
        }
        
        let fullLinkString = NSMutableAttributedString()

        // 只有当链接不是由我们的编辑器生成时，才手动添加图标
        if !isEditorGenerated {
            let attachment = NSTextAttachment()
            if let iconImage = UIImage(systemName: "link")?.withTintColor(linkColor) {
                attachment.image = iconImage
                let iconSize = font.pointSize * 0.9
                attachment.bounds = CGRect(x: 0, y: font.descender, width: iconSize, height: iconSize)
            }
            fullLinkString.append(NSAttributedString(attachment: attachment))
            fullLinkString.append(NSAttributedString(string: " "))
        }
        
        // 添加链接文本
        fullLinkString.append(NSAttributedString(string: text))
        
        // 为整个范围应用链接和样式属性
        let fullRange = NSRange(location: 0, length: fullLinkString.length)
        fullLinkString.addAttributes([
            .font: font,
            .foregroundColor: linkColor,
            .underlineStyle: NSUnderlineStyle.single.rawValue,
            .link: url
        ], range: fullRange)

        attributedString.append(fullLinkString)
    }
}

// MARK: - Newline Renderer
class NewlineRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .newline = item else { return }

        let attributes: [NSAttributedString.Key: Any] = [
            .font: styleManager.font(for: .body)
        ]

        let newlineSegment = NSAttributedString(string: "\n", attributes: attributes)
        attributedString.append(newlineSegment)
    }
}

// MARK: - Heading Renderer
class HeadingRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        let (text, textStyle, paragraphSpacing, paragraphSpacingBefore): (String, TextStyle, CGFloat, CGFloat)

        switch item {
        case .heading1(let headingText):
            text = headingText
            textStyle = .heading1
            paragraphSpacing = 16
            paragraphSpacingBefore = 24
        case .heading2(let headingText):
            text = headingText
            textStyle = .heading2
            paragraphSpacing = 12
            paragraphSpacingBefore = 20
        case .heading3(let headingText):
            text = headingText
            textStyle = .heading3
            paragraphSpacing = 8
            paragraphSpacingBefore = 16
        default:
            return
        }

        // 使用 StyleManager 获取标题字体
        let font = styleManager.font(for: textStyle)

        let headingStyle = NSMutableParagraphStyle()
        headingStyle.lineSpacing = context.style.lineSpacing
        headingStyle.paragraphSpacing = paragraphSpacing
        headingStyle.paragraphSpacingBefore = paragraphSpacingBefore
        headingStyle.lineBreakMode = .byCharWrapping
        headingStyle.hyphenationFactor = 1.0
        headingStyle.lineBreakStrategy = .pushOut

        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: context.style.textColor,
            .paragraphStyle: headingStyle
        ]

        let headingSegment = NSAttributedString(string: text, attributes: attributes)
        attributedString.append(headingSegment)
        attributedString.append(NSAttributedString(string: "\n", attributes: attributes))
    }
}

// MARK: - List Item Renderer
class ListItemRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .listItem(let text, let level, let isOrdered, let number) = item else { return }

        let indent = CGFloat(level) * 20.0
        let font = styleManager.font(for: .listItem)

        // 使用StyleManager创建基础段落样式，确保行距与其他文本一致
        let paragraphStyle = styleManager.createDefaultParagraphStyle(
            lineSpacing: context.style.lineSpacing,
            font: font
        )
        // 在基础样式上添加列表特有的缩进设置
        paragraphStyle.firstLineHeadIndent = indent
        paragraphStyle.headIndent = indent + 15

        // 处理前缀 - 针对无序列表优化圆点大小
        if isOrdered {
            // 有序列表使用正常字体和正确的序号
            let prefixAttributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: context.style.textColor,
                .paragraphStyle: paragraphStyle
            ]
            let orderNumber = number ?? 1
            let prefixSegment = NSAttributedString(string: "\(orderNumber). ", attributes: prefixAttributes)
            attributedString.append(prefixSegment)
        } else {
            // 无序列表使用更大的字体来显示圆点
            let bulletFont = font.withSize(font.pointSize * 2.0) // 圆点字体大小是文本的2倍
            
            // 优化基线偏移计算，使用更精确的字体度量来计算偏移量
            // 目标：让大圆点的视觉中心与正常文本的视觉中心对齐
            let normalTextCenter = font.capHeight / 2  // 正常文本的视觉中心（相对于基线）
            let bulletCenter = bulletFont.capHeight / 2  // 圆点字体的视觉中心
            let baselineAdjustment = normalTextCenter - bulletCenter  // 计算需要的偏移量
            
            let bulletAttributes: [NSAttributedString.Key: Any] = [
                .font: bulletFont,
                .foregroundColor: context.style.textColor,
                .paragraphStyle: paragraphStyle,
                .baselineOffset: baselineAdjustment
            ]
            let bulletSegment = NSAttributedString(string: "• ", attributes: bulletAttributes)
            attributedString.append(bulletSegment)
        }

        // 解析并渲染带格式的文本内容
        let formattedContent = parseFormattedText(text, context: context, paragraphStyle: paragraphStyle)
        attributedString.append(formattedContent)

        // 添加换行 - 使用正常字体
        let newlineAttributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: context.style.textColor,
            .paragraphStyle: paragraphStyle
        ]
        let newlineSegment = NSAttributedString(string: "\n", attributes: newlineAttributes)
        attributedString.append(newlineSegment)
    }

    /// 解析包含格式标记的文本并返回NSAttributedString
    private func parseFormattedText(_ text: String, context: RenderContext, paragraphStyle: NSParagraphStyle) -> NSAttributedString {
        let result = NSMutableAttributedString()
        var currentIndex = text.startIndex
        let baseFont = styleManager.font(for: .listItem)

        while currentIndex < text.endIndex {
            // 查找下一个格式标记
            if let boldRange = findNextFormatMarker(in: text, from: currentIndex, startMarker: "**", endMarker: "**") {
                // 添加标记前的普通文本
                if currentIndex < boldRange.start {
                    let plainText = String(text[currentIndex..<boldRange.start])
                    let plainAttributes: [NSAttributedString.Key: Any] = [
                        .font: baseFont,
                        .foregroundColor: context.style.textColor,
                        .paragraphStyle: paragraphStyle
                    ]
                    result.append(NSAttributedString(string: plainText, attributes: plainAttributes))
                }

                // 添加加粗文本
                let boldText = String(text[boldRange.content])
                let boldFont = baseFont.withTraits(.traitBold)
                let boldAttributes: [NSAttributedString.Key: Any] = [
                    .font: boldFont,
                    .foregroundColor: context.style.textColor,
                    .paragraphStyle: paragraphStyle
                ]
                result.append(NSAttributedString(string: boldText, attributes: boldAttributes))

                currentIndex = boldRange.end
            } else if let italicRange = findNextFormatMarker(in: text, from: currentIndex, startMarker: "*", endMarker: "*") {
                // 添加标记前的普通文本
                if currentIndex < italicRange.start {
                    let plainText = String(text[currentIndex..<italicRange.start])
                    let plainAttributes: [NSAttributedString.Key: Any] = [
                        .font: baseFont,
                        .foregroundColor: context.style.textColor,
                        .paragraphStyle: paragraphStyle
                    ]
                    result.append(NSAttributedString(string: plainText, attributes: plainAttributes))
                }

                // 添加斜体文本
                let italicText = String(text[italicRange.content])
                let italicFont = baseFont.withTraits(.traitItalic)
                let italicAttributes: [NSAttributedString.Key: Any] = [
                    .font: italicFont,
                    .foregroundColor: context.style.textColor,
                    .paragraphStyle: paragraphStyle
                ]
                result.append(NSAttributedString(string: italicText, attributes: italicAttributes))

                currentIndex = italicRange.end
            } else {
                // 没有更多格式标记，添加剩余的普通文本
                let remainingText = String(text[currentIndex...])
                let plainAttributes: [NSAttributedString.Key: Any] = [
                    .font: baseFont,
                    .foregroundColor: context.style.textColor,
                    .paragraphStyle: paragraphStyle
                ]
                result.append(NSAttributedString(string: remainingText, attributes: plainAttributes))
                break
            }
        }

        return result
    }

    /// 查找下一个格式标记的范围
    private func findNextFormatMarker(in text: String, from startIndex: String.Index, startMarker: String, endMarker: String) -> (start: String.Index, content: Range<String.Index>, end: String.Index)? {
        guard let markerStart = text.range(of: startMarker, range: startIndex..<text.endIndex)?.lowerBound else {
            return nil
        }

        let contentStart = text.index(markerStart, offsetBy: startMarker.count)
        guard let markerEnd = text.range(of: endMarker, range: contentStart..<text.endIndex)?.lowerBound else {
            return nil
        }

        let actualEnd = text.index(markerEnd, offsetBy: endMarker.count)
        return (start: markerStart, content: contentStart..<markerEnd, end: actualEnd)
    }
}

// MARK: - Horizontal Rule Renderer
class HorizontalRuleRenderer: ContentRenderer {
    func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .horizontalRule = item else { return }

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        paragraphStyle.paragraphSpacing = 10
        paragraphStyle.paragraphSpacingBefore = 10

        let hrString = NSAttributedString(string: "\n", attributes: [.paragraphStyle: paragraphStyle])
        attributedString.append(hrString)

        let hrAttachment = NSTextAttachment()
        let hrView = UIView(frame: CGRect(x: 0, y: 0, width: context.maxWidth, height: 1))
        hrView.backgroundColor = .systemGray4
        hrAttachment.image = hrView.asImage()
        
        let hrAttributedString = NSAttributedString(attachment: hrAttachment)
        attributedString.append(hrAttributedString)
        
        let afterHrString = NSAttributedString(string: "\n", attributes: [.paragraphStyle: paragraphStyle])
        attributedString.append(afterHrString)
    }
}

// MARK: - Spoiler Renderer
class SpoilerRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .spoiler(let text, let isRevealed) = item else { return }

        let font = styleManager.font(for: .body)
        let textColor = context.style.textColor

        // 使用ContentItem的ID作为标识符，确保点击时能正确匹配
        let spoilerIdentifier = item.id

        if isRevealed {
            // 显示状态：正常文本颜色，添加浅灰色背景表示这是剧透内容
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor,
                .backgroundColor: UIColor.systemGray6,
                .link: URL(string: "spoiler://\(spoilerIdentifier)/revealed")!
            ]

            let spoilerText = NSAttributedString(string: text, attributes: attributes)
            attributedString.append(spoilerText)
        } else {
            // 隐藏状态：使用遮盖字符替代原文字，创建真正的模糊效果
            let maskedText = createMaskedText(from: text)
            let backgroundColor = UIColor.systemGray3

            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: backgroundColor, // 使用与背景相同的颜色
                .backgroundColor: backgroundColor,
                .link: URL(string: "spoiler://\(spoilerIdentifier)/hidden")!
            ]

            let spoilerText = NSAttributedString(string: maskedText, attributes: attributes)
            attributedString.append(spoilerText)
        }
    }

    /// 创建遮盖文本，用特殊字符替换原文字
    private func createMaskedText(from originalText: String) -> String {
        var result = ""

        for char in originalText {
            if char.isWhitespace || char.isNewline {
                result += String(char) // 保留空格和换行
            } else {
                result += "█" // 使用实心方块字符遮盖
            }
        }

        return result
    }
}

// MARK: - Footnote Renderer
class FootnoteReferenceRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .footnoteReference(let index, let refText) = item else { return }
        
        let baseFont = styleManager.font(for: .body)
        
        // 1. 渲染带样式的脚注引用 `[index]`
        let refString = "[\(index)]"
        let refFont = baseFont.withSize(baseFont.pointSize * 0.8)
        let refAttributes: [NSAttributedString.Key: Any] = [
            .font: refFont,
            .foregroundColor: context.style.linkColor,
            .link: URL(string: "#footnote-\(index)")!,
            .baselineOffset: 5 // 上标效果
        ]
        let footnoteRef = NSAttributedString(string: refString, attributes: refAttributes)
        
        // 2. 渲染其后的普通文本 `refText`
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: baseFont,
            .foregroundColor: context.style.textColor
        ]
        let followingText = NSAttributedString(string: refText, attributes: textAttributes)
        
        // 3. 将两部分拼接在一起
        let combinedString = NSMutableAttributedString()
        combinedString.append(footnoteRef)
        combinedString.append(followingText)
        
        attributedString.append(combinedString)
    }
}

class FootnoteContentRenderer: BaseStyleRenderer {
    // styleManager 已经从父类继承

    override func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .footnoteContent(let index, let content) = item else { return }
        
        let text = "\(index). \(content)\n"
        // 脚注内容使用较小的字体
        let baseFont = styleManager.font(for: .caption)
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.firstLineHeadIndent = 0
        paragraphStyle.headIndent = 20
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: baseFont,
            .foregroundColor: context.style.textColor,
            .paragraphStyle: paragraphStyle,
            .link: URL(string: "#footnote-ref-\(index)")!
        ]
        
        let footnoteContent = NSAttributedString(string: text, attributes: attributes)
        attributedString.append(footnoteContent)
    }
}




// MARK: - Renderer Factory
class RendererFactory {
    static func createRenderers(layoutCalculator: LayoutCalculator, styleManager: StyleManager) -> [ContentRenderer] {
        return [
            TextRenderer(styleManager: styleManager),
            LinkRenderer(styleManager: styleManager),
            NewlineRenderer(styleManager: styleManager),
            HeadingRenderer(styleManager: styleManager),
            ImageRenderer(layoutCalculator: layoutCalculator),
            ListItemRenderer(styleManager: styleManager),
            FootnoteReferenceRenderer(styleManager: styleManager),
            FootnoteContentRenderer(styleManager: styleManager),
            SpoilerRenderer(styleManager: styleManager),
            HorizontalRuleRenderer()
        ]
    }

    static func findRenderer(for item: ContentItem, in renderers: [ContentRenderer]) -> ContentRenderer? {
        return renderers.first { renderer in
            switch (item, renderer) {
            case (.text, is TextRenderer): return true
            case (.link, is LinkRenderer): return true
            case (.newline, is NewlineRenderer): return true
            case (.heading1, is HeadingRenderer): return true
            case (.heading2, is HeadingRenderer): return true
            case (.heading3, is HeadingRenderer): return true
            case (.image, is ImageRenderer): return true
            case (.listItem, is ListItemRenderer): return true
            case (.footnoteReference, is FootnoteReferenceRenderer): return true
            case (.footnoteContent, is FootnoteContentRenderer): return true
            case (.spoiler, is SpoilerRenderer): return true
            case (.horizontalRule, is HorizontalRuleRenderer): return true
            default: return false
            }
        }
    }
}

extension UIView {
    func asImage() -> UIImage {
        let renderer = UIGraphicsImageRenderer(bounds: bounds)
        return renderer.image { rendererContext in
            layer.render(in: rendererContext.cgContext)
        }
    }
}


