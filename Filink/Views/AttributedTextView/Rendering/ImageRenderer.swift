import UIKit

// MARK: - Image Renderer
class ImageRenderer: ContentRenderer {
    private let layoutCalculator: LayoutCalculator

    init(layoutCalculator: LayoutCalculator) {
        self.layoutCalculator = layoutCalculator
    }

    // MARK: - Common Image Processing
    private func createImageAttachmentString(
        image: UIImage,
        size: CGSize,
        isEmoji: Bool,
        imageURL: URL, // 添加 imageURL 参数
        linkURL: URL?,
        context: RenderContext
    ) -> NSMutableAttributedString {
        let mutableImageString = TextViewUtils.createImageAttachment(
            image: image,
            size: size,
            isEmoji: isEmoji,
            linkURL: linkURL
        )
        
        // 关键：将图片的原始URL附加到属性中，以便点击时识别
        mutableImageString.addAttribute(.imageURL, value: imageURL, range: NSRange(location: 0, length: 1))

        // Apply consistent styling
        let fontManager = RendererFontManager(fontScope: context.fontScope)
        let styleManager = StyleManager(fontManager: fontManager)

        if isEmoji {
            // Emoji: 设置默认段落样式 + 垂直对齐
            let defaultStyle = styleManager.createDefaultParagraphStyle(
                lineSpacing: context.style.lineSpacing,
                font: context.style.font
            )
            mutableImageString.addAttribute(.paragraphStyle, value: defaultStyle, range: NSRange(location: 0, length: 1))

            // Apply emoji alignment
            if let attachment = mutableImageString.attribute(.attachment, at: 0, effectiveRange: nil) as? NSTextAttachment,
               let attachmentImage = attachment.image {
                styleManager.applyEmojiAlignment(
                    to: attachment,
                    image: attachmentImage,
                    font: context.style.font,
                    attributedString: mutableImageString,
                    range: NSRange(location: 0, length: 1),
                    defaultParagraphStyle: defaultStyle
                )
            }
        } else {
            // Regular image: 设置居中样式
            let centerStyle = styleManager.createCenterParagraphStyle(
                lineSpacing: context.style.lineSpacing,
                font: context.style.font
            )
            mutableImageString.addAttribute(.paragraphStyle, value: centerStyle, range: NSRange(location: 0, length: 1))
        }

        return mutableImageString
    }

    private func calculateFinalImageSize(
        originalImage: UIImage?,
        specifiedWidth: CGFloat?,
        specifiedHeight: CGFloat?,
        effectiveMaxWidth: CGFloat,
        isEmoji: Bool
    ) -> CGSize {
        if let image = originalImage, let width = specifiedWidth, let height = specifiedHeight {
            // Smart image sizing: force HTML dimensions for small images, preserve aspect ratio for large ones
            let isSmallImage = width <= 50 && height <= 50

            if isSmallImage || isEmoji {
                print("🎯 ImageRenderer: 小图片/emoji，强制使用HTML尺寸 \(width)x\(height)")
                return CGSize(width: width, height: height)
            } else {
                let calculatedSize = layoutCalculator.calculateImageSize(
                    originalSize: image.size,
                    specifiedWidth: width,
                    specifiedHeight: height,
                    maxWidthConstraint: effectiveMaxWidth,
                    isEmoji: isEmoji
                )
                print("🖼️ ImageRenderer: 大图片，保持宽高比，最终尺寸: \(calculatedSize)")
                return calculatedSize
            }
        } else {
            return layoutCalculator.calculateImageSize(
                originalSize: originalImage?.size,
                specifiedWidth: specifiedWidth,
                specifiedHeight: specifiedHeight,
                maxWidthConstraint: effectiveMaxWidth,
                isEmoji: isEmoji
            )
        }
    }
    
    func render(_ item: ContentItem, to attributedString: NSMutableAttributedString, context: RenderContext) {
        guard case .image(let url, let altText, let width, let height, let linkURL, let isEmoji) = item else { return }

        let effectiveMaxWidth = TextViewUtils.calculateEffectiveMaxWidth(for: context.textView)

        // 同步渲染，使用简化的方法
        renderImageSync(
            url: url,
            altText: altText,
            width: width,
            height: height,
            linkURL: linkURL,
            isEmoji: isEmoji,
            effectiveMaxWidth: effectiveMaxWidth,
            to: attributedString,
            context: context
        )
    }
    
    private func renderImageSync(
        url: URL,
        altText: String,
        width: CGFloat?,
        height: CGFloat?,
        linkURL: URL?,
        isEmoji: Bool,
        effectiveMaxWidth: CGFloat,
        to attributedString: NSMutableAttributedString,
        context: RenderContext
    ) {
        // 简化的同步渲染，避免 MainActor 问题
        let placeholderDisplaySize = layoutCalculator.calculateImageSize(
            originalSize: nil,
            specifiedWidth: width,
            specifiedHeight: height,
            maxWidthConstraint: effectiveMaxWidth,
            isEmoji: isEmoji
        )
        
        // Create placeholder image
        let placeholderText = "加载中..."
        let placeholderImage = PlaceholderImageGenerator.createSimplePlaceholder(size: placeholderDisplaySize, text: placeholderText)

        print("【调试】🖼️ ImageRenderer: 开始渲染图片，URL: \(url), 尺寸: \(placeholderDisplaySize), isEmoji: \(isEmoji)")

        let attachmentRange = NSRange(location: attributedString.length, length: 1)

        // Use common image processing logic
        let mutablePlaceholderString = createImageAttachmentString(
            image: placeholderImage,
            size: placeholderDisplaySize,
            isEmoji: isEmoji,
            imageURL: url, // 传递 imageURL
            linkURL: linkURL,
            context: context
        )

        attributedString.append(mutablePlaceholderString)
        
        // 异步加载真实图片
        startAsyncImageLoad(
            url: url,
            altText: altText,
            width: width,
            height: height,
            linkURL: linkURL,
            isEmoji: isEmoji,
            effectiveMaxWidth: effectiveMaxWidth,
            attachmentRange: attachmentRange,
            context: context
        )
    }
    
    private func startAsyncImageLoad(
        url: URL,
        altText: String,
        width: CGFloat?,
        height: CGFloat?,
        linkURL: URL?,
        isEmoji: Bool,
        effectiveMaxWidth: CGFloat,
        attachmentRange: NSRange,
        context: RenderContext
    ) {
        // 对于emoji，允许多个实例同时渲染；对于普通图片，防止重复加载
        if !isEmoji {
            guard !context.state.loadingImageURLs.contains(url) else {
                print("【调试】⚠️ ImageRenderer: 普通图片已在加载中，跳过重复请求: \(url)")
                return
            }
            context.state.addLoadingImage(url)
        } else {
            print("【调试】🎭 ImageRenderer: Emoji允许多实例渲染: \(url)")
        }

        // Simplified async image loading with dependency injection
        Task { @MainActor in
            do {
                let image = try await context.imageManager.loadImage(from: url)
                self.processLoadedImage(
                    image: image,
                    url: url,
                    altText: altText,
                    width: width,
                    height: height,
                    linkURL: linkURL,
                    isEmoji: isEmoji,
                    effectiveMaxWidth: effectiveMaxWidth,
                    attachmentRange: attachmentRange,
                    context: context
                )
            } catch {
                // Use unified error handling
                AttributedTextViewErrorHandler.shared.handleImageLoadingError(
                    error,
                    url: url,
                    context: "ImageRenderer.startAsyncImageLoad"
                )

                self.processFailedImage(
                    url: url,
                    altText: altText,
                    width: width,
                    height: height,
                    linkURL: linkURL,
                    isEmoji: isEmoji,
                    effectiveMaxWidth: effectiveMaxWidth,
                    attachmentRange: attachmentRange,
                    context: context,
                    error: error
                )
            }
        }
    }
    
    // Simplified image processing methods
    private func processLoadedImage(
        image: UIImage,
        url: URL,
        altText: String,
        width: CGFloat?,
        height: CGFloat?,
        linkURL: URL?,
        isEmoji: Bool,
        effectiveMaxWidth: CGFloat,
        attachmentRange: NSRange,
        context: RenderContext
    ) {
        // 只有普通图片才需要从加载状态中移除
        if !isEmoji {
            context.state.removeLoadingImage(url)
        }

        // Use common size calculation logic
        let finalImageSize = calculateFinalImageSize(
            originalImage: image,
            specifiedWidth: width,
            specifiedHeight: height,
            effectiveMaxWidth: effectiveMaxWidth,
            isEmoji: isEmoji
        )

        // Use common image processing logic
        let mutableImageString = createImageAttachmentString(
            image: image,
            size: finalImageSize,
            isEmoji: isEmoji,
            imageURL: url, // 传递 imageURL
            linkURL: linkURL,
            context: context
        )

        print("【调试】✅ ImageRenderer: 图片加载完成，样式将由统一状态管理器保持，URL: \(url)")
        context.state.addPendingUpdate((attachmentRange, mutableImageString))
    }
    
    private func processFailedImage(
        url: URL,
        altText: String,
        width: CGFloat?,
        height: CGFloat?,
        linkURL: URL?,
        isEmoji: Bool,
        effectiveMaxWidth: CGFloat,
        attachmentRange: NSRange,
        context: RenderContext,
        error: Error
    ) {
        // 只有普通图片才需要从加载状态中移除
        if !isEmoji {
            context.state.removeLoadingImage(url)
        }

        // Use common size calculation for failed placeholder
        let failedPlaceholderSize = calculateFinalImageSize(
            originalImage: nil,
            specifiedWidth: width,
            specifiedHeight: height,
            effectiveMaxWidth: effectiveMaxWidth,
            isEmoji: isEmoji
        )

        // Create failed placeholder
        let failedText = "加载失败"
        let failedPlaceholderImage = PlaceholderImageGenerator.createSimplePlaceholder(size: failedPlaceholderSize, text: failedText)

        // Use common image processing logic
        let mutableFailedImageString = createImageAttachmentString(
            image: failedPlaceholderImage,
            size: failedPlaceholderSize,
            isEmoji: isEmoji,
            imageURL: url, // 传递 imageURL
            linkURL: linkURL,
            context: context
        )

        context.state.addPendingUpdate((attachmentRange, mutableFailedImageString))
    }
}
