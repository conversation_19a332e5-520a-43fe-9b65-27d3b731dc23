import UIKit

// MARK: - Layout Calculator
class LayoutCalculator {
    func calculateImageSize(
        originalSize: CGSize?,
        specifiedWidth: CGFloat?,
        specifiedHeight: CGFloat?,
        maxWidthConstraint: CGFloat,
        isEmoji: Bool = false
    ) -> CGSize {
        // 如果有原始尺寸，说明要保持宽高比
        if let original = originalSize {
            print("🔍 LayoutCalculator: 保持宽高比模式，原始尺寸: \(original.width)x\(original.height), 最大宽度约束: \(maxWidthConstraint)")

            // 如果原始宽度超过约束，等比缩放
            if original.width > maxWidthConstraint {
                let ratio = maxWidthConstraint / original.width
                let finalSize = CGSize(width: maxWidthConstraint, height: original.height * ratio)
                print("📏 LayoutCalculator: 保持宽高比缩放到 \(finalSize.width)x\(finalSize.height)")
                return finalSize
            } else {
                print("✅ LayoutCalculator: 保持原始尺寸 \(original.width)x\(original.height)")
                return original
            }
        }

        // 没有原始尺寸，使用HTML指定的尺寸
        guard let targetWidth = specifiedWidth, let targetHeight = specifiedHeight else {
            print("⚠️ LayoutCalculator: 没有原始尺寸也没有指定尺寸，使用默认 50x50")
            return CGSize(width: 50, height: 50)
        }

        print("🔍 LayoutCalculator: 强制尺寸模式，指定尺寸 \(targetWidth)x\(targetHeight), 最大宽度约束: \(maxWidthConstraint), isEmoji: \(isEmoji)")

        // 确保不超过最大宽度约束
        if targetWidth > maxWidthConstraint {
            let ratio = maxWidthConstraint / targetWidth
            let finalSize = CGSize(width: maxWidthConstraint, height: targetHeight * ratio)
            print("📏 LayoutCalculator: 超过最大宽度约束，缩放到 \(finalSize.width)x\(finalSize.height)")
            return finalSize
        }

        let finalSize = CGSize(width: targetWidth, height: targetHeight)
        print("✅ LayoutCalculator: 使用指定尺寸 \(finalSize.width)x\(finalSize.height)")
        return finalSize
    }
    
    // 🧹 已删除无用的 handleImageAlignment 方法
    // 现在图片对齐在 ImageRenderer 中直接设置
    
    // 🧹 已删除无用的图片检测方法
    
    // 🧹 已删除无用的 applyCenterAlignment 方法

    // 🧹 已删除无用的图片对齐相关方法和结构
    
    // 🧹 已删除无用的 adjustTextViewForCentering 方法
}
