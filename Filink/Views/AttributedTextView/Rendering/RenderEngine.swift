import UIKit

// MARK: - Render Engine
class RenderEngine {
    private let renderers: [ContentRenderer]
    private let layoutCalculator: LayoutCalculator
    private let styleManager: StyleManager

    init(fontScope: FontScope) {
        let fontManager = RendererFontManager(fontScope: fontScope)
        self.layoutCalculator = LayoutCalculator()
        self.styleManager = StyleManager(fontManager: fontManager)
        self.renderers = RendererFactory.createRenderers(layoutCalculator: layoutCalculator, styleManager: styleManager)
    }
    
    func render(_ contentItems: [ContentItem], context: RenderContext) -> NSAttributedString {
        let attributedString = NSMutableAttributedString()

        // 🎯 重构：移除无效的全局段落样式设置
        // 每个渲染器会在渲染时设置自己的段落样式
        
        // 🎯 重构：每个渲染器在渲染时就设置好完整样式，无需后处理
        for item in contentItems {
            if let renderer = RendererFactory.findRenderer(for: item, in: renderers) {
                renderer.render(item, to: attributedString, context: context)
            }
        }

        print("【调试】✅ RenderEngine: 所有内容项已渲染完成，每项都包含完整样式")
        return attributedString
    }
    // 🧹 清理完成：每个渲染器负责设置自己的完整样式
}
