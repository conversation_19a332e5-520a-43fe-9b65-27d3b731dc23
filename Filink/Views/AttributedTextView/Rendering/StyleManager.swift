import UIKit

// MARK: - TextStyle Enum
enum TextStyle {
    case heading1
    case heading2
    case heading3
    case body
    case code
    case caption
    case listItem
}

// MARK: - Font Manager for Renderer
class RendererFontManager {
    private let fontScope: FontScope

    init(fontScope: FontScope) {
        self.fontScope = fontScope
    }

    // 不存储缩放值，每次都获取最新的
    func font(for textStyle: TextStyle) -> UIFont {
        let currentScale = SettingsManager.shared.postContentFontScale

        let font: UIFont
        switch textStyle {
        case .heading1:
            font = UIFont.customFont(size: 24 * currentScale, weight: .bold, for: fontScope)
        case .heading2:
            font = UIFont.customFont(size: 22 * currentScale, weight: .bold, for: fontScope)
        case .heading3:
            font = UIFont.customFont(size: 20 * currentScale, weight: .bold, for: fontScope)
        case .body:
            font = UIFont.customFont(size: 16 * currentScale, weight: .regular, for: fontScope)
        case .listItem:
            font = UIFont.customFont(size: 16 * currentScale, weight: .regular, for: fontScope)
        case .caption:
            font = UIFont.customFont(size: 12 * currentScale, weight: .regular, for: fontScope)
        case .code:
            font = UIFont.monospacedSystemFont(ofSize: 15 * currentScale, weight: .regular)
        }

        return font
    }
}

// MARK: - Style Cache Key
private struct StyleCacheKey: Hashable {
    let lineSpacing: CGFloat
    let fontSize: CGFloat
    let alignment: NSTextAlignment
    let minimumLineHeight: CGFloat

    init(lineSpacing: CGFloat, font: UIFont, alignment: NSTextAlignment = .natural) {
        self.lineSpacing = lineSpacing
        self.fontSize = font.pointSize
        self.alignment = alignment
        self.minimumLineHeight = max(font.lineHeight * AttributedTextConstants.minimumLineHeightMultiplier, 24)
    }
}

// MARK: - Style Manager
class StyleManager {
    // MARK: - Properties
    // 🎯 性能优化：样式对象缓存
    private static var paragraphStyleCache: [StyleCacheKey: NSMutableParagraphStyle] = [:]
    private static let cacheQueue = DispatchQueue(label: "StyleManager.cache", attributes: .concurrent)

    private let fontManager: RendererFontManager

    // MARK: - Initialization
    init(fontManager: RendererFontManager) {
        self.fontManager = fontManager
    }

    // MARK: - Font Factory

    /// 统一的字体获取接口
    func font(for textStyle: TextStyle) -> UIFont {
        return fontManager.font(for: textStyle)
    }

    // MARK: - Paragraph Style Factory

    func createDefaultParagraphStyle(lineSpacing: CGFloat, font: UIFont) -> NSMutableParagraphStyle {
        let cacheKey = StyleCacheKey(lineSpacing: lineSpacing, font: font)

        return Self.cacheQueue.sync {
            if let cachedStyle = Self.paragraphStyleCache[cacheKey] {
                return cachedStyle.mutableCopy() as! NSMutableParagraphStyle
            }
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = lineSpacing
            paragraphStyle.minimumLineHeight = cacheKey.minimumLineHeight
            paragraphStyle.lineBreakMode = .byCharWrapping
            paragraphStyle.hyphenationFactor = 1.0
            paragraphStyle.lineBreakStrategy = .pushOut

            Self.paragraphStyleCache[cacheKey] = paragraphStyle
            return paragraphStyle.mutableCopy() as! NSMutableParagraphStyle
        }
    }

    func createCenterParagraphStyle(lineSpacing: CGFloat, font: UIFont) -> NSMutableParagraphStyle {
        let cacheKey = StyleCacheKey(lineSpacing: lineSpacing, font: font, alignment: .center)

        return Self.cacheQueue.sync {
            if let cachedStyle = Self.paragraphStyleCache[cacheKey] {
                return cachedStyle.mutableCopy() as! NSMutableParagraphStyle
            }
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            paragraphStyle.lineSpacing = lineSpacing
            paragraphStyle.minimumLineHeight = cacheKey.minimumLineHeight
            paragraphStyle.lineBreakMode = .byCharWrapping
            paragraphStyle.hyphenationFactor = 1.0
            paragraphStyle.lineBreakStrategy = .pushOut

            Self.paragraphStyleCache[cacheKey] = paragraphStyle
            return paragraphStyle.mutableCopy() as! NSMutableParagraphStyle
        }
    }

    // 🎯 性能优化：清理缓存方法（可选）
    static func clearStyleCache() {
        cacheQueue.async(flags: .barrier) {
            paragraphStyleCache.removeAll()
        }
    }
    
    // 🧹 已删除无用的 applyOptimizedStyling 方法
    // 现在每个渲染器在渲染时就设置好完整样式，无需后处理

    // 🧹 已删除无用的 applyEmojiStyling 方法
    
    func applyEmojiAlignment(to textAttachment: NSTextAttachment, image: UIImage, font: UIFont, attributedString: NSMutableAttributedString, range: NSRange, defaultParagraphStyle: NSMutableParagraphStyle) {
        // 🎯 修复：移除对周围文本的段落样式处理
        // 这些处理会覆盖已经设置好的图片居中样式
        // 段落样式应该在统一的地方处理，而不是在emoji处理时重复处理

        // MARK: - Emoji垂直对齐优化
        //
        // 问题背景：
        // Emoji图片在文本中默认与基线对齐，导致视觉上偏下，与文字不协调
        //
        // 解决方案：
        // 通过调整NSTextAttachment的bounds.origin.y来实现垂直居中对齐
        // Y偏移是相对于字体基线计算的，负值向上移动，正值向下移动

        let actualDisplaySize = textAttachment.bounds.size

        // 获取字体的关键度量信息
        let fontCapHeight = font.capHeight  // 大写字母高度（从基线到字母顶部）
        let imageHeight = actualDisplaySize.height

        // 核心算法：让图片中心与文字视觉中心对齐
        //
        // 1. 文字视觉中心 = capHeight / 2
        //    - capHeight代表大写字母的高度，是文字最显眼的部分
        //    - 其中点位置是人眼感知的文字视觉中心
        //    - 相对于基线的位置：capHeight / 2
        //
        // 2. 图片中心 = imageHeight / 2
        //    - 图片几何中心点
        //
        // 3. Y偏移 = 文字视觉中心 - 图片中心
        //    - 正值：图片向下移动（文字中心在图片中心上方）
        //    - 负值：图片向上移动（文字中心在图片中心下方）
        //    - 零值：完美对齐

        let textVisualCenter = fontCapHeight / 2  // 相对于基线的文字视觉中心
        let imageCenter = imageHeight / 2         // 图片几何中心
        let yOffset = textVisualCenter - imageCenter

        // 应用垂直对齐偏移，保持原有的显示尺寸
        textAttachment.bounds = CGRect(origin: CGPoint(x: 0, y: yOffset), size: actualDisplaySize)
    }
}
