import UIKit

// MARK: - Constants
struct AttributedTextConstants {
    static let defaultImageSize: CGFloat = 100
    static let emojiMaxSize: CGFloat = 30
    static let minimumLineHeightMultiplier: CGFloat = 1.2
    static let defaultPadding: CGFloat = 8.0
    static let maxYOffset: CGFloat = -2.0
    static let extraLayoutPadding: CGFloat = 8.0
    
    struct FontSizes {
        static let h1Multiplier: CGFloat = 1.8
        static let h2Multiplier: CGFloat = 1.5
        static let h3Multiplier: CGFloat = 1.2
    }
    
    struct Cache {
        static let updateInterval: TimeInterval = 0.1
        static let defaultAspectRatio: CGFloat = 0.6
    }
}

// MARK: - Custom Attribute Keys
let imageURLAttributeName = "com.filink.attributedTextView.imageURL"
let isEmojiAttributeName = "com.filink.attributedTextView.isEmoji"
let imagePathAttributeName = "com.filink.attributedTextView.imagePath"

extension NSAttributedString.Key {
    static let imageURL = NSAttributedString.Key(imageURLAttributeName)
    static let isEmoji = NSAttributedString.Key(isEmojiAttributeName)
    static let imagePath = NSAttributedString.Key(imagePathAttributeName)
}
