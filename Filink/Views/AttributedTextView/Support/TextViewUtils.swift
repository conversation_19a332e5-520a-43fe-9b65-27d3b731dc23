import UIKit

// MARK: - Text View Utilities
struct TextViewUtils {
    
    // MARK: - Width Calculation
    static func calculateEffectiveMaxWidth(for textView: UITextView) -> CGFloat {
        let textContainerEffectiveWidth = textView.textContainer.size.width - (textView.textContainer.lineFragmentPadding * 2)
        let viewBoundsEffectiveWidth = textView.bounds.width > 0 ? 
            (textView.bounds.width - textView.textContainerInset.left - textView.textContainerInset.right - (textView.textContainer.lineFragmentPadding * 2)) : 0
        
        var effectiveMaxWidth = UIScreen.main.bounds.width - 40
        if textContainerEffectiveWidth > 0 {
            effectiveMaxWidth = textContainerEffectiveWidth
        } else if viewBoundsEffectiveWidth > 0 {
            effectiveMaxWidth = viewBoundsEffectiveWidth
        }
        return max(effectiveMaxWidth, 100)
    }
    
    // MARK: - State Management
    struct TextViewState {
        let selectedRange: NSRange
        let contentOffset: CGPoint
        
        init(from textView: UITextView) {
            self.selectedRange = textView.selectedRange
            self.contentOffset = textView.contentOffset
        }
    }
    
    static func saveState(from textView: UITextView) -> TextViewState {
        return TextViewState(from: textView)
    }
    
    static func restoreState(_ state: TextViewState, to textView: UITextView) {
        // Restore selection
        if state.selectedRange.location + state.selectedRange.length <= textView.attributedText.length {
            textView.selectedRange = state.selectedRange
        }
        
        // Restore scroll position
        let maxOffsetY = max(0, textView.contentSize.height - textView.bounds.height)
        if state.contentOffset.y <= maxOffsetY && state.contentOffset.y >= 0 {
            textView.setContentOffset(state.contentOffset, animated: false)
        } else if state.contentOffset.y > maxOffsetY && maxOffsetY >= 0 {
            textView.setContentOffset(CGPoint(x: state.contentOffset.x, y: maxOffsetY), animated: false)
        }
        
        // Update layout
        textView.setNeedsLayout()
        textView.layoutIfNeeded()
    }
    
    // MARK: - Attributed String Utilities
    static func addImageAttributes(
        to attributedString: NSMutableAttributedString,
        isEmoji: Bool,
        linkURL: URL?,
        range: NSRange
    ) {
        attributedString.addAttribute(.isEmoji, value: isEmoji, range: range)
        
        if let linkURL = linkURL {
            attributedString.addAttribute(.imageURL, value: linkURL, range: range)
        }
    }
    
    static func createImageAttachment(
        image: UIImage,
        size: CGSize,
        isEmoji: Bool,
        linkURL: URL?
    ) -> NSMutableAttributedString {
        let attachment = NSTextAttachment()

        // 如果图片尺寸与目标尺寸不匹配，需要缩放图片
        let scaledImage: UIImage
        if image.size != size {
            print("🔄 TextViewUtils: 图片尺寸不匹配，需要缩放：\(image.size.width)x\(image.size.height)px -> \(size.width)x\(size.height)pt")
            scaledImage = resizeImage(image, to: size)
        } else {
            scaledImage = image
        }

        attachment.image = scaledImage
        attachment.bounds = CGRect(origin: .zero, size: size)

        print("🖼️ TextViewUtils: 创建图片附件，目标尺寸: \(size.width)x\(size.height)pt, 原始图片: \(image.size.width)x\(image.size.height)px, 最终图片: \(scaledImage.size.width)x\(scaledImage.size.height)px, isEmoji: \(isEmoji)")

        let imageString = NSAttributedString(attachment: attachment)
        let mutableImageString = NSMutableAttributedString(attributedString: imageString)

        addImageAttributes(
            to: mutableImageString,
            isEmoji: isEmoji,
            linkURL: linkURL,
            range: NSRange(location: 0, length: mutableImageString.length)
        )

        return mutableImageString
    }

    // 图片缩放辅助方法
    private static func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }
}

// MARK: - Custom Text View
class CustomTextView: UITextView {
    private var lastLayoutWidth: CGFloat = 0
    private var lastContentHeight: CGFloat = 0

    override var intrinsicContentSize: CGSize {
        let fixedWidth = self.frame.width
        return self.sizeThatFits(CGSize(width: fixedWidth, height: CGFloat.greatestFiniteMagnitude))
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        let currentWidth = self.bounds.width
        let currentContentHeight = self.contentSize.height

        // Update layout only when there's a significant change
        if abs(currentWidth - lastLayoutWidth) > 0.5 || abs(currentContentHeight - lastContentHeight) > 0.5 {
            lastLayoutWidth = currentWidth
            lastContentHeight = currentContentHeight
            invalidateIntrinsicContentSize()
        }
    }

    override func sizeThatFits(_ size: CGSize) -> CGSize {
        let result = super.sizeThatFits(size)

        // Ensure text container width is correctly set
        if self.textContainer.size.width != size.width && size.width > 0 {
            self.textContainer.size = CGSize(width: size.width, height: 0)
        }

        // Calculate accurate size for attributed text
        if let attributedText = self.attributedText, attributedText.length > 0 {
            let textRect = attributedText.boundingRect(
                with: CGSize(width: size.width, height: CGFloat.greatestFiniteMagnitude),
                options: [.usesLineFragmentOrigin, .usesFontLeading],
                context: nil
            )

            let totalHeight = textRect.height +
                            self.textContainerInset.top +
                            self.textContainerInset.bottom +
                            AttributedTextConstants.extraLayoutPadding

            return CGSize(width: min(result.width, size.width), height: totalHeight)
        }

        return result
    }
}

// MARK: - Placeholder Image Generator
struct PlaceholderImageGenerator {
    static func createSimplePlaceholder(size: CGSize, text: String) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { ctx in
            // Simple placeholder drawing
            UIColor.systemGray5.setFill()
            ctx.fill(CGRect(origin: .zero, size: size))
            
            // Draw text
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            
            let fontSize = max(10, min(14, size.height * 0.2))
            let font = UIFont.systemFont(ofSize: fontSize)
            
            let attrs: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: UIColor.systemGray,
                .paragraphStyle: paragraphStyle
            ]
            
            let textRect = CGRect(
                x: 4,
                y: (size.height - font.lineHeight) / 2,
                width: size.width - 8,
                height: font.lineHeight
            )
            
            (text as NSString).draw(in: textRect, withAttributes: attrs)
        }
    }
}
