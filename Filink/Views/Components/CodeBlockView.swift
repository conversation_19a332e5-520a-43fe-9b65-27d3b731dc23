import SwiftUI
import UIKit

// 定义一个枚举来表示不同的代码块主题
enum CodeBlockTheme {
    case orange
    case blue
    case green
    case purple

    var quoteLineColor: Color {
        switch self {
        case .orange:
            return Color(red: 255/255, green: 149/255, blue: 0/255) // 橙色
        case .blue:
            return Color(red: 0/255, green: 122/255, blue: 255/255) // 蓝色
        case .green:
            return Color(red: 52/255, green: 199/255, blue: 89/255) // 绿色
        case .purple:
            return Color(red: 175/255, green: 82/255, blue: 222/255) // 紫色
        }
    }

    func backgroundColor() -> Color {
        switch self {
        case .orange:
            return Color(red: 255/255, green: 245/255, blue: 230/255, opacity: 0.2) // 橙色主题背景
        case .blue:
            return Color(red: 230/255, green: 242/255, blue: 255/255, opacity: 0.2) // 蓝色主题背景
        case .green:
            return Color(red: 230/255, green: 255/255, blue: 235/255, opacity: 0.2) // 绿色主题背景
        case .purple:
            return Color(red: 245/255, green: 230/255, blue: 255/255, opacity: 0.2) // 紫色主题背景
        }
    }
    
    // 文本字体，用于测量和显示
    static var codeFont: UIFont {
        UIFont.systemFont(ofSize: 16)
    }
    static var swiftUIFont: Font {
        Font.system(size: 16)
    }
}

struct CodeBlockView: View {
    let content: String
    let theme: CodeBlockTheme
    let onCollapseScroll: (() -> Void)?
    let onExpandScroll: ((CGFloat, UnitPoint) -> Void)? // 新增：展开时的智能滚动回调
    
    @State private var viewGeometry: CGRect = .zero // 存储代码块在父视图中的位置

    @State private var isExpanded: Bool = false
    @State private var measuredVStackFullHeight: CGFloat = .zero // 存储 VStack 完整内容时的高度
    @State private var contentAvailableWidth: CGFloat = .zero  // 存储 VStack 内容区域的可用宽度
    @Environment(\.showToast) private var showToast
    private let maxHeightWhenCollapsed: CGFloat = 150
    private let textInternalVerticalPadding: CGFloat = 16 // Text 上下 padding(8) 的总和

    init(content: String, theme: CodeBlockTheme, onCollapseScroll: (() -> Void)? = nil, onExpandScroll: ((CGFloat, UnitPoint) -> Void)? = nil) {
        self.content = content
        self.theme = theme
        self.onCollapseScroll = onCollapseScroll
        self.onExpandScroll = onExpandScroll
    }

    private var showExpandButton: Bool {
        let shouldShow = contentAvailableWidth > 0 && measuredVStackFullHeight > maxHeightWhenCollapsed
        return shouldShow
    }

    private var targetOverallHeight: CGFloat {
        guard contentAvailableWidth > 0, measuredVStackFullHeight > 0 else {
            return maxHeightWhenCollapsed
        }
        if showExpandButton {
            return isExpanded ? measuredVStackFullHeight : maxHeightWhenCollapsed
        } else {
            return measuredVStackFullHeight
        }
    }

    var body: some View {
        ZStack(alignment: .topTrailing) { // 用于放置引号和提示文本
            VStack(alignment: .leading, spacing: 0) { // 主要内容块
                // GeometryReader 现在包裹 Text，以获取 Text 的实际可用宽度
                GeometryReader { textGeometry in
                    Text(content)
                        .font(CodeBlockTheme.swiftUIFont)
                        .foregroundColor(.primary)
                        .lineLimit(nil)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(width: textGeometry.size.width, alignment: .leading) // Text 宽度填充 GeometryReader
                        .background(
                            GeometryReader { fullGeometry in
                                Color.clear.onAppear {
                                    // 获取代码块在父视图中的位置
                                    self.viewGeometry = fullGeometry.frame(in: .global)
                                }
                            }
                        )
                        .onAppear {
                            updateWidthAndRecalculateHeight(
                                newContentWidth: textGeometry.size.width,
                                currentContent: self.content,
                                source: "GR onAppear"
                            )
                        }
                        .onChange(of: textGeometry.size.width) { newWidth in
                            updateWidthAndRecalculateHeight(
                                newContentWidth: newWidth,
                                currentContent: self.content,
                                source: "GR onChangeWidth"
                            )
                        }
                }
            }
            .padding(8) // VStack 的整体内边距 (影响 Text 的 GeometryReader 获取的宽度)
            .padding(.leading, 8) // VStack 左侧额外内边距
            .frame(maxWidth: .infinity) // VStack 宽度填满 ZStack (减去 ZStack 的 padding)
            .background(
                theme.backgroundColor()
                // LinearGradient(gradient: Gradient(colors: [theme.backgroundColor().opacity(0.8), theme.backgroundColor()]), startPoint: .bottom, endPoint: .top)
            )
            .overlay(
                Rectangle().fill(theme.quoteLineColor).frame(width: 4).cornerRadius(2),
                alignment: .leading
            )
            .cornerRadius(8)
            .clipped() // 裁剪超出 VStack 高度的内容
            .onTapGesture { 
                UIPasteboard.general.string = content
                showToast(.success("已复制到剪贴板"))
            }
            .overlay(alignment: .bottomTrailing) {
                if self.showExpandButton {
                    Button(action: {
                        let wasExpanded = isExpanded
                        withAnimation(.easeInOut(duration: 0.25)) {
                            isExpanded.toggle()
                        }

                        // 如果从展开状态变为折叠状态，触发滚动调整
                        if wasExpanded && !isExpanded {
                            // 延迟执行滚动，确保动画完成后再滚动
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                                onCollapseScroll?()
                            }
                        }
                        // 判断是否需要滚动
                        if !wasExpanded && isExpanded {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                                handleExpandScroll()
                            }
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                            .font(.title2)
                            .foregroundColor(theme.quoteLineColor)
                            .padding(8)
                            .background(Color.white.opacity(0.8))
                            .clipShape(Circle())
                            .shadow(radius: 3)
                    }
                    .padding(8)
                    .transition(.opacity.animation(.easeInOut(duration: 0.15)))
                }
            }

            // 右上角引号和提示文本，相对于 ZStack 定位
            Text("”")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(theme.quoteLineColor)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                .padding(.trailing, 8) // 视觉边距，因为ZStack的padding已在外部
                .padding(.top, 6)

            Text("点击内容复制")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(theme.quoteLineColor.opacity(0.7))
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                .padding(.trailing, 8 + 24 + 2) // 8(引号margin), 24(引号宽约), 8(提示文本margin)
                .padding(.top, 6 - 14) // 6(引号top), 4(提示文本视觉offset)
                .allowsHitTesting(false)

        } // End of ZStack
        .frame(height: targetOverallHeight) // 将计算出的高度应用到 ZStack
        .frame(maxWidth: .infinity) // 占满父视图给定的最大宽度
        .padding(.horizontal, 16) // ZStack 的外部水平 padding
        .onChange(of: content) { newContent in // 当外部 CodeBlockView.content 变化
            if contentAvailableWidth > 0 {
                 updateWidthAndRecalculateHeight(newContentWidth: contentAvailableWidth, currentContent: newContent, source: "Root onChangeContent")
            }
        }
    }

    private func updateWidthAndRecalculateHeight(newContentWidth: CGFloat, currentContent: String, source: String) {
        if abs(contentAvailableWidth - newContentWidth) > 0.1 || contentAvailableWidth == .zero {
            contentAvailableWidth = newContentWidth
        }
        recalculateVStackFullHeight(textRenderWidth: contentAvailableWidth, forContent: currentContent, source: source)
    }

    private func recalculateVStackFullHeight(textRenderWidth: CGFloat, forContent: String, source: String) {
        guard textRenderWidth > 0 else {
            if measuredVStackFullHeight != 0 { measuredVStackFullHeight = 0 }
            return
        }

        let pureTextHeight = calculateTextHeight(text: forContent, font: CodeBlockTheme.codeFont, width: textRenderWidth)
        let calculatedFullVStackHeight = pureTextHeight + textInternalVerticalPadding // VStack 高度 = 纯文本高度 + Text的垂直padding

        if abs(measuredVStackFullHeight - calculatedFullVStackHeight) > 0.1 || (measuredVStackFullHeight == .zero && calculatedFullVStackHeight > 0) {
            measuredVStackFullHeight = calculatedFullVStackHeight
        }
    }

    private func calculateTextHeight(text: String, font: UIFont, width: CGFloat) -> CGFloat {
        let attributedString = NSAttributedString(string: text, attributes: [.font: font])
        let textStorage = NSTextStorage(attributedString: attributedString)
        let layoutManager = NSLayoutManager()
        textStorage.addLayoutManager(layoutManager)
        let containerWidth = max(1, width)
        let textContainer = NSTextContainer(size: CGSize(width: containerWidth, height: .greatestFiniteMagnitude))
        textContainer.lineFragmentPadding = 0
        layoutManager.addTextContainer(textContainer)
        layoutManager.ensureLayout(for: textContainer)
        return ceil(layoutManager.usedRect(for: textContainer).size.height)
    }
    
    /// 处理展开时的智能滚动判断
    private func handleExpandScroll() {
        guard let onExpandScroll = onExpandScroll else { return }
        
        // 获取当前视图的可见区域
        let screenHeight = UIScreen.main.bounds.height
        
        // 计算展开后的代码块底部边界
        let expandedBottom = viewGeometry.minY + measuredVStackFullHeight
        
        // 检查是否需要滚动
        if expandedBottom > screenHeight {
            // 超出底部，需要滚动
            onExpandScroll(measuredVStackFullHeight, .bottom)
        }
    }
}
