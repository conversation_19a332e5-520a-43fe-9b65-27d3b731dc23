import SwiftUI

/// 一个可定制的、从底部滑出的抽屉视图。
///
/// 这个视图支持多个高度档位，并提供了流畅的拖拽和吸附动画。
struct Drawer<Content: View>: View {
    // MARK: - 属性

    /// 控制抽屉是否可见的绑定。
    @Binding var isOpen: Bool

    /// 抽屉可以吸附的高度档位，从屏幕底部算起。
    /// 数组中的值应该在 0 到 `UIScreen.main.bounds.height` 之间。
    let heights: [CGFloat]

    /// 抽屉的圆角半径。
    let cornerRadius: CGFloat

    /// 抽屉的背景颜色。
    let backgroundColor: Color

    /// 抽屉内容视图。
    private let content: Content

    // MARK: - 状态

    @GestureState private var translation: CGFloat = 0
    @State private var currentHeight: CGFloat
    @State private var isExpandedIcon: Bool = false  // 新增：用于分离图标状态和高度动画

    // MARK: - 初始化

    /// 创建一个新的抽屉视图。
    ///
    /// - Parameters:
    ///   - isOpen: 控制抽屉可见性的绑定。
    ///   - heights: 抽屉可以吸附的高度档位。默认为屏幕高度的 50% 和 90%。
    ///   - cornerRadius: 抽屉的圆角半径。默认为 20。
    ///   - backgroundColor: 抽屉的背景颜色。
    ///   - content: 在抽屉中显示的内容。
    init(
        isOpen: Binding<Bool>,
        heights: [CGFloat]? = nil,
        cornerRadius: CGFloat = 20,
        backgroundColor: Color = Color(.secondarySystemBackground),
        @ViewBuilder content: () -> Content
    ) {
        self._isOpen = isOpen

        // 使用 SafeAreaHelper 获取精确的安全区域
        let topInset = SafeAreaHelper.topSafeArea
        let bottomInset = SafeAreaHelper.bottomSafeArea
        let screenHeight = UIScreen.main.bounds.height

        // 计算最大可用高度，留出合适的顶部空间
        // 顶部空间 = 顶部安全区 + 导航栏高度 + 额外间距
        let navigationBarHeight: CGFloat = 44
        let extraTopPadding: CGFloat = 16
        let reservedTopSpace = topInset + navigationBarHeight + extraTopPadding
        let maxAvailableHeight = screenHeight - reservedTopSpace - bottomInset

        // 调试信息
        print(
            "Drawer 初始化 - 屏幕高度: \(screenHeight), 顶部安全区: \(topInset), 底部安全区: \(bottomInset)"
        )
        print("  - 保留顶部空间: \(reservedTopSpace), 最大可用高度: \(maxAvailableHeight)")

        let defaultHeights = [
            screenHeight * 0.5,  // 中等高度：屏幕高度的50%
            maxAvailableHeight,  // 最大高度：可用高度，不会被遮挡
        ]

        self.heights = (heights ?? defaultHeights).sorted()
        self.cornerRadius = cornerRadius
        self.backgroundColor = backgroundColor
        self.content = content()
        self._currentHeight = State(initialValue: self.heights.first ?? 0)
    }

    // MARK: - 视图构建

    var body: some View {
        ZStack(alignment: .bottom) {
            // 背景遮罩 - 覆盖整个屏幕包括TabBar
            Color.black.opacity(isOpen ? 0.3 : 0.0)
                .ignoresSafeArea(.all)
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isOpen = false
                    }
                }
                .allowsHitTesting(isOpen)
                .zIndex(999)  // 确保背景遮罩在TabBar之上

            // 抽屉内容
            VStack(spacing: 0) {
                // 标题栏，包含拖拽指示器和按钮
                ZStack {
                    // 拖拽指示器（居中）
                    Capsule()
                        .fill(Color.gray.opacity(0.4))
                        .frame(width: 40, height: 5)

                    // 右上角的按钮
                    HStack(spacing: 22) {
                        Spacer()

                        // 展开/折叠按钮
                        Button(action: {
                            guard heights.count > 1 else { return }

                            // 1. 立即更新图标
                            let isCurrentlyExpanded =
                                self.currentHeight == self.heights.last
                            self.isExpandedIcon = !isCurrentlyExpanded

                            // 2. 延迟一小段时间再执行高度动画
                            DispatchQueue.main.asyncAfter(
                                deadline: .now() + 0.1
                            ) {
                                let newHeight =
                                    !isCurrentlyExpanded
                                    ? self.heights.last! : self.heights.first!
                                self.currentHeight = newHeight
                            }
                        }) {
                            // 图标状态由 isExpandedIcon 控制，不再与 currentHeight 直接绑定
                            Image(
                                systemName: self.isExpandedIcon
                                    ? "arrow.down.right.and.arrow.up.left"
                                    : "arrow.up.left.and.arrow.down.right"
                            )
                            .font(
                                .system(size: 16, weight: .medium).monospaced()
                            )
                            .foregroundColor(.secondary)
                        }

                        // 关闭按钮
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                self.isOpen = false
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(
                                    .system(size: 16, weight: .medium)
                                        .monospaced()
                                )
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.top, 12)
                .padding(.bottom, 8)

                // 实际内容
                content
                    .frame(maxWidth: .infinity, maxHeight: .infinity)  // 确保内容撑满
                    .clipped()  // 裁剪超出范围的内容
            }
            .frame(width: UIScreen.main.bounds.width, height: currentHeight)
            .background(backgroundColor)
            .cornerRadius(cornerRadius, corners: [.topLeft, .topRight])
            .shadow(color: .black.opacity(0.2), radius: 10, y: -5)
            .offset(y: isOpen ? translation : UIScreen.main.bounds.height)  // 修正：关闭时使用屏幕高度偏移
            .clipped()  // 裁剪整个抽屉的超出部分
            .gesture(dragGesture)
            .animation(.easeInOut(duration: 0.3), value: isOpen)
            .animation(.easeInOut(duration: 0.3), value: currentHeight)  // 简化动画，移除 translation 动画
            .zIndex(1000)  // 确保抽屉内容在最顶层
            .opacity(isOpen ? 1.0 : 0.0)
            .offset(y: isOpen ? 0 : UIScreen.main.bounds.height)
        }
        .onChange(of: isOpen) { isOpen in
            // 当抽屉打开时，重置为最小高度
            if isOpen {
                currentHeight = heights.first ?? 0
            }
        }
    }

    // MARK: - 手势与位置计算

    private var dragGesture: some Gesture {
        DragGesture()
            .updating($translation) { value, state, _ in
                // 只允许向下拖拽（正值），防止向上拖拽时视图跳动
                state = max(0, value.translation.height)
            }
            .onEnded { value in
                let verticalDrag = value.predictedEndTranslation.height

                // 如果向下拖拽超过100个点，则关闭抽屉
                if verticalDrag > 100 {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isOpen = false
                    }
                }
                // 移除了向上拖拽来改变高度的逻辑。
                // 高度现在是固定的，除非通过其他方式改变 `currentHeight`。
            }
    }
}

// MARK: - 辅助扩展

extension View {
    fileprivate func cornerRadius(_ radius: CGFloat, corners: UIRectCorner)
        -> some View
    {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

private struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
