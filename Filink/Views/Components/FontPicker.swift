import SwiftUI
import UIKit
import UniformTypeIdentifiers

struct FontPicker: UIViewControllerRepresentable {

    @Environment(\.presentationMode) var presentationMode
    var onFontPicked: (URL) -> Void

    func makeUIViewController(context: Context)
        -> UIDocumentPickerViewController
    {
        // 使用现代的 UTType API 来指定支持的字体文件类型
        //        let supportedTypes: [UTType] = [.trueTypeFont, .openTypeFont]
        let supportedTypes: [UTType] = [
            UTType("public.truetype-ttf-font")!,
            UTType("public.opentype-font")!,
        ]
        let picker = UIDocumentPickerViewController(
            forOpeningContentTypes: supportedTypes)
        picker.allowsMultipleSelection = false
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(
        _ uiViewController: UIDocumentPickerViewController, context: Context
    ) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        var parent: FontPicker

        init(_ parent: FontPicker) {
            self.parent = parent
        }

        func documentPicker(
            _ controller: UIDocumentPickerViewController,
            didPickDocumentsAt urls: [URL]
        ) {
            guard let url = urls.first else { return }

            // 在调用回调之前，确保我们获得了文件的安全访问权限
            let shouldStopAccessing = url.startAccessingSecurityScopedResource()
            defer {
                if shouldStopAccessing {
                    url.stopAccessingSecurityScopedResource()
                }
            }

            parent.onFontPicked(url)
            parent.presentationMode.wrappedValue.dismiss()
        }

        func documentPickerWasCancelled(
            _ controller: UIDocumentPickerViewController
        ) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
