import SwiftUI

struct LinkCardView: View {
    let iconSystemName: String = "link" // Default icon
    let imageURL: URL?
    let domainName: String
    let title: String
    let tagNumber: Int
    let description: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                if let url = imageURL {
                    AsyncImage(url: url) { phase in
                        if let image = phase.image {
                            image
                                .resizable()
                                .scaledToFit()
                                .frame(width: 12, height: 12) // Adjust size as needed
                        } else if phase.error != nil {
                            Image(systemName: iconSystemName) // Fallback to default icon on error
                                .resizable()
                                .scaledToFit()
                                .frame(width: 12, height: 12) // Adjust size as needed
                                .foregroundColor(.secondary)
                        } else {
                            ProgressView() // Show progress while loading
                                .frame(width: 12, height: 12) // Adjust size as needed
                        }
                    }
                } else {
                    Image(systemName: iconSystemName)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 12, height: 12) // Adjust size as needed
                        .foregroundColor(.secondary)
                }
                
                Text(domainName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text(title)
                    .font(.headline)
                    .foregroundColor(Color.blue)
                Text("\(tagNumber)")
                    .font(.caption2)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 2)
                    .background(Color(UIColor.systemGray5))
                    .cornerRadius(4)
            }
            
            Text(description)
                .font(.body)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading) // 确保VStack填满可用宽度
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(UIColor.systemGray5), lineWidth: 1)
        )
        // .shadow(radius: 2)
    }
}

struct LinkCardView_Previews: PreviewProvider {
    static var previews: some View {
        LinkCardView(imageURL: URL(string: "https://www.example.com/icon.png"), domainName: "linux-do.ovinc.cn", title: "Linux Do 分发站", tagNumber: 30, description: "LinuxDo 分发平台，提供邀请码、CDKEY 等资源分发能力，一人一码，安全高效")
            .padding()
            .previewLayout(.sizeThatFits)
        
        LinkCardView(imageURL: nil, domainName: "linux-do.ovinc.cn", title: "Linux Do 分发站", tagNumber: 30, description: "LinuxDo 分发平台，提供邀请码、CDKEY 等资源分发能力，一人一码，安全高效")
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
