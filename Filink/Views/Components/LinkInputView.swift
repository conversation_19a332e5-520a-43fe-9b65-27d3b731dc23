import SwiftUI

/// 插入或编辑链接的视图，通常以底部抽屉（Sheet）形式呈现
struct LinkInputView: View {
    
    // MARK: - State
    
    /// 链接要显示的文本
    @State private var linkText: String
    /// 链接的 URL 地址
    @State private var linkURL: String
    
    /// 当前环境，用于关闭视图
    @Environment(\.presentationMode) var presentationMode
    
    /// 完成后的回调，传递输入的文本和URL
    var onComplete: (String, String) -> Void
    
    // MARK: - Initialization
    
    /// 初始化方法
    /// - Parameters:
    ///   - initialText: 初始显示的文本（例如，用户选中的文本）
    ///   - initialURL: 初始的 URL（如果是编辑模式）
    ///   - onComplete: 完成回调
    init(initialText: String = "", initialURL: String = "", onComplete: @escaping (String, String) -> Void) {
        _linkText = State(initialValue: initialText)
        _linkURL = State(initialValue: initialURL)
        self.onComplete = onComplete
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("链接信息")) {
                    TextField("显示文本", text: $linkText)
                    TextField("URL 地址", text: $linkURL)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                }
            }
            .onAppear(perform: autoFillURLFromPasteboard)
            .navigationTitle("插入链接")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 左侧取消按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                // 右侧完成按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        // 当用户点击完成时，调用回调并关闭视图
                        onComplete(linkText, linkURL)
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(linkText.isEmpty || !isValidURL(linkURL)) // 如果URL无效或文本为空，则禁用按钮
                }
            }
        }
    }
    
    // MARK: - Private Helpers
    
    /// 视图出现时，尝试从剪贴板自动填充URL
    private func autoFillURLFromPasteboard() {
        // 仅当URL字段为空时才尝试自动填充
        guard linkURL.isEmpty else { return }
        
        // 检查剪贴板中是否有字符串
        if let pasteboardString = UIPasteboard.general.string {
            // 验证字符串是否为有效URL
            if isValidURL(pasteboardString) {
                // 如果是，则自动填充
                self.linkURL = pasteboardString
            }
        }
    }
    
    /// 验证 URL 字符串是否有效
    private func isValidURL(_ urlString: String) -> Bool {
        // 简单的URL验证：至少要能创建一个URL对象，并且包含 http 或 https
        if let url = URL(string: urlString),
           let scheme = url.scheme,
           ["http", "https"].contains(scheme.lowercased()) {
            return true
        }
        return false
    }
}

// MARK: - Preview
struct LinkInputView_Previews: PreviewProvider {
    static var previews: some View {
        LinkInputView(initialText: "示例网站") { text, url in
            print("输入的文本: \(text), URL: \(url)")
        }
    }
}
