import SwiftUI

struct PollView: View {
    let pollItem: PollItem
    
    @State private var selectedOptionIDs: Set<String> = []
    @State private var isShowingResults = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            if isShowingResults {
                PollResultsView(pollItem: pollItem, backAction: {
                    isShowingResults = false
                })
            } else {
                PollVotingView(
                    pollItem: pollItem,
                    selectedOptionIDs: $selectedOptionIDs,
                    showResultsAction: {
                        isShowingResults = true
                    }
                )
            }
        }
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - Voting View
struct PollVotingView: View {
    let pollItem: PollItem
    @Binding var selectedOptionIDs: Set<String>
    let showResultsAction: () -> Void
    
    var body: some View {
        VStack {
            HStack(alignment: .top) {
                // 左侧：投票选项
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(pollItem.options) { option in
                        Button(action: {
                            toggleSelection(for: option.id)
                        }) {
                            HStack {
                                Image(systemName: selectedOptionIDs.contains(option.id) ? "checkmark.square.fill" : "square")
                                    .foregroundColor(selectedOptionIDs.contains(option.id) ? .accentColor : .primary)
                                Text(option.label)
                                    .foregroundColor(.primary)
                            }
                        }
                    }
                }
                
                Spacer()
                
                // 右侧：投票信息
                VStack(spacing: 10) {
                    Text("\(pollItem.voterCount)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    Text("投票人")
                        .font(.caption)
                    
                    Divider()
                    
                    HStack {
                        Image(systemName: "list.bullet")
                        Text("最多选择 \(pollItem.maxSelections) 个选项。")
                            .font(.footnote)
                    }
                    .foregroundColor(.secondary)
                }
                .frame(width: 120)
            }
            
            Divider().padding(.vertical, 10)
            
            // 底部：操作按钮
            HStack {
                Button(action: {
                    // TODO: 实现投票逻辑
                    print("Voted for: \(selectedOptionIDs)")
                }) {
                    Label("立即投票!", systemImage: "paperplane.fill")
                }
                .buttonStyle(.borderedProminent)
                .disabled(selectedOptionIDs.isEmpty)
                
                Spacer()
                
                Button(action: showResultsAction) {
                    Label("结果", systemImage: "chart.bar.xaxis")
                }
                .buttonStyle(.bordered)
            }
        }
    }
    
    private func toggleSelection(for optionID: String) {
        if selectedOptionIDs.contains(optionID) {
            selectedOptionIDs.remove(optionID)
        } else {
            if selectedOptionIDs.count < pollItem.maxSelections {
                selectedOptionIDs.insert(optionID)
            }
        }
    }
}

// MARK: - Results View
struct PollResultsView: View {
    let pollItem: PollItem
    let backAction: () -> Void
    
    // 模拟的投票结果数据
    private var results: [(PollOption, Int)] {
        // 在实际应用中，这里应该是从服务器获取或计算得出的真实数据
        let totalVotes = pollItem.voterCount > 0 ? pollItem.voterCount : pollItem.options.reduce(0) { partialResult, option in
            partialResult + Int.random(in: 5...50)
        }
        var remainingVotes = totalVotes
        
        return pollItem.options.map { option in
            let votes = Int.random(in: 0...remainingVotes)
            remainingVotes -= votes
            return (option, votes)
        }
    }
    
    private var totalVotes: Int {
        results.reduce(0) { $0 + $1.1 }
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("投票结果")
                .font(.title2)
                .fontWeight(.bold)
                .padding(.bottom, 10)
            
            ForEach(results, id: \.0.id) { (option, votes) in
                VStack(alignment: .leading) {
                    HStack {
                        Text(option.label)
                        Spacer()
                        Text("\(votes) 票")
                            .fontWeight(.semibold)
                    }
                    
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            Capsule().fill(Color.gray.opacity(0.3))
                                .frame(height: 10)
                            Capsule().fill(Color.accentColor)
                                .frame(width: calculatePercentageWidth(for: votes, in: geometry.size.width), height: 10)
                        }
                    }
                    .frame(height: 10)
                }
                .padding(.vertical, 5)
            }
            
            Divider().padding(.vertical, 10)
            
            HStack {
                Button(action: backAction) {
                    Label("返回投票", systemImage: "arrow.uturn.backward")
                }
                .buttonStyle(.bordered)
                
                Spacer()
                
                Text("总计: \(totalVotes) 票")
                    .font(.footnote)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func calculatePercentageWidth(for votes: Int, in totalWidth: CGFloat) -> CGFloat {
        guard totalVotes > 0 else { return 0 }
        let percentage = CGFloat(votes) / CGFloat(totalVotes)
        return totalWidth * percentage
    }
}

// MARK: - Preview
struct PollView_Previews: PreviewProvider {
    static var previews: some View {
        let samplePoll = PollItem(
            name: "poll-preview",
            options: [
                PollOption(id: "1", label: "JDK8以前的版本"),
                PollOption(id: "2", label: "JDK8"),
                PollOption(id: "3", label: "JDK11"),
                PollOption(id: "4", label: "JDK17"),
            ],
            type: "multiple",
            minSelections: 1,
            maxSelections: 2,
            status: "open",
            isPublic: true,
            resultsVisibility: "always",
            voterCount: 70
        )
        
        PollView(pollItem: samplePoll)
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
