//
//  StorageLegendView.swift
//  Filink
//
//  Created by wot on 2025/5/31.
//

import SwiftUI
import UIKit

// MARK: - 存储单位格式化工具
extension Double {
    /// 格式化存储大小，自动选择合适的单位
    /// - Parameter sizeInGB: 以GB为单位的大小
    /// - Returns: 格式化后的字符串
    func formatStorageSize() -> String {
        let sizeInBytes = self * 1024 * 1024 * 1024 // 转换为字节

        if sizeInBytes >= 1024 * 1024 * 1024 { // >= 1GB
            return String(format: "%.2f GB", self)
        } else if sizeInBytes >= 1024 * 1024 { // >= 1MB
            let sizeInMB = self * 1024
            return String(format: "%.2f MB", sizeInMB)
        } else if sizeInBytes >= 1024 { // >= 1KB
            let sizeInKB = self * 1024 * 1024
            return String(format: "%.2f KB", sizeInKB)
        } else { // < 1KB
            return String(format: "%.0f B", sizeInBytes)
        }
    }
}

// MARK: - 1. 数据结构定义
struct StorageSegment: Identifiable, Equatable {
    var id = UUID()
    var name: String
    var size: Double  // 单位：GB
    var color: Color
    
    static func == (lhs: StorageSegment, rhs: StorageSegment) -> Bool {
        return lhs.name == rhs.name && 
               lhs.size == rhs.size &&
               lhs.id == rhs.id
    }
}

// MARK: - 2. 图例组件
struct LegendWrapView: View {
    let segments: [StorageSegment]
    let spacing: CGFloat
    
    @State private var calculatedRows: [[StorageSegment]] = [[]]
    @State private var didCalculate = false
    @State private var height: CGFloat = 0 // 添加高度状态变量来跟踪实际内容高度

    var body: some View {
        // 简化视图结构，避免复杂的条件渲染
        GeometryReader { geometry in
            contentView(width: geometry.size.width)
                .onAppear {
                    // 在视图出现时计算行
                    self.calculatedRows = self.calculateRows(in: geometry.size.width)
                    self.didCalculate = true
                    
                    // 估算高度：每行高度约为 28 点（标签高度 + 行间距）
                    self.height = CGFloat(self.calculatedRows.count) * 28.0
                }
        }
        .frame(minHeight: height) // 使用计算出的高度
    }
    
    // 将内容视图提取到单独的方法中
    @ViewBuilder
    private func contentView(width: CGFloat) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 确保有行可以显示
            if calculatedRows.isEmpty {
                EmptyView()
            } else {
                ForEach(0..<calculatedRows.count, id: \.self) { rowIndex in
                    rowView(for: calculatedRows[rowIndex])
                }
            }
        }
    }
    
    // 单行视图
    @ViewBuilder
    private func rowView(for segments: [StorageSegment]) -> some View {
        HStack(spacing: spacing) {
            ForEach(segments) { segment in
                LegendLabel(segment: segment)
            }
            Spacer(minLength: 0) // 添加 Spacer 确保行内容左对齐
        }
    }
    
    // 计算行布局
    private func calculateRows(in totalWidth: CGFloat) -> [[StorageSegment]] {
        var rows: [[StorageSegment]] = [[]]
        var currentRowWidth: CGFloat = 0
        
        for segment in segments {
            // 估算每个图例标签的宽度
            let labelWidth = estimateLabelWidth(for: segment)
            
            if currentRowWidth + labelWidth + spacing > totalWidth && !rows[rows.count-1].isEmpty {
                // 换新行
                rows.append([segment])
                currentRowWidth = labelWidth
            } else {
                rows[rows.count-1].append(segment)
                currentRowWidth += labelWidth + spacing
            }
        }
        
        return rows
    }
    
    // 估算标签宽度
    private func estimateLabelWidth(for segment: StorageSegment) -> CGFloat {
        // 简单估算：文本长度 * 平均字符宽度 + 图标宽度 + 间距
        let formattedText = "\(segment.name): \(segment.size.formatStorageSize())"
        let textLength = formattedText.count

        let averageCharWidth: CGFloat = 7.0 // 估算平均字符宽度
        let iconWidth: CGFloat = 10.0 // 圆形图标宽度
        let horizontalPadding: CGFloat = 12.0 // 水平内边距
        let iconTextSpacing: CGFloat = 4.0 // 图标和文本间距

        return CGFloat(textLength) * averageCharWidth + iconWidth + iconTextSpacing + horizontalPadding
    }
}

// MARK: - 3. 图例标签视图
struct LegendLabel: View {
    let segment: StorageSegment

    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(segment.color)
                .frame(width: 10, height: 10)

            Text("\(segment.name): \(segment.size.formatStorageSize())")
                .font(.caption)
                .foregroundColor(.primary)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 4)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(4)
    }
}

// MARK: - 4. 存储使用条形图视图
struct UsageBarView: View {
    let segments: [StorageSegment]
    let totalSizeGB: Double
    let showRemainingSpace: Bool
    
    private let maxSegments = 8
    private let segmentSpacing: CGFloat = 1.0
    
    private var processedSegments: [StorageSegment] {
        if segments.count <= maxSegments {
            return segments
        }
        
        let sortedSegments = segments.sorted { $0.size > $1.size }
        var result = Array(sortedSegments.prefix(maxSegments - 1))
        let remainingSegments = sortedSegments.suffix(from: maxSegments - 1)
        let totalRemainingSize = remainingSegments.reduce(0) { $0 + $1.size }
        
        if totalRemainingSize > 0 {
            result.append(StorageSegment(
                name: "其他",
                size: totalRemainingSize,
                color: .gray
            ))
        }
        
        return result
    }

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: segmentSpacing) {
                let displaySegments = processedSegments
                let totalSegmentsGB = displaySegments.reduce(0.0) { $0 + $1.size }
                let totalSpacingWidth = segmentSpacing * CGFloat(displaySegments.count - 1)
                let availableWidth = geometry.size.width - totalSpacingWidth
                
                ForEach(displaySegments) { segment in
                    let widthRatio = totalSegmentsGB > 0 ? segment.size / totalSegmentsGB : 0
                    
                    Rectangle()
                        .fill(segment.color)
                        .frame(width: max(availableWidth * widthRatio, 1))
                }
                
                if showRemainingSpace && totalSizeGB > totalSegmentsGB {
                    let remainingSpaceGB = totalSizeGB - totalSegmentsGB
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: max(availableWidth * (remainingSpaceGB / totalSizeGB), 0))
                }
            }
            .cornerRadius(5)
        }
        .frame(height: 20)
    }
}

// MARK: - 5. 存储使用组合视图
struct StorageUsageDetailView: View {
    let usedGB: Double
    @Binding var segments: [StorageSegment]
    let totalGB: Double?

    init(usedGB: Double, segments: Binding<[StorageSegment]>, totalGB: Double? = nil) {
        self.usedGB = usedGB
        self._segments = segments
        self.totalGB = totalGB
    }
    
    init(usedGB: Double, segments: [StorageSegment], totalGB: Double? = nil) {
        self.usedGB = usedGB
        self._segments = .constant(segments)
        self.totalGB = totalGB
    }

    private var displayTotalGB: Double {
        if let explicitTotalGB = totalGB {
            return explicitTotalGB
        } else {
            let segmentsTotalGB = segments.reduce(0) { $0 + $1.size }
            return max(segmentsTotalGB, usedGB)
        }
    }

    private var showRemainingSpace: Bool {
        return totalGB != nil
    }
    
    private var percentage: Double {
        let validTotalGB = max(displayTotalGB, 0.001)
        return (usedGB / validTotalGB) * 100
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("应用占用空间")
                .font(.title2)
                .bold()
                .padding(.top, 16)
            
            UsageBarView(segments: segments, totalSizeGB: displayTotalGB, showRemainingSpace: showRemainingSpace)

            Text("\(usedGB.formatStorageSize()) 已使用（共 \(displayTotalGB.formatStorageSize())，\(String(format: "%.1f", percentage))%）")
            .font(.caption)
            .foregroundColor(.secondary)

            LegendWrapView(segments: segments, spacing: 8)
                .padding(.bottom, 16)
                
        }
        .padding(.horizontal)
        .background(Color(UIColor { traitCollection in
            return traitCollection.userInterfaceStyle == .dark ? .secondarySystemBackground : .systemBackground
        }))
        .cornerRadius(10)
        .listRowInsets(EdgeInsets())
    }
}

// MARK: - 6. 预览
struct StorageUsageDetailView_Previews: PreviewProvider {
    static let sampleSegments: [StorageSegment] = [
        .init(name: "App", size: 120, color: .red),
        .init(name: "照片", size: 20, color: .orange),
        .init(name: "iOS", size: 10, color: .yellow),
        .init(name: "信息", size: 5, color: .green),
        .init(name: "系统数据", size: 7.9, color: .gray)
    ]
    
    static var previews: some View {
        StorageUsageDetailView(usedGB: 200.2, segments: sampleSegments, totalGB: 256.0)
            .previewLayout(.sizeThatFits)
            .preferredColorScheme(.light)
    }
}
