import SwiftUI
import Combine

// MARK: - 1. Toast Type Definition

// 定义Toast消息的类型，包括成功、错误和信息
enum ToastType {
    case success(String)  // 成功消息，带有一个字符串
    case error(String)  // 错误消息，带有一个字符串
    case info(String)  // 信息消息，带有一个字符串

    // 根据Toast类型返回相应的背景颜色
    var backgroundColor: Color {
        switch self {
        case .success: return Color.green.opacity(0.9)  // 成功为绿色
        case .error: return Color.red.opacity(0.9)  // 错误为红色
        case .info: return Color.blue.opacity(0.9)  // 信息为蓝色
        }
    }

    // 根据Toast类型返回相应的系统图标
    var icon: Image {
        switch self {
        case .success: return Image(systemName: "checkmark.circle.fill") // 使用fill版本图标可能更好看
        case .error: return Image(systemName: "xmark.octagon.fill")
        case .info: return Image(systemName: "info.circle.fill")
        }
    }

    // 获取Toast消息的文本内容
    var message: String {
        switch self {
        case .success(let msg), .error(let msg), .info(let msg):
            return msg  // 返回关联的字符串消息
        }
    }
}

// MARK: - 2. Toast View

// Toast消息的视图结构
struct ToastView: View {
    let type: ToastType  // Toast的类型

    var body: some View {
        HStack(alignment: .center, spacing: 10) {
            type.icon  // 显示Toast图标
                .resizable() // 确保图标可以调整大小
                .frame(width: 20, height: 20) // 给图标一个固定大小
                .foregroundStyle(.white)  // 图标颜色为白色
            Text(type.message)  // 显示Toast消息文本
                .foregroundColor(.white)  // 文本颜色为白色
                .font(.subheadline)  // 字体为副标题
                .multilineTextAlignment(.leading)  // 多行文本左对齐
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 15) // 稍微调整一下水平padding
        .background(type.backgroundColor)  // 背景颜色根据Toast类型设置
        .cornerRadius(25)  // 圆角
        .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 3) // 稍微调整阴影
    }
}

// MARK: - 3. ShowToastAction for Environment (Optional, for View-internal use)

// 用于在环境中传递显示Toast动作的结构体
struct ShowToastAction {
    typealias Action = (ToastType) -> Void  // 定义动作类型
    let action: Action  // 实际的动作闭包

    // 允许像函数一样调用此结构体
    func callAsFunction(_ type: ToastType) {
        action(type)
    }
}

// 扩展EnvironmentValues以添加showToast环境变量
extension EnvironmentValues {
    // 如果你没有自定义 @Entry，通常会这样定义 EnvironmentKey
    private struct ShowToastKey: EnvironmentKey {
        static let defaultValue: ShowToastAction = ShowToastAction(action: { _ in
            // 默认空实现，或者可以打印一个警告
            print("Warning: ShowToastAction was called but not handled. Did you forget to apply .withToast() modifier?")
        })
    }

    var showToast: ShowToastAction {
        get { self[ShowToastKey.self] }
        set { self[ShowToastKey.self] = newValue }
    }
}


// MARK: - 4. Global Toast Presenter (for Class-internal use)

class GlobalToastPresenter: ObservableObject {
    static let shared = GlobalToastPresenter()
    let showToastSubject = PassthroughSubject<ToastType, Never>()

    private init() {}

    func show(type: ToastType) {
        DispatchQueue.main.async { // Ensure UI updates are on the main thread
            self.showToastSubject.send(type)
        }
    }

    // Convenience methods
    func showSuccess(_ message: String) {
        show(type: .success(message))
    }

    func showError(_ message: String) {
        show(type: .error(message))
    }

    func showInfo(_ message: String) {
        show(type: .info(message))
    }
}

// MARK: - 5. Toast Modifier

// Toast修饰符，用于管理Toast的显示和隐藏逻辑
struct ToastModifier: ViewModifier {
    @State private var currentToast: ToastType? = nil // 更清晰的命名
    @State private var dismissTask: DispatchWorkItem? = nil

    // Internal action handler for both environment and global presenter
    private func displayToast(_ type: ToastType) {
        dismissTask?.cancel() // Cancel any existing dismissal

        withAnimation(.spring()) { // 使用 spring 动画可能效果更好
            currentToast = type
        }

        let task = DispatchWorkItem {
            withAnimation(.spring()) {
                currentToast = nil
            }
        }
        self.dismissTask = task
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: task) // 3秒后消失
    }

    func body(content: Content) -> some View {
        content
            .environment(\.showToast, ShowToastAction(action: displayToast)) // Provide action to environment
            .overlay(alignment: .top) { // Using .overlay(alignment:content:) for modern SwiftUI
                if let toast = currentToast {
                    ToastView(type: toast)
                        .padding(.top, UIApplication.shared.connectedScenes
                                    .filter { $0.activationState == .foregroundActive }
                                    .compactMap { $0 as? UIWindowScene }
                                    .first?.windows
                                    .filter { $0.isKeyWindow }.first?.safeAreaInsets.top ?? 20) // 考虑安全区域
                        .transition(.asymmetric(
                            insertion: .move(edge: .top).combined(with: .opacity),
                            removal: .move(edge: .top).combined(with: .opacity) // 可以定义不同的出入动画
                        ))
                        .zIndex(1) //确保Toast在最上层
                }
            }
            .onReceive(GlobalToastPresenter.shared.showToastSubject) { toastType in
                displayToast(toastType) // Handle globally presented toasts
            }
    }
}

// MARK: - 6. View Extension for easy application

// 扩展View以提供withToast()修饰符
extension View {
    /// Applies a modifier to present toast notifications.
    ///
    /// To show a toast from a non-View context (e.g., a ViewModel or Service),
    /// use `GlobalToastPresenter.shared.show(type: .success("Your message"))`.
    ///
    /// To show a toast from within a View that has this modifier applied (or one of its ancestors),
    /// use `@Environment(\.showToast) var showToast; showToast(.info("View message"))`.
    ///
    /// This modifier should typically be applied once at a high level in your view hierarchy,
    /// for example, on your root view in your `App` struct or on a `NavigationView`/`TabView`.
    func withToast() -> some View {
        self.modifier(ToastModifier())  // 应用Toast修饰符
    }
}

