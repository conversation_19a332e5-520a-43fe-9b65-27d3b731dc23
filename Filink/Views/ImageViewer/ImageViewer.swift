import SwiftUI
import Photos

// MARK: - ImageViewerItem
/// 用于图片查看器的数据模型
struct ImageViewerItem: Identifiable {
    let id = UUID()
    let displayURL: URL // 用于显示的URL（可能是缩略图）
    let highResURL: URL // 用于打开或保存的高清图URL
    let altText: String?

    init(displayURL: URL, highResURL: URL? = nil, altText: String?) {
        self.displayURL = displayURL
        self.highResURL = highResURL ?? displayURL // 如果没有高清图URL，则回退到显示URL
        self.altText = altText
    }
}

// MARK: - CachedAsyncImage
struct CachedAsyncImage: View {
    let url: URL
    @State private var image: UIImage?
    @State private var isLoading = true

    var body: some View {
        Group {
            if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
            } else if isLoading {
                ProgressView()
            } else {
                // 加载失败时显示的占位符
                Image(systemName: "photo")
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(.gray)
            }
        }
        .onAppear(perform: loadImage)
    }

    private func loadImage() {
        Task {
            // 尝试从内存缓存中快速获取
            if let cachedImage = ImageManager.shared.getCachedImage(for: url) {
                self.image = cachedImage
                self.isLoading = false
                return
            }
            
            // 如果内存缓存未命中，则启动加载
            do {
                let loadedImage = try await ImageManager.shared.loadImage(from: url)
                self.image = loadedImage
            } catch {
                print("Error loading image for viewer: \(error)")
            }
            self.isLoading = false
        }
    }
}


// MARK: - ImageViewer
struct ImageViewer: View {
    // 图片项的数组
    let items: [ImageViewerItem]
    // 当前显示的图片索引
    @State private var currentIndex: Int
    // 控制视图是否显示
    @Binding var isPresented: Bool

    // 下拉关闭手势相关状态
    @State private var dragOffset: CGSize = .zero
    @State private var isDraggingToClose = false
    @State private var backgroundOpacity: Double = 1.0

    // 保存图片状态 - 将使用 GlobalToastPresenter

    // 初始时显示的图片索引
    private let initialIndex: Int
    
    init(items: [ImageViewerItem], selectedIndex: Int, isPresented: Binding<Bool>) {
        self.items = items
        self.initialIndex = selectedIndex
        self._currentIndex = State(initialValue: selectedIndex)
        self._isPresented = isPresented
    }

    // 下拉关闭手势的阈值
    private let dismissThreshold: CGFloat = 150
    private let maxDragDistance: CGFloat = 300
    
    var body: some View {
        ZStack {
            // 背景
            Color.black
                .opacity(backgroundOpacity)
                .edgesIgnoringSafeArea(.all)

            // 图片滑动视图
            TabView(selection: $currentIndex) {
                ForEach(items.indices, id: \.self) { index in
                    // 修改：在查看器中直接使用高清图URL
                    ZoomableImageView(url: items[index].highResURL, isPresented: $isPresented)
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .edgesIgnoringSafeArea(.all)
            .offset(y: dragOffset.height)
            .scaleEffect(1 - abs(dragOffset.height) / maxDragDistance * 0.2)
            
            // 顶部工具栏
            VStack(spacing: 0) {
                HStack {
                    // 左上角：返回按钮
                    Button(action: closeViewer) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.headline.weight(.bold))
                            Text("返回")
                        }
                        .foregroundColor(.white)
                        .padding()
                    }

                    Spacer()

                    // 中间：数量指示器
                    if !items.isEmpty {
                        Text("\(currentIndex + 1) / \(items.count)")
                            .foregroundColor(.white)
                            .font(.headline)
                    }

                    Spacer()

                    // 右上角按钮组
                    HStack(spacing: 25) { // 增加间距
                        // 在浏览器中打开按钮
                        Button(action: openInBrowser) {
                            Image(systemName: "safari")
                                .font(.title2) // 增大图标
                                .foregroundColor(.white)
                        }

                        // 保存按钮
                        Button(action: saveCurrentImage) {
                            Image(systemName: "square.and.arrow.down")
                                .font(.title2) // 增大图标
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.trailing)
                }
                .padding(.top, 40)
                .background(Color.black.opacity(0.3))

                Spacer()
            }
            .edgesIgnoringSafeArea(.top)
            .offset(y: dragOffset.height)
            .opacity(1 - abs(dragOffset.height) / maxDragDistance * 0.5)
            
            // 底部信息
            VStack {
                Spacer()

                // 从 altText 中提取的图片信息
                if currentIndex < items.count, let altText = items[currentIndex].altText, !altText.isEmpty {
                    Text(altText)
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(12)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(8)
                        .padding(.bottom, 40)
                }
                // TODO: 如果没有 altText，可以显示从解析器获取的其他图片信息
            }
            .offset(y: dragOffset.height)
            .opacity(1 - abs(dragOffset.height) / maxDragDistance * 0.5)

            // Toast 提示将由 .withToast() 修饰符处理
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    // 只允许向下拖拽
                    if value.translation.height > 0 {
                        dragOffset = value.translation
                        isDraggingToClose = true
                        // 根据拖拽距离调整背景透明度
                        backgroundOpacity = max(0.3, 1 - abs(value.translation.height) / maxDragDistance * 0.7)
                    }
                }
                .onEnded { value in
                    if value.translation.height > dismissThreshold {
                        // 达到关闭阈值，关闭查看器
                        closeViewer()
                    } else {
                        // 未达到阈值，恢复原位
                        withAnimation(.easeOut(duration: 0.3)) {
                            dragOffset = .zero
                            backgroundOpacity = 1.0
                        }
                    }
                    isDraggingToClose = false
                }
        )
        .onAppear {
            currentIndex = initialIndex
        }
        .withToast() // 在此应用 withToast 修饰符
    }
    
    private func closeViewer() {
        isPresented = false
    }

    private func openInBrowser() {
        guard currentIndex < items.count else { return }
        let url = items[currentIndex].highResURL
        UIApplication.shared.open(url)
    }
    
    private func saveCurrentImage() {
        guard currentIndex < items.count else { return }
        let currentURL = items[currentIndex].highResURL
        
        Task {
            do {
                let image = try await ImageManager.shared.loadImage(from: currentURL)
                
                // 请求相册访问权限
                PHPhotoLibrary.requestAuthorization { status in
                    if status == .authorized {
                        // 保存到相册
                        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
                        GlobalToastPresenter.shared.showSuccess("图片已保存")
                    } else {
                        GlobalToastPresenter.shared.showError("需要相册访问权限")
                    }
                }
            } catch {
                GlobalToastPresenter.shared.showError("保存失败")
                print("Error saving image: \(error)")
            }
        }
    }
}
