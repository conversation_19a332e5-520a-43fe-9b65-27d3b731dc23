import SwiftUI

struct ZoomableImageView: View {
    let url: URL
    @Binding var isPresented: Bool
    
    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var isDragging = false
    @GestureState private var isMagnifying = false

    var body: some View {
        let magnification = MagnificationGesture()
            .updating($isMagnifying) { _, state, _ in
                state = true
            }
            .onChanged { value in
                let delta = value / self.lastScale
                self.scale *= delta
                self.lastScale = value
            }
            .onEnded { _ in
                self.lastScale = 1.0
                if self.scale < 1.0 {
                    withAnimation {
                        self.scale = 1.0
                        self.offset = .zero
                        self.lastOffset = .zero
                    }
                }
            }

        let drag = DragGesture()
            .onChanged { value in
                self.isDragging = true
                // 只允许在放大状态下拖动
                if self.scale > 1 {
                    self.offset = CGSize(width: self.lastOffset.width + value.translation.width, height: self.lastOffset.height + value.translation.height)
                }
            }
            .onEnded { value in
                self.isDragging = false
                // 只在放大状态下保存拖动位置
                if self.scale > 1 {
                    self.lastOffset = self.offset
                }
            }

        let doubleTapToReset = TapGesture(count: 2)
            .onEnded {
                withAnimation {
                    self.scale = 1.0
                    self.offset = .zero
                    self.lastOffset = .zero
                }
            }

        let combinedDragAndMagnify = drag.simultaneously(with: magnification)

        CachedAsyncImage(url: url)
            .scaleEffect(scale)
            .offset(offset)
            .gesture(
                doubleTapToReset.exclusively(before: combinedDragAndMagnify)
            )
    }
}
