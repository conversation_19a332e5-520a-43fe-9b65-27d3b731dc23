import Foundation
import Combine
import UIKit

/// 自动保存服务
/// 负责监听编辑器内容变化并执行自动保存
@MainActor
final class AutoSaveService: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 保存状态
    @Published private(set) var saveStatus: SaveStatus = .idle
    
    /// 最后保存时间
    @Published private(set) var lastSaveTime: Date?
    
    /// 是否启用自动保存
    @Published var isAutoSaveEnabled: Bool = true
    
    // MARK: - Types
    
    enum SaveStatus {
        case idle           // 空闲状态
        case pending        // 等待保存
        case saving         // 正在保存
        case saved          // 保存成功
        case failed(Error)  // 保存失败
    }
    
    // MARK: - Private Properties
    
    private var saveTimer: Timer?
    private var draftManager: DraftManager?
    private var currentDraft: Draft?
    private var lastContent: NSAttributedString?
    private var cancellables = Set<AnyCancellable>()
    
    // 自动保存配置
    private let autoSaveDelay: TimeInterval = 3.0  // 3秒延迟
    private let minContentLength: Int = 5          // 最小内容长度
    private let maxRetryAttempts: Int = 3          // 最大重试次数
    
    // 重试相关
    private var retryCount: Int = 0
    private var retryTimer: Timer?
    
    // MARK: - Initialization
    
    init() {
        setupNotifications()
    }
    
    deinit {
        saveTimer?.invalidate()
        saveTimer = nil
        retryTimer?.invalidate()
        retryTimer = nil
    }
    
    // MARK: - Private Setup
    
    private func setupNotifications() {
        // 监听应用进入后台，立即保存
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.saveImmediately()
                }
            }
            .store(in: &cancellables)
        
        // 监听应用即将终止，立即保存
        NotificationCenter.default.publisher(for: UIApplication.willTerminateNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.saveImmediately()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 开始自动保存
    /// - Parameters:
    ///   - draftManager: 草稿管理器
    ///   - draft: 当前草稿（可选，如果为nil则创建新草稿）
    func startAutoSave(with draftManager: DraftManager, draft: Draft? = nil) {
        self.draftManager = draftManager
        self.currentDraft = draft
    }
    
    /// 停止自动保存
    func stopAutoSave() {
        saveTimer?.invalidate()
        saveTimer = nil
        saveStatus = .idle
    }
    
    /// 内容变化时调用
    /// - Parameter content: 新的内容
    func contentDidChange(_ content: NSAttributedString) {
        guard isAutoSaveEnabled else { return }
        guard shouldSave(content: content) else { return }
        
        // 取消之前的定时器
        saveTimer?.invalidate()
        
        // 更新状态
        saveStatus = .pending
        
        // 存储内容以供定时器使用
        lastContent = content
        
        // 设置新的定时器
        saveTimer = Timer.scheduledTimer(withTimeInterval: autoSaveDelay, repeats: false) { [weak self] _ in
            Task { @MainActor in
                guard let self = self, let contentToSave = self.lastContent else { return }
                self.performAutoSave(content: contentToSave)
            }
        }
    }
    
    /// 立即保存
    func saveImmediately() {
        guard let lastContent = lastContent else { return }
        
        saveTimer?.invalidate()
        saveTimer = nil
        
        performAutoSave(content: lastContent)
    }
    
    /// 手动保存
    /// - Parameter content: 要保存的内容
    func saveManually(_ content: NSAttributedString) {
        saveTimer?.invalidate()
        saveTimer = nil
        
        performAutoSave(content: content, isManualSave: true)
    }
    
    /// 创建新草稿
    /// - Parameter content: 初始内容
    func createNewDraft(with content: NSAttributedString) {
        guard let draftManager = draftManager else { return }
        
        let newDraft = draftManager.createDraft(from: content)
        currentDraft = newDraft
        lastContent = content
        

    }
    
    /// 切换到指定草稿
    /// - Parameter draft: 目标草稿
    func switchToDraft(_ draft: Draft) {
        // 如果当前有草稿且内容有变化，先保存
        if let currentContent = lastContent, shouldSave(content: currentContent) {
            saveImmediately()
        }
        
        currentDraft = draft
        draftManager?.currentDraft = draft
        
        print("【调试】AutoSaveService: 切换到草稿 - \(draft.title ?? "无标题")")
    }
    
    // MARK: - Private Methods
    
    /// 判断是否需要保存
    private func shouldSave(content: NSAttributedString) -> Bool {
        // 检查内容长度
        guard content.string.trimmingCharacters(in: .whitespacesAndNewlines).count >= minContentLength else {
            return false
        }
        
        // 检查内容是否有变化
        if let lastContent = lastContent {
            return !content.isEqual(to: lastContent)
        }
        
        return true
    }
    
    /// 执行自动保存
    private func performAutoSave(content: NSAttributedString, isManualSave: Bool = false) {
        guard let draftManager = draftManager else {
            saveStatus = .failed(AutoSaveError.noDraftManager)
            return
        }
        
        saveStatus = .saving
        
        if let currentDraft = currentDraft {
            // 更新现有草稿
            draftManager.saveDraft(currentDraft, content: content, isAutoSave: !isManualSave)
        } else {
            // 创建新草稿
            let newDraft = draftManager.createDraft(from: content)
            currentDraft = newDraft
            draftManager.currentDraft = newDraft
        }
        
        lastContent = content
        lastSaveTime = Date()
        saveStatus = .saved
        

        
        // 3秒后重置状态为idle
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            if case .saved = self.saveStatus {
                self.saveStatus = .idle
            }
        }
        
        // 注意：由于我们移除了do-catch块，这里不再需要错误处理
        // 如果需要错误处理，应该在DraftManager的方法中处理
    }
}

// MARK: - AutoSaveError

enum AutoSaveError: LocalizedError {
    case noDraftManager
    case contentTooShort
    case noContentChange
    
    var errorDescription: String? {
        switch self {
        case .noDraftManager:
            return "草稿管理器未初始化"
        case .contentTooShort:
            return "内容太短，无需保存"
        case .noContentChange:
            return "内容未发生变化"
        }
    }
}

// MARK: - AutoSaveService Extensions

extension AutoSaveService {
    /// 获取保存状态的显示文本
    var saveStatusText: String {
        switch saveStatus {
        case .idle:
            return ""
        case .pending:
            return "等待保存..."
        case .saving:
            return "正在保存..."
        case .saved:
            if let lastSaveTime = lastSaveTime {
                return "已保存 \(lastSaveTime.relativeTime())"
            } else {
                return "已保存"
            }
        case .failed(let error):
            return "保存失败: \(error.localizedDescription)"
        }
    }
    
    /// 获取保存状态的颜色
    var saveStatusColor: UIColor {
        switch saveStatus {
        case .idle:
            return .systemGray
        case .pending:
            return .systemOrange
        case .saving:
            return .systemBlue
        case .saved:
            return .systemGreen
        case .failed:
            return .systemRed
        }
    }
}
