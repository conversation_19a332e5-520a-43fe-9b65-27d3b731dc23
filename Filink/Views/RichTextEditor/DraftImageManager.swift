import UIKit
import Foundation

/// 草稿图片管理器
/// 专门处理草稿中的图片存储和管理
class DraftImageManager {
    static let shared = DraftImageManager()
    
    private let fileManager = FileManager.default
    private let draftImagesDirectory: URL
    
    private init() {
        // 创建草稿图片专用目录
        if let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            draftImagesDirectory = documentsDirectory.appendingPathComponent("DraftImages")
            print("【调试】DraftImageManager: 草稿图片目录: \(draftImagesDirectory.path)")
            
            // 如果目录不存在，则创建它
            if !fileManager.fileExists(atPath: draftImagesDirectory.path) {
                do {
                    try fileManager.createDirectory(at: draftImagesDirectory, withIntermediateDirectories: true, attributes: nil)
                    print("【调试】DraftImageManager: 成功创建草稿图片目录")
                } catch {
                    print("【错误】DraftImageManager: 创建草稿图片目录失败: \(error)")
                }
            }
        } else {
            fatalError("无法找到Documents目录")
        }
    }
    
    /// 保存图片到草稿目录
    /// - Parameter image: 要保存的图片
    /// - Returns: 本地文件路径，如果保存失败返回nil
    func saveDraftImage(_ image: UIImage) -> String? {
        // 生成唯一的文件名
        let fileName = "\(UUID().uuidString).jpg"
        let fileURL = draftImagesDirectory.appendingPathComponent(fileName)
        
        // 将图片转换为JPEG数据（压缩质量0.8）
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            return nil
        }

        do {
            try imageData.write(to: fileURL)
            return fileURL.path
        } catch {
            return nil
        }
    }
    
    /// 从本地路径加载图片
    /// - Parameter path: 本地文件路径
    /// - Returns: 加载的图片，如果失败返回nil
    func loadDraftImage(from path: String) -> UIImage? {
        let fileURL = URL(fileURLWithPath: path)
        
        guard fileManager.fileExists(atPath: path) else {
            return nil
        }

        do {
            let imageData = try Data(contentsOf: fileURL)
            return UIImage(data: imageData)
        } catch {
            return nil
        }
    }
    
    /// 删除草稿图片
    /// - Parameter path: 本地文件路径
    func deleteDraftImage(at path: String) {
        let fileURL = URL(fileURLWithPath: path)
        
        guard fileManager.fileExists(atPath: path) else {
            return // 文件不存在，无需删除
        }
        
        do {
            try fileManager.removeItem(at: fileURL)
        } catch {
            // 删除失败，静默处理
        }
    }
    
    /// 从HTML内容中提取所有本地图片路径
    /// - Parameter htmlContent: HTML内容
    /// - Returns: 本地图片路径数组
    func extractLocalImagePaths(from htmlContent: String) -> [String] {
        var imagePaths: [String] = []
        
        // 使用正则表达式匹配img标签中的src属性
        let pattern = #"<img[^>]+src\s*=\s*["\']([^"\']+)["\'][^>]*>"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            let matches = regex.matches(in: htmlContent, options: [], range: NSRange(location: 0, length: htmlContent.count))
            
            for match in matches {
                if match.numberOfRanges > 1 {
                    let srcRange = match.range(at: 1)
                    if let range = Range(srcRange, in: htmlContent) {
                        let srcValue = String(htmlContent[range])
                        
                        // 只处理本地文件路径（以/开头或file://开头）
                        if srcValue.hasPrefix("/") || srcValue.hasPrefix("file://") {
                            let cleanPath = srcValue.replacingOccurrences(of: "file://", with: "")
                            imagePaths.append(cleanPath)
                        }
                    }
                }
            }
        } catch {
            print("【错误】DraftImageManager: 提取图片路径失败: \(error)")
        }
        
        return imagePaths
    }
    
    /// 清理草稿删除后的孤立图片
    /// - Parameter usedPaths: 仍在使用的图片路径集合
    func cleanupOrphanedImages(usedPaths: Set<String>) {
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: draftImagesDirectory, includingPropertiesForKeys: nil, options: [])
            
            for fileURL in fileURLs {
                let filePath = fileURL.path
                
                // 如果图片不在使用列表中，删除它
                if !usedPaths.contains(filePath) {
                    try fileManager.removeItem(at: fileURL)
                    print("【调试】DraftImageManager: 清理孤立图片: \(fileURL.lastPathComponent)")
                }
            }
        } catch {
            print("【错误】DraftImageManager: 清理孤立图片失败: \(error)")
        }
    }
    
    /// 计算草稿图片缓存的总大小
    /// - Returns: 缓存大小（MB）
    func calculateCacheSize() -> Double {
        var totalSize: Double = 0.0
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: draftImagesDirectory, includingPropertiesForKeys: [.fileSizeKey], options: [])
            
            for fileURL in fileURLs {
                let attributes = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = attributes.fileSize {
                    totalSize += Double(fileSize)
                }
            }
        } catch {
            print("【错误】DraftImageManager: 计算草稿图片缓存大小失败: \(error)")
        }
        
        return totalSize / (1024 * 1024) // 转换为MB
    }
    
    /// 清空所有草稿图片
    func clearAllDraftImages() {
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: draftImagesDirectory, includingPropertiesForKeys: nil, options: [])
            
            for fileURL in fileURLs {
                try fileManager.removeItem(at: fileURL)
            }
            
            print("【调试】DraftImageManager: 成功清空所有草稿图片")
        } catch {
            print("【错误】DraftImageManager: 清空草稿图片失败: \(error)")
        }
    }
    
    /// 将图片路径转换为HTML img标签
    /// - Parameter imagePath: 本地图片路径
    /// - Returns: HTML img标签字符串
    func createImageHTMLTag(for imagePath: String) -> String {
        // 加载图片获取尺寸信息
        if let image = loadDraftImage(from: imagePath) {
            // 计算80%宽度的显示尺寸
            let maxWidth: CGFloat = 300 // 默认最大宽度
            let targetWidth = maxWidth * 0.8
            let aspectRatio = image.size.height / image.size.width
            let targetHeight = targetWidth * aspectRatio

            return "<p><img src=\"file://\(imagePath)\" width=\"\(Int(targetWidth))\" height=\"\(Int(targetHeight))\" style=\"max-width: 80%; height: auto; display: block; margin: 0 auto;\" /></p>"
        } else {
            return "<p><img src=\"file://\(imagePath)\" style=\"width: 80%; max-width: 80%; height: auto; display: block; margin: 0 auto;\" /></p>"
        }
    }
    
    /// 检查图片文件是否存在
    /// - Parameter path: 图片路径
    /// - Returns: 是否存在
    func imageExists(at path: String) -> Bool {
        return fileManager.fileExists(atPath: path)
    }
}
