import SwiftUI

/// 草稿列表视图
/// 显示所有保存的草稿，支持搜索、删除和恢复功能
struct DraftListView: View {
    
    // MARK: - Properties
    
    /// 草稿管理器
    @ObservedObject var draftManager: DraftManager
    
    /// 恢复草稿的回调
    let onRestoreDraft: (Draft) -> Void
    
    /// 搜索文本
    @State private var searchText = ""
    
    /// 显示删除确认对话框
    @State private var showingDeleteAlert = false
    
    /// 要删除的草稿
    @State private var draftToDelete: Draft?
    
    /// 编辑模式
    @State private var editMode: EditMode = .inactive
    
    /// 选中的草稿（用于批量操作）
    @State private var selectedDrafts = Set<Draft>()

    /// 显示清空所有草稿的确认对话框
    @State private var showingClearAllAlert = false

    /// 显示导出选项
    @State private var showingExportOptions = false
    
    // MARK: - Computed Properties
    
    /// 过滤后的草稿列表
    private var filteredDrafts: [Draft] {
        if searchText.isEmpty {
            return draftManager.drafts
        } else {
            return draftManager.searchDrafts(keyword: searchText)
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack {
                if draftManager.isLoading {
                    // 加载状态
                    ProgressView("加载草稿中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if filteredDrafts.isEmpty {
                    // 空状态
                    emptyStateView
                } else {
                    // 草稿列表
                    draftListView
                }
            }
            .navigationTitle("草稿")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if editMode == .active {
                        Button("取消") {
                            editMode = .inactive
                            selectedDrafts.removeAll()
                        }
                    } else {
                        Button("编辑") {
                            editMode = .active
                        }
                        .disabled(filteredDrafts.isEmpty)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if editMode == .active {
                        Menu {
                            Button("删除选中", role: .destructive) {
                                deleteSelectedDrafts()
                            }
                            .disabled(selectedDrafts.isEmpty)
                            
                            Button("导出选中") {
                                showingExportOptions = true
                            }
                            .disabled(selectedDrafts.isEmpty)
                        } label: {
                            Text("操作")
                        }
                    } else {
                        Button("清空") {
                            showingClearAllAlert = true
                        }
                        .disabled(filteredDrafts.isEmpty)
                        .foregroundColor(.red)
                    }
                }
            }
            .searchable(text: $searchText, prompt: "搜索草稿...")
            .alert("删除草稿", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    if let draft = draftToDelete {
                        draftManager.deleteDraft(draft)
                        draftToDelete = nil
                    }
                }
            } message: {
                Text("确定要删除这个草稿吗？此操作无法撤销。")
            }
            .alert("清空所有草稿", isPresented: $showingClearAllAlert) {
                Button("取消", role: .cancel) { }
                Button("清空", role: .destructive) {
                    draftManager.clearAllDrafts()
                    editMode = .inactive
                    selectedDrafts.removeAll()
                }
            } message: {
                Text("确定要清空所有草稿吗？此操作无法撤销，包括所有相关的图片文件。")
            }
            .sheet(isPresented: $showingExportOptions) {
                ExportOptionsView(
                    selectedDrafts: Array(selectedDrafts),
                    draftManager: draftManager
                )
            }
        }
    }
    
    // MARK: - Subviews
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text(searchText.isEmpty ? "还没有草稿" : "没有找到匹配的草稿")
                .font(.title2)
                .foregroundColor(.gray)
            
            if searchText.isEmpty {
                Text("开始写作时会自动创建草稿")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 草稿列表视图
    private var draftListView: some View {
        List {
            ForEach(filteredDrafts, id: \.id) { draft in
                DraftRowView(
                    draft: draft,
                    isSelected: selectedDrafts.contains(draft),
                    editMode: editMode
                ) {
                    // 点击恢复草稿
                    onRestoreDraft(draft)
                } onToggleSelection: {
                    // 切换选中状态
                    if selectedDrafts.contains(draft) {
                        selectedDrafts.remove(draft)
                    } else {
                        selectedDrafts.insert(draft)
                    }
                } onDelete: {
                    // 删除单个草稿
                    draftToDelete = draft
                    showingDeleteAlert = true
                }
            }
            .onDelete(perform: deleteDraftsAtOffsets)
        }
        .environment(\.editMode, $editMode)
    }
    
    // MARK: - Private Methods
    
    /// 删除选中的草稿
    private func deleteSelectedDrafts() {
        let draftsToDelete = Array(selectedDrafts)
        draftManager.deleteDrafts(draftsToDelete)
        selectedDrafts.removeAll()
        editMode = .inactive
    }
    
    /// 左滑删除草稿
    private func deleteDraftsAtOffsets(_ offsets: IndexSet) {
        let draftsToDelete = offsets.map { filteredDrafts[$0] }
        draftManager.deleteDrafts(draftsToDelete)
    }
}

// MARK: - DraftRowView

/// 草稿行视图
struct DraftRowView: View {
    let draft: Draft
    let isSelected: Bool
    let editMode: EditMode
    let onRestore: () -> Void
    let onToggleSelection: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack {
            // 选择指示器（编辑模式下显示）
            if editMode == .active {
                Button(action: onToggleSelection) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .blue : .gray)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 草稿内容
            VStack(alignment: .leading, spacing: 8) {
                // 标题
                Text(draft.title ?? "无标题草稿")
                    .font(.headline)
                    .lineLimit(1)
                
                // 预览内容
                if !draft.previewText.isEmpty {
                    Text(draft.previewText)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                // 底部信息
                HStack {
                    // 字数
                    Text("\(draft.wordCount)字")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    // 自动保存标识
                    if draft.isAutoSaved {
                        Text("自动保存")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    
                    // 更新时间
                    Text(draft.relativeTimeString)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 操作按钮（非编辑模式下显示）
            if editMode == .inactive {
                Menu {
                    Button("恢复编辑", action: onRestore)
                    Button("删除", role: .destructive, action: onDelete)
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.gray)
                }
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if editMode == .active {
                onToggleSelection()
            } else {
                onRestore()
            }
        }
    }
}

// MARK: - ExportOptionsView

/// 导出选项视图
struct ExportOptionsView: View {
    let selectedDrafts: [Draft]
    let draftManager: DraftManager

    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            List {
                Section("导出格式") {
                    Button("导出为纯文本") {
                        exportAsText()
                    }

                    Button("导出为HTML") {
                        exportAsHTML()
                    }
                }

                Section("导出信息") {
                    Text("选中草稿数量: \(selectedDrafts.count)")
                    Text("总字数: \(selectedDrafts.reduce(0) { $0 + Int($1.wordCount) })")
                }
            }
            .navigationTitle("导出草稿")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }

    private func exportAsText() {
        let combinedText = selectedDrafts.map { draftManager.exportDraftAsText($0) }.joined(separator: "\n\n---\n\n")
        shareText(combinedText)
    }

    private func exportAsHTML() {
        let combinedHTML = selectedDrafts.map { draftManager.exportDraftAsHTML($0) }.joined(separator: "<hr>")
        shareText(combinedHTML)
    }

    private func shareText(_ text: String) {
        let activityVC = UIActivityViewController(activityItems: [text], applicationActivities: nil)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }

        presentationMode.wrappedValue.dismiss()
    }
}
