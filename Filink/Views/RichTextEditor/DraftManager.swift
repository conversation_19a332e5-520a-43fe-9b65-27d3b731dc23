import Foundation
import CoreData
import Combine
import UIKit

/// 草稿管理器
/// 负责草稿的创建、保存、删除、搜索等操作
@MainActor
final class DraftManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 所有草稿列表
    @Published private(set) var drafts: [Draft] = []
    
    /// 当前正在编辑的草稿
    @Published var currentDraft: Draft?
    
    /// 是否正在加载
    @Published private(set) var isLoading = false
    
    /// 错误信息
    @Published private(set) var error: Error?
    
    // MARK: - Private Properties

    private let viewContext: NSManagedObjectContext
    let imageManager = DraftImageManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(viewContext: NSManagedObjectContext = CoreDataManager.shared.container.viewContext) {
        self.viewContext = viewContext
        setupNotifications()
        fetchDrafts()
    }
    
    // MARK: - Private Setup
    
    private func setupNotifications() {
        // 监听Core Data变化
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.fetchDrafts()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 获取所有草稿
    func fetchDrafts() {
        isLoading = true
        error = nil
        
        let request: NSFetchRequest<Draft> = Draft.fetchRequest()
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Draft.updatedAt, ascending: false)
        ]
        
        do {
            drafts = try viewContext.fetch(request)
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    /// 创建新草稿
    /// - Parameters:
    ///   - attributedText: 富文本内容
    ///   - title: 草稿标题，如果为空则自动生成
    /// - Returns: 创建的草稿对象
    func createDraft(from attributedText: NSAttributedString, title: String? = nil) -> Draft {
        let draft = Draft(context: viewContext)
        draft.id = UUID()
        draft.createdAt = Date()
        draft.updatedAt = Date()
        draft.isAutoSaved = false
        
        updateDraftContent(draft, with: attributedText, title: title)
        
        do {
            try viewContext.save()
        } catch {
            self.error = error
        }
        
        return draft
    }
    
    /// 保存草稿
    /// - Parameters:
    ///   - draft: 要保存的草稿
    ///   - attributedText: 富文本内容
    ///   - title: 草稿标题
    ///   - isAutoSave: 是否为自动保存
    func saveDraft(_ draft: Draft, content attributedText: NSAttributedString, title: String? = nil, isAutoSave: Bool = false) {
        updateDraftContent(draft, with: attributedText, title: title)
        draft.updatedAt = Date()
        draft.isAutoSaved = isAutoSave
        
        do {
            try viewContext.save()
        } catch {
            self.error = error
        }
    }
    
    /// 删除草稿
    /// - Parameter draft: 要删除的草稿
    func deleteDraft(_ draft: Draft) {
        // 删除草稿关联的图片
        if let htmlContent = draft.htmlContent {
            let imagePaths = imageManager.extractLocalImagePaths(from: htmlContent)
            for imagePath in imagePaths {
                imageManager.deleteDraftImage(at: imagePath)
            }
        }
        
        viewContext.delete(draft)
        
        // 如果删除的是当前草稿，清除引用
        if currentDraft == draft {
            currentDraft = nil
        }
        
        do {
            try viewContext.save()
        } catch {
            self.error = error
        }
    }
    
    /// 批量删除草稿
    /// - Parameter drafts: 要删除的草稿数组
    func deleteDrafts(_ draftsToDelete: [Draft]) {
        for draft in draftsToDelete {
            // 删除草稿关联的图片
            if let htmlContent = draft.htmlContent {
                let imagePaths = imageManager.extractLocalImagePaths(from: htmlContent)
                for imagePath in imagePaths {
                    imageManager.deleteDraftImage(at: imagePath)
                }
            }
            
            viewContext.delete(draft)
            
            // 如果删除的包含当前草稿，清除引用
            if currentDraft == draft {
                currentDraft = nil
            }
        }
        
        do {
            try viewContext.save()
        } catch {
            self.error = error
        }
    }
    
    /// 搜索草稿
    /// - Parameter keyword: 搜索关键词
    /// - Returns: 匹配的草稿数组
    func searchDrafts(keyword: String) -> [Draft] {
        guard !keyword.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return drafts
        }
        
        let request: NSFetchRequest<Draft> = Draft.fetchRequest()
        request.predicate = NSPredicate(format: "title CONTAINS[cd] %@ OR plainTextContent CONTAINS[cd] %@", keyword, keyword)
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Draft.updatedAt, ascending: false)
        ]
        
        do {
            let results = try viewContext.fetch(request)
            return results
        } catch {
            self.error = error
            return []
        }
    }
    
    /// 获取草稿的富文本内容
    /// - Parameter draft: 草稿对象
    /// - Returns: 从HTML恢复的NSAttributedString
    func getAttributedContent(from draft: Draft) -> NSAttributedString {
        guard let htmlContent = draft.htmlContent else {
            return NSAttributedString(string: draft.title ?? "")
        }
        
        return NSAttributedString.fromDraftHTML(htmlContent)
    }
    
    /// 保存图片到草稿并返回HTML标签
    /// - Parameter image: 要保存的图片
    /// - Returns: HTML img标签，如果保存失败返回nil
    func saveDraftImage(_ image: UIImage) -> String? {
        guard let imagePath = imageManager.saveDraftImage(image) else {
            return nil
        }
        
        return imageManager.createImageHTMLTag(for: imagePath)
    }
    
    /// 清理孤立的草稿图片
    func cleanupOrphanedImages() {
        // 收集所有草稿中使用的图片路径
        var usedImagePaths = Set<String>()
        
        for draft in drafts {
            if let htmlContent = draft.htmlContent {
                let imagePaths = imageManager.extractLocalImagePaths(from: htmlContent)
                usedImagePaths.formUnion(imagePaths)
            }
        }
        
        // 清理未使用的图片
        imageManager.cleanupOrphanedImages(usedPaths: usedImagePaths)
    }
    
    /// 获取草稿缓存大小
    func getDraftCacheSize() -> Double {
        return imageManager.calculateCacheSize()
    }
    
    /// 清空所有草稿和图片
    func clearAllDrafts() {
        // 删除所有草稿
        for draft in drafts {
            viewContext.delete(draft)
        }
        
        // 清空所有草稿图片
        imageManager.clearAllDraftImages()
        
        // 重置当前草稿
        currentDraft = nil
        
        do {
            try viewContext.save()
        } catch {
            self.error = error
        }
    }
    
    /// 导出草稿为文本
    /// - Parameter draft: 要导出的草稿
    /// - Returns: 纯文本内容
    func exportDraftAsText(_ draft: Draft) -> String {
        return draft.plainTextContent ?? draft.title ?? ""
    }
    
    /// 导出草稿为HTML
    /// - Parameter draft: 要导出的草稿
    /// - Returns: HTML内容
    func exportDraftAsHTML(_ draft: Draft) -> String {
        return draft.htmlContent ?? ""
    }
    
    // MARK: - Private Helper Methods
    
    /// 更新草稿内容
    private func updateDraftContent(_ draft: Draft, with attributedText: NSAttributedString, title: String?) {
        // 设置标题
        if let title = title, !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            draft.title = title
        } else {
            // 自动生成标题（取前20个字符）
            let previewText = attributedText.previewText(maxLength: 20)
            draft.title = previewText.isEmpty ? "无标题草稿" : previewText
        }
        
        // 转换为HTML内容
        draft.htmlContent = attributedText.toDraftHTML()
        
        // 保存纯文本内容用于搜索
        draft.plainTextContent = attributedText.plainText
        
        // 更新字数
        draft.wordCount = Int32(attributedText.wordCount)
    }
}

// MARK: - Draft Entity Extension

extension Draft {
    /// 获取相对时间字符串
    var relativeTimeString: String {
        guard let updatedAt = updatedAt else { return "未知时间" }
        return updatedAt.relativeTime()
    }
    
    /// 获取预览文本
    var previewText: String {
        if let plainTextContent = plainTextContent, !plainTextContent.isEmpty {
            return plainTextContent.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return title ?? ""
    }
}
