import SwiftUI

/// 草稿设置视图
/// 管理草稿相关的设置选项
struct DraftSettingsView: View {
    
    @ObservedObject var settingsManager: SettingsManager
    @ObservedObject var draftManager: DraftManager
    
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingClearCacheAlert = false
    @State private var cacheSize: Double = 0.0
    
    var body: some View {
        NavigationView {
            List {
                // 自动保存设置
                Section("自动保存") {
                    Toggle("启用自动保存", isOn: $settingsManager.enableAutoSave)
                    
                    if settingsManager.enableAutoSave {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("自动保存延迟: \(Int(settingsManager.autoSaveDelay)) 秒")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Slider(
                                value: $settingsManager.autoSaveDelay,
                                in: 1...10,
                                step: 1
                            ) {
                                Text("延迟时间")
                            }
                        }
                    }
                }
                
                // 显示设置
                Section("显示选项") {
                    Toggle("显示字数统计", isOn: $settingsManager.showWordCount)
                }
                
                // 草稿管理
                Section("草稿管理") {
                    HStack {
                        Text("草稿数量")
                        Spacer()
                        Text("\(draftManager.drafts.count)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("缓存大小")
                        Spacer()
                        Text(formatCacheSize(cacheSize))
                            .foregroundColor(.secondary)
                    }
                    
                    Button("清理缓存") {
                        showingClearCacheAlert = true
                    }
                    .foregroundColor(.red)
                }
                
                // 导入导出
                Section("数据管理") {
                    Button("导出所有草稿") {
                        exportAllDrafts()
                    }
                    
                    Button("清空所有草稿") {
                        clearAllDrafts()
                    }
                    .foregroundColor(.red)
                }
                
                // 帮助信息
                Section("帮助") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("关于自动保存")
                            .font(.headline)
                        
                        Text("• 自动保存会在您停止输入后的指定时间自动保存草稿")
                        Text("• 图片会保存在本地沙盒目录中")
                        Text("• 草稿支持富文本格式，包括粗体、斜体、链接等")
                        Text("• 清理缓存会删除未使用的图片文件")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
            .navigationTitle("草稿设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .onAppear {
                updateCacheSize()
            }
            .alert("清理缓存", isPresented: $showingClearCacheAlert) {
                Button("取消", role: .cancel) { }
                Button("清理", role: .destructive) {
                    draftManager.cleanupOrphanedImages()
                    updateCacheSize()
                }
            } message: {
                Text("这将删除所有未使用的图片文件，但不会影响草稿内容。")
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func updateCacheSize() {
        cacheSize = draftManager.getDraftCacheSize()
    }
    
    private func formatCacheSize(_ size: Double) -> String {
        if size < 1.0 {
            return String(format: "%.1f KB", size * 1024)
        } else if size < 1024.0 {
            return String(format: "%.1f MB", size)
        } else {
            return String(format: "%.1f GB", size / 1024)
        }
    }
    
    private func exportAllDrafts() {
        let allDrafts = draftManager.drafts
        guard !allDrafts.isEmpty else { return }
        
        let combinedText = allDrafts.map { draft in
            let title = draft.title ?? "无标题草稿"
            let content = draftManager.exportDraftAsText(draft)
            let date = draft.updatedAt?.formatted() ?? "未知时间"
            
            return """
            标题: \(title)
            更新时间: \(date)
            字数: \(draft.wordCount)
            
            \(content)
            """
        }.joined(separator: "\n\n" + String(repeating: "=", count: 50) + "\n\n")
        
        shareText(combinedText)
    }
    
    private func clearAllDrafts() {
        draftManager.clearAllDrafts()
        updateCacheSize()
    }
    
    private func shareText(_ text: String) {
        let activityVC = UIActivityViewController(activityItems: [text], applicationActivities: nil)
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

// MARK: - Previews

struct DraftSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        DraftSettingsView(
            settingsManager: SettingsManager(),
            draftManager: DraftManager()
        )
    }
}
