import Foundation
import UIKit

/// HTML 与 NSAttributedString 转换工具
enum HTMLConverter {
    
    /// 将 NSAttributedString 转换为结构化的 HTML 字符串
    /// - Parameter attributedString: 要转换的富文本
    /// - Returns: 转换后的 HTML 字符串
    static func toHTML(from attributedString: NSAttributedString) -> String? {
        var htmlString = ""
        let range = NSRange(location: 0, length: attributedString.length)
        
        attributedString.enumerateAttributes(in: range, options: []) { attributes, range, _ in
            // 检查是否是图片附件
            if let attachment = attributes[.attachment] as? NSTextAttachment,
               let image = attachment.image {
                // 处理图片附件
                let imageHTML = processImageAttachment(attachment, image: image)
                htmlString += imageHTML
                return
            }

            var text = attributedString.attributedSubstring(from: range).string
            var isBlockquote = false

            // --- 检查并处理 Markdown 块引用标记 ---
            if let firstChar = text.trimmingCharacters(in: .whitespacesAndNewlines).first, firstChar == ">" {
                isBlockquote = true
                // 从文本中移除 `>` 标记及其后的空格
                if let firstRealCharIndex = text.firstIndex(of: ">") {
                    let afterMarkerIndex = text.index(after: firstRealCharIndex)
                    if afterMarkerIndex < text.endIndex && text[afterMarkerIndex] == " " {
                        text.removeSubrange(firstRealCharIndex...afterMarkerIndex)
                    } else {
                        text.remove(at: firstRealCharIndex)
                    }
                }
            }
            
            // --- 处理剧透标签 ---
            var content = text.replacingOccurrences(of: "\n", with: "<br />")

            // 检查并处理 [spoiler][/spoiler] 标签
            let spoilerPattern = "\\[spoiler\\](.*?)\\[/spoiler\\]"
            if let regex = try? NSRegularExpression(pattern: spoilerPattern, options: .dotMatchesLineSeparators) {
                let range = NSRange(location: 0, length: content.count)
                content = regex.stringByReplacingMatches(in: content, options: [], range: range, withTemplate: "<span class=\"spoiler\">$1</span>")
            }

            var styles = [String]()

            // --- 提取样式 ---
            if let font = attributes[.font] as? UIFont {
                // 提取字号
                styles.append("font-size: \(font.pointSize)px;")

                // 检查粗体和斜体
                let isBold = font.fontDescriptor.symbolicTraits.contains(.traitBold)
                let isItalic = font.fontDescriptor.symbolicTraits.contains(.traitItalic)

                if isBold {
                    content = "<b>\(content)</b>"
                }
                if isItalic {
                    content = "<i>\(content)</i>"
                }
            }
            
            if let color = attributes[.foregroundColor] as? UIColor {
                // 提取颜色
                var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
                color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
                styles.append("color: rgba(\(Int(red * 255)), \(Int(green * 255)), \(Int(blue * 255)), \(alpha));")
            }

            // --- 应用标签 ---
            if attributes[.underlineStyle] != nil {
                content = "<u>\(content)</u>"
            }
            
            if let link = attributes[.link] as? URL {
                content = "<a href=\"\(link.absoluteString)\">\(content)</a>"
            }
            
            // --- 构建最终的 HTML ---
            let styleAttribute = styles.isEmpty ? "" : " style=\"\(styles.joined(separator: " "))\""
            
            // 如果内容没有被其他标签包裹，则用带样式的 <p> 或 <span> 包裹
            var styledContent: String
            if !content.starts(with: "<") && !content.isEmpty {
                if content.contains("<br />") {
                    // 如果包含换行，使用 p 标签
                    styledContent = "<p\(styleAttribute)>\(content)</p>"
                } else {
                    // 否则，使用 span 标签
                    styledContent = "<span\(styleAttribute)>\(content)</span>"
                }
            } else {
                // 如果已经被 a, b, i, u 等标签包裹，则需要找到合适的位置插入样式
                // 这是一个简化的处理，对于复杂嵌套可能不完美
                if let firstTagEnd = content.firstIndex(of: ">") {
                    var modifiedContent = content
                    // 在第一个标签内部注入样式
                    if let styleAttrRange = content.range(of: "style=\"") {
                        // 如果已有 style 属性，则追加
                        let insertStylePosition = content.index(styleAttrRange.upperBound, offsetBy: 0)
                        modifiedContent.insert(contentsOf: styles.joined(separator: " ") + " ", at: insertStylePosition)
                        styledContent = modifiedContent
                    } else if !styles.isEmpty {
                        // 否则，插入新的 style 属性
                        modifiedContent.insert(contentsOf: " " + styleAttribute, at: content.index(before: firstTagEnd))
                        styledContent = modifiedContent
                    } else {
                        styledContent = content
                    }
                } else {
                    styledContent = content
                }
            }
            
            // 如果是块引用，则用 <blockquote> 包裹最终的样式化内容
            if isBlockquote {
                // 确保即使内容为空，也生成一个有效的 blockquote 标签
                htmlString += "<blockquote>\(styledContent.isEmpty ? "<br />" : styledContent)</blockquote>"
            } else {
                htmlString += styledContent
            }
        }
        
        if htmlString.isEmpty && attributedString.length > 0 {
            let font = attributedString.attribute(.font, at: 0, effectiveRange: nil) as? UIFont ?? .systemFont(ofSize: 16)
            return "<p style=\"font-size: \(font.pointSize)px;\">\(attributedString.string.replacingOccurrences(of: "\n", with: "<br />"))</p>"
        }
        
        return htmlString
    }

    /// 处理图片附件，转换为HTML
    private static func processImageAttachment(_ attachment: NSTextAttachment, image: UIImage) -> String {
        // 将图片转换为base64编码
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            return "<p>[图片]</p>"
        }

        let base64String = imageData.base64EncodedString()
        let mimeType = "image/jpeg"
        let dataURL = "data:\(mimeType);base64,\(base64String)"

        // 获取图片尺寸
        let size = attachment.bounds.size
        let width = size.width
        let height = size.height

        // 生成HTML img标签
        return "<p><img src=\"\(dataURL)\" width=\"\(width)\" height=\"\(height)\" style=\"width: 80%; max-width: 80%; height: auto; display: block; margin: 0 auto;\" /></p>"
    }

    /// 将 HTML 字符串转换为 NSAttributedString
    /// - Parameter html: 要转换的 HTML 字符串
    /// - Returns: 转换后的富文本，如果失败则返回 nil
    static func fromHTML(_ html: String) -> NSAttributedString? {
        // 预处理：将 <u> 标签替换为带 `text-decoration: underline` 样式的 <span>，
        // 因为系统的 HTML 解析器对 CSS 样式的支持比对 `<u>` 标签的支持更可靠。
        var processedHtml = html.replacingOccurrences(of: "<u>", with: "<span style=\"text-decoration: underline;\">", options: .caseInsensitive)
        processedHtml = processedHtml.replacingOccurrences(of: "</u>", with: "</span>", options: .caseInsensitive)

        // 为了让解析器能正确工作，我们需要一个根元素
        let wrappedHtml = "<body>\(processedHtml)</body>"
        guard let data = wrappedHtml.data(using: .utf8) else {
            return nil
        }
        
        do {
            let attributedString = try NSAttributedString(
                data: data,
                options: [.documentType: NSAttributedString.DocumentType.html, .characterEncoding: String.Encoding.utf8.rawValue],
                documentAttributes: nil
            )
            
            // 转换后，系统有时会在末尾添加一个多余的换行符，我们需要移除它
            let mutableString = NSMutableAttributedString(attributedString: attributedString)
            if mutableString.string.hasSuffix("\n") {
                mutableString.deleteCharacters(in: NSRange(location: mutableString.length - 1, length: 1))
            }
            
            return mutableString
            
        } catch {
            return nil
        }
    }
}
