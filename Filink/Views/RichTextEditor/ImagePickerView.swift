import SwiftUI
import AVFoundation

/// 相机可用性检查工具
struct CameraHelper {
    /// 检查相机是否真正可用
    static func isCameraReallyAvailable() -> Bool {
        // 检查硬件可用性
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            return false
        }

        // 检查后置摄像头
        guard UIImagePickerController.isCameraDeviceAvailable(.rear) ||
              UIImagePickerController.isCameraDeviceAvailable(.front) else {
            return false
        }

        // 检查权限状态
        let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
        switch authStatus {
        case .authorized:
            return true
        case .notDetermined:
            return true // 系统会自动请求权限
        case .denied, .restricted:
            return false
        @unknown default:
            return false
        }
    }
}

/// 图片选择器视图
struct ImagePickerView: UIViewControllerRepresentable {
    let sourceType: UIImagePickerController.SourceType
    let onImageSelected: (UIImage) -> Void
    @Environment(\.presentationMode) var presentationMode

    init(sourceType: UIImagePickerController.SourceType = .photoLibrary, onImageSelected: @escaping (UIImage) -> Void) {
        self.sourceType = sourceType
        self.onImageSelected = onImageSelected
    }

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator

        // 验证sourceType是否可用
        if sourceType == .camera {
            if CameraHelper.isCameraReallyAvailable() {
                picker.sourceType = .camera
            } else {
                picker.sourceType = .photoLibrary
            }
        } else {
            picker.sourceType = sourceType
        }

        picker.allowsEditing = false

        // 如果是相机模式，设置相机捕获模式
        if picker.sourceType == .camera {
            picker.cameraCaptureMode = .photo
            picker.cameraDevice = .rear // 优先使用后置摄像头
        }

        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {
        // 不需要更新
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePickerView
        
        init(_ parent: ImagePickerView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.onImageSelected(editedImage)
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.onImageSelected(originalImage)
            }
            
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
