import Foundation
import UIKit

/// NSAttributedString草稿处理扩展
/// 用于将富文本内容转换为HTML格式进行草稿存储
extension NSAttributedString {
    
    /// 将NSAttributedString转换为HTML字符串用于草稿存储
    /// 图片会被转换为本地路径引用
    /// - Returns: HTML字符串，如果失败返回纯文本
    func toDraftHTML() -> String {
        var htmlParts: [String] = []
        let range = NSRange(location: 0, length: length)

        enumerateAttributes(in: range, options: []) { attributes, range, _ in
            // 检查是否是图片附件
            if let attachment = attributes[.attachment] as? NSTextAttachment,
               let image = attachment.image {

                // 保存图片并生成HTML标签
                let draftImageManager = DraftImageManager.shared
                if let savedPath = draftImageManager.saveDraftImage(image) {
                    let imageHTML = draftImageManager.createImageHTMLTag(for: savedPath)
                    htmlParts.append(imageHTML)
                } else {
                    htmlParts.append("<p>[图片保存失败]</p>")
                }
                return
            }

            // 处理普通文本
            let text = (string as NSString).substring(with: range)

            // 简单的文本处理，保持换行符
            let escapedText = text
                .replacingOccurrences(of: "&", with: "&amp;")
                .replacingOccurrences(of: "<", with: "&lt;")
                .replacingOccurrences(of: ">", with: "&gt;")
                .replacingOccurrences(of: "\n", with: "<br>")

            // 检查文本样式
            var styledText = escapedText

            if let font = attributes[.font] as? UIFont {
                let isBold = font.fontDescriptor.symbolicTraits.contains(.traitBold)
                let isItalic = font.fontDescriptor.symbolicTraits.contains(.traitItalic)

                if isBold {
                    styledText = "<b>\(styledText)</b>"
                }
                if isItalic {
                    styledText = "<i>\(styledText)</i>"
                }
            }

            htmlParts.append(styledText)
        }

        let finalHTML = htmlParts.joined()
        return finalHTML.isEmpty ? string : finalHTML
    }
    
    /// 从HTML字符串创建NSAttributedString用于草稿恢复
    /// - Parameter html: HTML字符串
    /// - Returns: 创建的NSAttributedString，如果失败返回纯文本版本
    static func fromDraftHTML(_ html: String) -> NSAttributedString {
        // 首先处理本地图片，将其替换为占位符
        let processedHTML = preprocessLocalImages(html)

        guard let data = processedHTML.data(using: .utf8) else {
            return NSAttributedString(string: html)
        }

        do {
            let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]

            let attributedString = try NSMutableAttributedString(data: data, options: options, documentAttributes: nil)

            // 修复文字颜色以适应深色模式
            fixTextColorsForDarkMode(attributedString)

            // 后处理：将占位符替换为实际的图片附件
            let result = postprocessLocalImages(attributedString, originalHTML: html)
            return result

        } catch {
            return NSAttributedString(string: html)
        }
    }

    /// 预处理HTML，将本地图片替换为占位符
    private static func preprocessLocalImages(_ html: String) -> String {
        let pattern = #"<p><img src=\"file://([^\"]+)\"[^>]*></p>"#

        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            let processedHTML = regex.stringByReplacingMatches(
                in: html,
                options: [],
                range: NSRange(location: 0, length: html.count),
                withTemplate: "<p>【图片占位符：$1】</p>"
            )
            return processedHTML
        } catch {
            return html
        }
    }

    /// 后处理：将占位符替换为实际的图片附件
    private static func postprocessLocalImages(_ attributedString: NSMutableAttributedString, originalHTML: String) -> NSAttributedString {
        let placeholderPattern = "【图片占位符：([^】]+)】"

        do {
            let regex = try NSRegularExpression(pattern: placeholderPattern, options: [])
            let text = attributedString.string
            let matches = regex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))

            // 从后往前替换，避免索引变化
            for match in matches.reversed() {
                if match.numberOfRanges > 1 {
                    let pathRange = match.range(at: 1)
                    let placeholderRange = match.range(at: 0)

                    if let pathRange = Range(pathRange, in: text),
                       let imagePath = String(text[pathRange]) as String? {

                        // 从路径加载图片
                        let draftImageManager = DraftImageManager.shared
                        if let image = draftImageManager.loadDraftImage(from: imagePath) {
                            // 计算显示尺寸（80%宽度）
                            let maxWidth: CGFloat = 300 // 默认最大宽度
                            let targetWidth = maxWidth * 0.8
                            let aspectRatio = image.size.height / image.size.width
                            let targetHeight = targetWidth * aspectRatio
                            let displaySize = CGSize(width: targetWidth, height: targetHeight)

                            // 创建图片附件
                            let attachment = NSTextAttachment()
                            attachment.image = image
                            attachment.bounds = CGRect(origin: .zero, size: displaySize)

                            // 创建图片的NSAttributedString
                            let imageAttributedString = NSAttributedString(attachment: attachment)

                            // 替换占位符
                            attributedString.replaceCharacters(in: placeholderRange, with: imageAttributedString)

                        } else {
                            // 图片加载失败，替换为错误文本
                            let errorText = NSAttributedString(string: "[图片加载失败]")
                            attributedString.replaceCharacters(in: placeholderRange, with: errorText)
                        }
                    }
                }
            }

            return attributedString

        } catch {
            return attributedString
        }
    }

    /// 修复文字颜色和字体大小以适应编辑器
    private static func fixTextColorsForDarkMode(_ attributedString: NSMutableAttributedString) {
        let range = NSRange(location: 0, length: attributedString.length)
        let standardFontSize: CGFloat = 16 // 编辑器标准字体大小

        // 同时处理颜色和字体
        attributedString.enumerateAttributes(in: range, options: []) { attributes, range, _ in
            var newAttributes: [NSAttributedString.Key: Any] = [:]

            // 处理颜色
            if let color = attributes[.foregroundColor] as? UIColor {
                var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
                color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

                // 检查是否是黑色或深色（RGB值都很低）
                if red < 0.3 && green < 0.3 && blue < 0.3 {
                    newAttributes[.foregroundColor] = UIColor.label
                }
            } else {
                // 如果没有设置颜色属性，设置为系统标签颜色
                newAttributes[.foregroundColor] = UIColor.label
            }

            // 处理字体大小
            if let font = attributes[.font] as? UIFont {
                // 保持字体样式（粗体、斜体），但确保字体大小为16
                let fontDescriptor = font.fontDescriptor
                let isBold = fontDescriptor.symbolicTraits.contains(.traitBold)
                let isItalic = fontDescriptor.symbolicTraits.contains(.traitItalic)

                var traits: UIFontDescriptor.SymbolicTraits = []
                if isBold { traits.insert(.traitBold) }
                if isItalic { traits.insert(.traitItalic) }

                if !traits.isEmpty {
                    if let newDescriptor = UIFont.systemFont(ofSize: standardFontSize).fontDescriptor.withSymbolicTraits(traits) {
                        newAttributes[.font] = UIFont(descriptor: newDescriptor, size: standardFontSize)
                    } else {
                        newAttributes[.font] = UIFont.systemFont(ofSize: standardFontSize)
                    }
                } else {
                    newAttributes[.font] = UIFont.systemFont(ofSize: standardFontSize)
                }
            } else {
                // 如果没有字体属性，设置标准字体
                newAttributes[.font] = UIFont.systemFont(ofSize: standardFontSize)
            }

            // 应用新属性
            attributedString.addAttributes(newAttributes, range: range)
        }


    }
    
    /// 获取纯文本内容（用于搜索和预览）
    var plainText: String {
        return string
    }
    
    /// 获取字数统计
    var wordCount: Int {
        let text = string.trimmingCharacters(in: .whitespacesAndNewlines)
        if text.isEmpty {
            return 0
        }
        
        // 对中文和英文进行不同的计数方式
        var count = 0
        text.enumerateSubstrings(in: text.startIndex..<text.endIndex, options: [.byWords, .localized]) { (substring, _, _, _) in
            if substring != nil {
                count += 1
            }
        }
        
        // 如果没有检测到单词，可能是纯中文，按字符计数
        if count == 0 {
            count = text.count
        }
        
        return count
    }
    
    /// 转换为HTML字符串（用于预览和搜索）
    func toHTML() -> String {
        do {
            let documentAttributes: [NSAttributedString.DocumentAttributeKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]
            
            let htmlData = try data(from: NSRange(location: 0, length: length), 
                                  documentAttributes: documentAttributes)
            
            if let htmlString = String(data: htmlData, encoding: .utf8) {
                return htmlString
            }
        } catch {
            // 转换失败，静默处理
        }
        
        // 如果转换失败，返回纯文本
        return string
    }
    
    /// 从HTML字符串创建NSAttributedString
    /// - Parameter html: HTML字符串
    /// - Returns: 创建的NSAttributedString，如果失败返回纯文本版本
    static func fromHTML(_ html: String) -> NSAttributedString {
        guard let data = html.data(using: .utf8) else {
            return NSAttributedString(string: html)
        }
        
        do {
            let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]
            
            let attributedString = try NSAttributedString(data: data, options: options, documentAttributes: nil)
            return attributedString
        } catch {
            return NSAttributedString(string: html)
        }
    }
    
    /// 生成预览文本（限制长度）
    /// - Parameter maxLength: 最大长度，默认100字符
    /// - Returns: 预览文本
    func previewText(maxLength: Int = 100) -> String {
        let text = plainText.trimmingCharacters(in: .whitespacesAndNewlines)
        if text.count <= maxLength {
            return text
        }
        
        let endIndex = text.index(text.startIndex, offsetBy: maxLength)
        return String(text[..<endIndex]) + "..."
    }
}
