import SwiftUI
import Combine

/// 主富文本编辑器视图
/// - 包含编辑/预览切换、工具栏和内容区域
struct RichTextEditor: View {
    // MARK: - State
    
    /// 富文本内容，使用 NSAttributedString 进行存储
    @State private var attributedText: NSAttributedString = NSAttributedString(
        string: "",
        attributes: [.font: UIFont.systemFont(ofSize: 16)]
    )
    
    /// 当前的编辑模式
    @State private var editMode: EditMode = .edit
    
    /// 存储 UITextView 的引用，以便工具栏可以操作它
    @State private var textView: UITextView?
    
    /// 控制链接输入工作表的显示
    @State private var isShowingLinkSheet = false

    /// 存储正在编辑的链接的信息，包括其在文本中的范围
    @State private var editingLink: (url: URL, text: String, range: NSRange)? = nil

    /// 控制相册选择器的显示
    @State private var isShowingPhotoLibrary = false

    /// 控制相机的显示
    @State private var isShowingCamera = false

    /// 控制草稿列表的显示
    @State private var isShowingDraftList = false

    /// 草稿管理器
    @StateObject private var draftManager = DraftManager()

    /// 自动保存服务
    @StateObject private var autoSaveService = AutoSaveService()

    /// 设置管理器
    @StateObject private var settingsManager = SettingsManager()

    /// 显示草稿设置
    @State private var isShowingDraftSettings = false

    /// Combine订阅
    @State private var cancellables = Set<AnyCancellable>()

    /// 撤销重做管理器
    @StateObject private var undoRedoManager = UndoRedoManager()

    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部工具栏和模式切换
            headerView

            Divider()

            // 内容区域
            contentView
                .frame(maxWidth: .infinity, maxHeight: .infinity)

            // 底部状态栏
            if settingsManager.showWordCount {
                Divider()
                statusBarView
            }
        }
        .navigationTitle("富文本编辑器")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("完成") {
                    textView?.resignFirstResponder()
                }
            }
        }
    }
    
    // MARK: - Subviews
    
    /// 顶部的视图，包含模式切换和工具栏
    @ViewBuilder
    private var headerView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // 编辑/预览模式切换
                Picker("模式", selection: $editMode) {
                    Text("编辑").tag(EditMode.edit)
                    Text("预览").tag(EditMode.preview)
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 150)

                Spacer()

                // 保存状态指示器
                if !autoSaveService.saveStatusText.isEmpty {
                    Text(autoSaveService.saveStatusText)
                        .font(.caption)
                        .foregroundColor(Color(autoSaveService.saveStatusColor))
                }

                // 草稿按钮
                Button(action: {
                    isShowingDraftList = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "externaldrive.badge.checkmark")
                            .font(.title2)
                        if draftManager.drafts.count > 0 {
                            Text("\(draftManager.drafts.count)")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.red)
                                .foregroundColor(.white)
                                .clipShape(Capsule())
                        }
                    }
                }

                // 设置按钮
                Button(action: {
                    isShowingDraftSettings = true
                }) {
                    Image(systemName: "gear")
                        .font(.title2)
                }
            }
            
            // 在编辑模式下显示可滚动的工具栏
            if editMode == .edit {
                ScrollView(.horizontal, showsIndicators: false) {
                    ToolbarView(
                        attributedText: $attributedText,
                        textView: textView,
                        onAddLink: {
                            // 清除之前的编辑状态，准备创建新链接
                            editingLink = nil
                            isShowingLinkSheet = true
                        },
                        onAddUnorderedList: addUnorderedList,
                        onAddOrderedList: addOrderedList,
                        onToggleTextDirection: toggleTextDirection,
                        onToggleBlockquote: toggleBlockquote,
                        onAddFootnote: addFootnote,
                        onAddSpoiler: addSpoiler,
                        onAddImage: { sourceType in
                            if sourceType == .camera {
                                isShowingCamera = true
                            } else {
                                isShowingPhotoLibrary = true
                            }
                        },
                        onSaveManually: saveManually,
                        onCreateNewDraft: createNewDraft
                    )
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .sheet(isPresented: $isShowingLinkSheet) {
            // 准备 LinkInputView 的初始值
            let initialText = editingLink?.text.trimmingCharacters(in: .whitespacesAndNewlines) ?? selectedText()
            let initialURL = editingLink?.url.absoluteString ?? ""

            LinkInputView(
                initialText: initialText,
                initialURL: initialURL,
                onComplete: { text, urlString in
                    if let range = editingLink?.range {
                        // 编辑现有链接
                        updateLink(text: text, urlString: urlString, at: range)
                    } else {
                        // 插入新链接
                        insertLink(text: text, urlString: urlString)
                    }
                }
            )
        }
        .sheet(isPresented: $isShowingPhotoLibrary) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                insertImage(image)
            }
        }
        .sheet(isPresented: $isShowingCamera) {
            ImagePickerView(sourceType: .camera) { image in
                insertImage(image)
            }
        }
        .sheet(isPresented: $isShowingDraftList) {
            DraftListView(draftManager: draftManager) { draft in
                // 恢复草稿到编辑器
                restoreDraft(draft)
                isShowingDraftList = false
            }
        }
        .sheet(isPresented: $isShowingDraftSettings) {
            DraftSettingsView(
                settingsManager: settingsManager,
                draftManager: draftManager
            )
        }
        .onAppear {
            setupAutoSave()
        }
        .onDisappear {
            autoSaveService.stopAutoSave()
        }
    }
    
    /// 内容视图，根据模式显示编辑器或预览
    @ViewBuilder
    private var contentView: some View {
        if editMode == .edit {
            RichTextEditorRepresentable(
                attributedText: $attributedText,
                textView: $textView,
                onLinkTapped: { url, text, range in
                    // 当链接被点击时，设置编辑状态并显示工作表
                    self.editingLink = (url, text, range)
                    self.isShowingLinkSheet = true
                },
                onContentChanged: { newContent in
                    // 记录撤销操作
                    if let textView = textView {
                        undoRedoManager.recordOperation(content: newContent, selectedRange: textView.selectedRange)
                    }

                    // 通知自动保存服务内容已变化
                    autoSaveService.contentDidChange(newContent)
                },
                undoRedoManager: undoRedoManager
            )
        } else {
            RichTextPreviewView(attributedText: attributedText)
        }
    }

    /// 底部状态栏
    @ViewBuilder
    private var statusBarView: some View {
        HStack {
            // 字数统计
            Text("\(attributedText.wordCount) 字")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            // 当前草稿信息
            if let currentDraft = draftManager.currentDraft {
                Text("草稿: \(currentDraft.title ?? "无标题")")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            // 保存状态
            if !autoSaveService.saveStatusText.isEmpty {
                Text(autoSaveService.saveStatusText)
                    .font(.caption)
                    .foregroundColor(Color(autoSaveService.saveStatusColor))
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(UIColor.systemGray6))
    }

    // MARK: - Private Methods
    
    /// 获取当前选中的文本
    private func selectedText() -> String {
        guard let range = textView?.selectedTextRange else { return "" }
        return textView?.text(in: range) ?? ""
    }
    
    /// 插入新链接
    private func insertLink(text: String, urlString: String) {
        guard let range = textView?.selectedRange else { return }
        updateLink(text: text, urlString: urlString, at: range)
    }
    
    /// 更新或插入链接
    private func updateLink(text: String, urlString: String, at range: NSRange) {
        guard let url = URL(string: urlString) else { return }
        
        let font = UIFont.systemFont(ofSize: 16)
        let linkColor = UIColor.systemBlue

        // 创建带图标的附件
        let attachment = NSTextAttachment()
        if let iconImage = UIImage(systemName: "link")?.withTintColor(linkColor) {
            attachment.image = iconImage
            let iconSize = font.pointSize * 0.9
            attachment.bounds = CGRect(x: 0, y: font.descender, width: iconSize, height: iconSize)
        }
        
        // 创建完整的链接字符串（图标 + 标记 + 文本）
        let fullLinkString = NSMutableAttributedString()
        fullLinkString.append(NSAttributedString(attachment: attachment))
        fullLinkString.append(NSAttributedString(string: "\u{200B} ")) // 零宽空格标记
        fullLinkString.append(NSAttributedString(string: text))
        
        // 应用链接属性
        let fullRange = NSRange(location: 0, length: fullLinkString.length)
        fullLinkString.addAttributes([
            .link: url,
            .font: font,
            .foregroundColor: linkColor,
            .underlineStyle: NSUnderlineStyle.single.rawValue
        ], range: fullRange)
        
        // 更新文本视图
        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        mutableString.replaceCharacters(in: range, with: fullLinkString)
        self.attributedText = mutableString
    }
    
    /// 添加无序列表
    private func addUnorderedList() {
        applyListStyle(ordered: false)
    }
    
    /// 添加有序列表
    private func addOrderedList() {
        applyListStyle(ordered: true)
    }
    
    /// 应用列表样式
    private func applyListStyle(ordered: Bool) {
        guard let textView = textView, let selectedRange = textView.selectedTextRange else { return }
        
        // 获取选区的文本，并按行分割
        let selectedText = textView.text(in: selectedRange) ?? ""
        let lines = selectedText.isEmpty ? [""] : selectedText.split(separator: "\n", omittingEmptySubsequences: false).map(String.init)
        
        // 获取当前输入位置的样式，以便新列表项可以继承
        let typingAttributes = textView.typingAttributes
        
        // 创建一个新的富文本字符串来构建列表
        let listAttributedString = NSMutableAttributedString()
        
        for (index, line) in lines.enumerated() {
            // 根据列表类型（有序/无序）创建前缀
            let prefix = ordered ? "\(index + 1). " : "• " // 使用 • 符号以获得更好的视觉效果
            let fullLine = "\(prefix)\(line)\n"
            
            // 创建带样式的行
            let attributedLine = NSAttributedString(string: fullLine, attributes: typingAttributes)
            listAttributedString.append(attributedLine)
        }
        
        // 如果最后多了一个换行符，则移除它
        if listAttributedString.string.hasSuffix("\n") {
            listAttributedString.deleteCharacters(in: NSRange(location: listAttributedString.length - 1, length: 1))
        }
        
        // 获取当前的富文本内容
        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        
        // 用新创建的列表替换选定的范围
        let replacementRange = textView.selectedRange
        mutableString.replaceCharacters(in: replacementRange, with: listAttributedString)
        
        // 更新状态
        self.attributedText = mutableString
        
        // 更新光标位置到修改后文本的末尾
        DispatchQueue.main.async {
            let newCursorPosition = replacementRange.location + listAttributedString.length
            textView.selectedRange = NSRange(location: newCursorPosition, length: 0)
        }
    }

    /// 添加脚注
    private func addFootnote() {
        guard let textView = textView else { return }
        let selectedRange = textView.selectedRange
        
        // 插入一个通用的脚注模板 ^[]
        let footnoteText = "^[]"
        let footnoteAttributedString = NSAttributedString(string: footnoteText, attributes: textView.typingAttributes)
        
        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        mutableString.replaceCharacters(in: selectedRange, with: footnoteAttributedString)
        
        self.attributedText = mutableString
        
        // 将光标定位到 `[]` 中间，方便用户输入脚注内容
        DispatchQueue.main.async {
            let newCursorPosition = selectedRange.location + footnoteText.count - 1
            textView.selectedRange = NSRange(location: newCursorPosition, length: 0)
        }
    }

    /// 添加剧透
    private func addSpoiler() {
        guard let textView = textView else { return }
        let selectedRange = textView.selectedRange

        let spoilerTag = "[spoiler][/spoiler]"
        let spoilerAttributedString = NSAttributedString(string: spoilerTag, attributes: textView.typingAttributes)

        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        mutableString.replaceCharacters(in: selectedRange, with: spoilerAttributedString)

        self.attributedText = mutableString

        // 将光标定位到 `[spoiler]` 和 `[/spoiler]` 之间
        DispatchQueue.main.async {
            let newCursorPosition = selectedRange.location + "[spoiler]".count
            textView.selectedRange = NSRange(location: newCursorPosition, length: 0)
        }
    }

    /// 插入图片
    private func insertImage(_ image: UIImage) {
        guard let textView = textView else { return }
        let selectedRange = textView.selectedRange

        // 保存图片到草稿目录并获取HTML标签
        if let imageHTMLTag = draftManager.saveDraftImage(image) {
            // 将HTML标签转换为NSAttributedString
            let imageAttributedString = NSAttributedString.fromDraftHTML(imageHTMLTag)

            // 在图片前后添加换行符，确保图片独占一行
            let finalString = NSMutableAttributedString()

            // 如果光标不在行首，先添加换行
            if selectedRange.location > 0 {
                let previousChar = (attributedText.string as NSString).character(at: selectedRange.location - 1)
                if previousChar != unichar("\n".utf16.first!) {
                    finalString.append(NSAttributedString(string: "\n", attributes: textView.typingAttributes))
                }
            }

            finalString.append(imageAttributedString)
            finalString.append(NSAttributedString(string: "\n", attributes: textView.typingAttributes))

            // 插入到文档中
            let mutableString = NSMutableAttributedString(attributedString: attributedText)
            mutableString.replaceCharacters(in: selectedRange, with: finalString)

            self.attributedText = mutableString

            // 将光标移动到图片后面
            DispatchQueue.main.async {
                let newCursorPosition = selectedRange.location + finalString.length
                textView.selectedRange = NSRange(location: newCursorPosition, length: 0)
            }

        }
    }

    /// 计算图片显示尺寸
    private func calculateImageSize(image: UIImage, maxWidth: CGFloat) -> CGSize {
        let originalSize = image.size
        let aspectRatio = originalSize.height / originalSize.width

        // 始终使用maxWidth作为目标宽度（80%宽度）
        let newWidth = maxWidth
        let newHeight = newWidth * aspectRatio

        return CGSize(width: newWidth, height: newHeight)
    }

    /// 缩放图片到指定尺寸
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage ?? image
    }
    
    /// 切换文本方向
    private func toggleTextDirection() {
        guard let textView = textView else { return }

        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        let selectedRange = textView.selectedRange

        // 如果选区为空，则不执行任何操作
        if selectedRange.length == 0 {
            // 获取当前光标所在段落的范围
            let paragraphRange = (textView.text as NSString).paragraphRange(for: selectedRange)
            applyDirectionChange(to: paragraphRange, in: mutableString)
        } else {
            // 遍历选区中的每个段落并应用方向更改
            (textView.text as NSString).enumerateSubstrings(in: selectedRange, options: .byParagraphs) { _, paragraphRange, _, _ in
                self.applyDirectionChange(to: paragraphRange, in: mutableString)
            }
        }

        self.attributedText = mutableString
        
        // 修改文本后恢复光标的原始位置
        textView.selectedRange = selectedRange
    }

    /// 将文本方向更改应用于指定的范围
    /// - Parameters:
    ///   - range: 要应用更改的范围
    ///   - mutableString: 要修改的富文本字符串
    private func applyDirectionChange(to range: NSRange, in mutableString: NSMutableAttributedString) {
        // 确保我们有一个有效的范围来获取属性
        let location = min(range.location, max(0, mutableString.length - 1))
        
        // 如果字符串为空，则不执行任何操作
        guard mutableString.length > 0 else {
            return
        }

        // 获取当前段落的样式
        let existingStyle = mutableString.attribute(.paragraphStyle, at: location, effectiveRange: nil) as? NSParagraphStyle
        let newParagraphStyle = (existingStyle?.mutableCopy() as? NSMutableParagraphStyle) ?? NSMutableParagraphStyle()

        // 切换对齐方式和书写方向
        if newParagraphStyle.alignment == .left || newParagraphStyle.alignment == .natural {
            newParagraphStyle.alignment = .right
            newParagraphStyle.baseWritingDirection = .rightToLeft
        } else {
            newParagraphStyle.alignment = .left
            newParagraphStyle.baseWritingDirection = .leftToRight
        }

        // 应用新样式
        mutableString.addAttribute(.paragraphStyle, value: newParagraphStyle, range: range)
    }
    
    /// 切换块引用样式 (Markdown `> `)
    private func toggleBlockquote() {
        guard let textView = textView else { return }

        let selectedRange = textView.selectedRange
        let mutableString = NSMutableAttributedString(attributedString: attributedText)
        let fullText = mutableString.string as NSString

        // 如果在空编辑器中，直接插入
        if mutableString.length == 0 {
            let insertionString = NSAttributedString(string: "> ", attributes: textView.typingAttributes)
            mutableString.insert(insertionString, at: 0)
            self.attributedText = mutableString
            textView.selectedRange = NSRange(location: insertionString.length, length: 0)
            return
        }
        
        // 确定要操作的范围
        var rangesToUpdate = [NSRange]()
        if selectedRange.length > 0 {
            fullText.enumerateSubstrings(in: selectedRange, options: .byParagraphs) { _, paragraphRange, _, _ in
                // 包含被选择的非空段落或空行
                if paragraphRange.length > 0 || paragraphRange.location == selectedRange.location {
                    rangesToUpdate.append(paragraphRange)
                }
            }
        } else {
            rangesToUpdate.append(fullText.paragraphRange(for: selectedRange))
        }

        // 检查第一个段落是否已经是块引用
        guard let firstRange = rangesToUpdate.first, firstRange.location != NSNotFound else { return }
        let firstParagraph = fullText.substring(with: firstRange)
        let isCurrentlyBlockquote = firstParagraph.trimmingCharacters(in: .whitespacesAndNewlines).hasPrefix(">")

        var totalOffset = 0

        // 从后往前遍历以避免范围失效
        for range in rangesToUpdate.reversed() {
            let paragraphText = fullText.substring(with: range)
            
            if isCurrentlyBlockquote {
                // 移除块引用标记
                if let prefixRange = paragraphText.range(of: ">") {
                    let nsPrefixRange = NSRange(prefixRange, in: paragraphText)
                    // 移除 ">" 和它后面的一个空格
                    let removalLength = (paragraphText.dropFirst(nsPrefixRange.location + 1).first == " ") ? 2 : 1
                    let removalRange = NSRange(location: range.location + nsPrefixRange.location, length: removalLength)
                    
                    if removalRange.location != NSNotFound && NSMaxRange(removalRange) <= mutableString.length {
                        mutableString.deleteCharacters(in: removalRange)
                        totalOffset -= removalRange.length
                    }
                }
            } else {
                // 添加块引用标记
                let insertionString = NSAttributedString(string: "> ", attributes: textView.typingAttributes)
                mutableString.insert(insertionString, at: range.location)
                totalOffset += insertionString.length
            }
        }

        // 更新文本视图并调整光标
        let newSelectedRange = NSRange(location: selectedRange.location + totalOffset, length: selectedRange.length)
        self.attributedText = mutableString
        
        DispatchQueue.main.async {
            let finalCursorPosition = min(max(0, newSelectedRange.location), mutableString.length)
            let finalSelectionLength = min(newSelectedRange.length, mutableString.length - finalCursorPosition)
            textView.selectedRange = NSRange(location: finalCursorPosition, length: finalSelectionLength)
        }
    }
}

// MARK: - Inner Types

extension RichTextEditor {
    /// 编辑模式枚举
    enum EditMode {
        case edit
        case preview
    }

    // MARK: - Draft Methods

    /// 设置自动保存
    private func setupAutoSave() {
        // 根据设置启用或禁用自动保存
        autoSaveService.isAutoSaveEnabled = settingsManager.enableAutoSave
        autoSaveService.startAutoSave(with: draftManager)

        // 监听设置变化
        settingsManager.$enableAutoSave
            .sink { enabled in
                autoSaveService.isAutoSaveEnabled = enabled
            }
            .store(in: &cancellables)

        // 如果有初始内容，创建草稿并记录初始状态
        if !attributedText.string.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            autoSaveService.createNewDraft(with: attributedText)
            // 记录初始状态到撤销管理器
            undoRedoManager.recordOperation(content: attributedText, selectedRange: NSRange(location: 0, length: 0))
        }
    }

    /// 恢复草稿到编辑器
    private func restoreDraft(_ draft: Draft) {
        let content = draftManager.getAttributedContent(from: draft)
        attributedText = content
        autoSaveService.switchToDraft(draft)

        // 清空撤销历史并记录新的初始状态
        undoRedoManager.clearHistory()
        undoRedoManager.recordOperation(content: content, selectedRange: NSRange(location: 0, length: 0))

        // 切换到编辑模式
        editMode = .edit
    }

    /// 手动保存当前内容
    private func saveManually() {
        autoSaveService.saveManually(attributedText)
    }

    /// 创建新草稿
    private func createNewDraft() {
        // 清空当前内容
        attributedText = NSAttributedString(string: "")

        // 清空撤销历史
        undoRedoManager.clearHistory()

        // 创建新草稿
        autoSaveService.createNewDraft(with: attributedText)
    }
}

// MARK: - Previews

struct RichTextEditor_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            RichTextEditor()
        }
    }
}
