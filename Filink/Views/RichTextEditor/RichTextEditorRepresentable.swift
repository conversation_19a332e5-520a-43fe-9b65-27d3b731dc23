import SwiftUI
import UIKit
import Combine

/// `UIViewRepresentable` 封装，将 `UITextView` 引入 SwiftUI
struct RichTextEditorRepresentable: UIViewRepresentable {
    
    /// 与 SwiftUI 视图双向绑定的富文本
    @Binding var attributedText: NSAttributedString
    
    /// 绑定 UITextView 的引用
    @Binding var textView: UITextView?
    
    /// 点击链接时的回调
    var onLinkTapped: ((URL, String, NSRange) -> Void)?

    /// 内容变化时的回调
    var onContentChanged: ((NSAttributedString) -> Void)?

    /// 撤销重做管理器
    var undoRedoManager: UndoRedoManager?
    
    // MARK: - UIViewRepresentable
    
    /// 创建底层的 `UITextView`
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.delegate = context.coordinator

        // 基础样式配置
        textView.isEditable = true
        textView.isScrollEnabled = true
        textView.font = UIFont.systemFont(ofSize: 16)
        // 确保新输入的文本也使用正确的字号
        textView.typingAttributes = [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.label
        ]
        textView.autocapitalizationType = .sentences
        textView.isSelectable = true
        textView.isUserInteractionEnabled = true

        // 添加键盘工具栏
        textView.inputAccessoryView = context.coordinator.createKeyboardToolbar(for: textView)

        // 在下一个 run loop 中设置 textView，避免修改视图状态的警告
        DispatchQueue.main.async {
            self.textView = textView
        }

        return textView
    }
    
    /// 从 SwiftUI 更新 `UITextView`
    func updateUIView(_ uiView: UITextView, context: Context) {
        // 避免在用户输入时被 SwiftUI 的更新覆盖
        if uiView.attributedText != attributedText {
            uiView.attributedText = attributedText
        }
    }
    
    /// 创建协调器 `Coordinator`
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // MARK: - Coordinator
    
    /// 协调器，用于处理 `UITextView` 的代理回调
    class Coordinator: NSObject, UITextViewDelegate {
        var parent: RichTextEditorRepresentable
        private var toolbarHelper: KeyboardToolbarHelper?

        init(_ parent: RichTextEditorRepresentable) {
            self.parent = parent
        }
        
        /// 当文本内容发生变化时调用
        func textViewDidChange(_ textView: UITextView) {
            let mutableString = NSMutableAttributedString(attributedString: textView.attributedText)
            
            // 遍历整个文本，应用或移除块引用样式
            mutableString.enumerateAttribute(.isBlockquote, in: NSRange(location: 0, length: mutableString.length)) { value, range, _ in
                if let isBlockquote = value as? Bool, isBlockquote {
                    // 应用块引用颜色
                    mutableString.addAttribute(.foregroundColor, value: UIColor.systemGreen, range: range)
                } else {
                    // 恢复默认颜色（如果需要）
                    // 注意：这可能会覆盖用户选择的其他颜色。一个更复杂的实现需要检查其他颜色属性。
                    // 为简单起见，我们假设块引用优先于其他颜色。
                    // 如果要移除块引用，toggleBlockquote 应该已经处理了颜色的恢复。
                }
            }
            
            // 将 `UITextView` 的内容同步回 SwiftUI 的 `@Binding`
            // 检查是否真的有变化，避免无限循环
            if textView.attributedText != mutableString {
                // 保存当前光标位置
                let selectedRange = textView.selectedRange
                textView.attributedText = mutableString
                // 恢复光标位置
                textView.selectedRange = selectedRange
            }
            
            parent.attributedText = textView.attributedText

            // 通知内容变化
            parent.onContentChanged?(textView.attributedText)
        }
        
        /// 控制是否与 URL 交互
        func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
            // 拦截链接点击
            if interaction == .invokeDefaultAction {
                let text = textView.attributedText.attributedSubstring(from: characterRange).string
                parent.onLinkTapped?(URL, text, characterRange)
                return false // 阻止默认行为（打开浏览器）
            }
            return true
        }
        
        /// 在文本更改前调用，用于实现自动列表项
        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // 仅在用户按下回车键时触发
            if text == "\n" {
                // 使用 DispatchQueue.main.async 延迟执行，确保在换行符插入后重置样式
                DispatchQueue.main.async {
                    // 保留颜色和字号，但重置字体为默认样式（移除粗体、斜体等）
                    let currentColor = textView.typingAttributes[.foregroundColor] as? UIColor ?? .label
                    let currentFontSize = (textView.typingAttributes[.font] as? UIFont)?.pointSize ?? 16
                    
                    textView.typingAttributes = [
                        .font: UIFont.systemFont(ofSize: currentFontSize),
                        .foregroundColor: currentColor
                    ]
                }
            }
            
            guard text == "\n" else {
                return true
            }

            let currentLineRange = (textView.text as NSString).lineRange(for: range)
            let currentLine = (textView.text as NSString).substring(with: currentLineRange)
            var newListItemText: String?

            // --- 无序列表处理 ---
            let unorderedPrefix = "• "
            if currentLine.hasPrefix(unorderedPrefix) {
                if currentLine.trimmingCharacters(in: .whitespacesAndNewlines) == "•" {
                    // 在空的列表项中按回车，退出列表
                    textView.textStorage.replaceCharacters(in: currentLineRange, with: "")
                    textView.selectedRange = NSRange(location: currentLineRange.location, length: 0)
                    return false
                } else {
                    // 创建新的列表项
                    newListItemText = "\n" + unorderedPrefix
                }
            }
            // --- 有序列表处理 ---
            else if let match = currentLine.range(of: "^\\d+\\. ", options: .regularExpression) {
                let content = currentLine[match.upperBound...].trimmingCharacters(in: .whitespacesAndNewlines)
                if content.isEmpty {
                    // 在空的列表项中按回车，退出列表
                    textView.textStorage.replaceCharacters(in: currentLineRange, with: "")
                    textView.selectedRange = NSRange(location: currentLineRange.location, length: 0)
                    return false
                } else {
                    // 创建新的列表项
                    let numberString = String(currentLine.prefix(while: { $0.isNumber }))
                    if let number = Int(numberString) {
                        newListItemText = "\n\(number + 1). "
                    }
                }
            }

            // 如果确定要插入新的列表项
            if let newText = newListItemText {
                let attributes = textView.typingAttributes
                let attributedNewText = NSAttributedString(string: newText, attributes: attributes)
                
                // 替换当前选区（通常是光标位置）为新的列表项文本
                textView.textStorage.replaceCharacters(in: range, with: attributedNewText)
                
                // 将光标移动到新插入文本的末尾
                let newCursorPosition = range.location + attributedNewText.length
                textView.selectedRange = NSRange(location: newCursorPosition, length: 0)
                
                // 我们已经处理了输入，所以阻止系统默认行为
                return false
            }
            
            // 对于所有其他情况，使用系统默认行为
            return true
        }

        /// 创建键盘工具栏
        func createKeyboardToolbar(for textView: UITextView) -> UIToolbar {
            let toolbar = UIToolbar()
            toolbar.sizeToFit()

            // 创建工具栏助手并保持强引用
            let helper = KeyboardToolbarHelper()
            self.toolbarHelper = helper
            helper.textView = textView
            helper.onContentChanged = { [weak self] content, range in
                self?.parent.attributedText = content
                self?.parent.onContentChanged?(content)
            }

            // 创建撤销按钮
            let undoButton = UIBarButtonItem(
                image: UIImage(systemName: "arrow.uturn.backward"),
                style: .plain,
                target: helper,
                action: #selector(KeyboardToolbarHelper.performUndo)
            )

            // 创建重做按钮
            let redoButton = UIBarButtonItem(
                image: UIImage(systemName: "arrow.uturn.forward"),
                style: .plain,
                target: helper,
                action: #selector(KeyboardToolbarHelper.performRedo)
            )

            // 创建关闭键盘按钮
            let doneButton = UIBarButtonItem(
                image: UIImage(systemName: "keyboard.chevron.compact.down"),
                style: .plain,
                target: helper,
                action: #selector(KeyboardToolbarHelper.dismissKeyboard)
            )

            // 创建灵活空间
            let flexSpace = UIBarButtonItem(
                barButtonSystemItem: .flexibleSpace,
                target: nil,
                action: nil
            )

            // 创建固定空间
            let fixedSpace = UIBarButtonItem(
                barButtonSystemItem: .fixedSpace,
                target: nil,
                action: nil
            )
            fixedSpace.width = 20

            // 设置工具栏项目：撤销 - 重做 - 灵活空间 - 关闭键盘
            toolbar.items = [undoButton, fixedSpace, redoButton, flexSpace, doneButton]

            // 保存按钮引用到helper中
            helper.undoButton = undoButton
            helper.redoButton = redoButton

            // 设置撤销重做管理器并开始监听状态变化
            helper.setupUndoRedoManager(parent.undoRedoManager)

            return toolbar
        }
    }
}

// MARK: - KeyboardToolbarHelper

/// 键盘工具栏辅助类，用于处理Objective-C方法调用
private class KeyboardToolbarHelper: NSObject {
    weak var textView: UITextView?
    weak var undoRedoManager: UndoRedoManager?
    var onContentChanged: ((NSAttributedString, NSRange) -> Void)?

    // 工具栏按钮引用，用于更新状态
    weak var undoButton: UIBarButtonItem?
    weak var redoButton: UIBarButtonItem?

    // Combine订阅
    private var cancellables = Set<AnyCancellable>()

    @objc func dismissKeyboard() {
        textView?.resignFirstResponder()
    }

    @objc func performUndo() {
        guard let textView = textView,
              let undoRedoManager = undoRedoManager,
              let result = undoRedoManager.undo() else {
            return
        }

        // 更新文本视图内容
        textView.attributedText = result.content
        textView.selectedRange = result.selectedRange

        // 通知内容变化
        onContentChanged?(result.content, result.selectedRange)
    }

    @objc func performRedo() {
        guard let textView = textView,
              let undoRedoManager = undoRedoManager,
              let result = undoRedoManager.redo() else {
            return
        }

        // 更新文本视图内容
        textView.attributedText = result.content
        textView.selectedRange = result.selectedRange

        // 通知内容变化
        onContentChanged?(result.content, result.selectedRange)
    }

    /// 设置撤销重做管理器并开始监听状态变化
    func setupUndoRedoManager(_ manager: UndoRedoManager?) {
        self.undoRedoManager = manager

        // 清除之前的订阅
        cancellables.removeAll()

        guard let manager = manager else { return }

        // 监听canUndo状态变化
        manager.$canUndo
            .receive(on: DispatchQueue.main)
            .sink { [weak self] canUndo in
                self?.undoButton?.isEnabled = canUndo
            }
            .store(in: &cancellables)

        // 监听canRedo状态变化
        manager.$canRedo
            .receive(on: DispatchQueue.main)
            .sink { [weak self] canRedo in
                self?.redoButton?.isEnabled = canRedo
            }
            .store(in: &cancellables)
    }
}
