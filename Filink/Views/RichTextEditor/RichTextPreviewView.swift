import SwiftUI

/// 富文本预览视图
/// - 复用项目中的 `AttributedTextView` 来渲染 `NSAttributedString`
struct RichTextPreviewView: View {
    
    /// 需要预览的富文本内容
    let attributedText: NSAttributedString
    
    @State private var contentItems: [ContentItem] = []

    var body: some View {
        // 使用 ScrollView 包裹，以便内容过长时可以滚动
        ScrollView {
            // 复用 AttributedTextView 进行渲染，添加onUpdate回调以支持剧透内容的交互
            AttributedTextView(
                contentItems: $contentItems,
                style: .init(),
                fontScope: .detail, // 使用详情页作用域，适合富文本预览
                onUpdate: { updatedItems in
                    // 更新contentItems以支持剧透内容的显示/隐藏切换
                    self.contentItems = updatedItems
                }
            )
            .padding()
        }
        .onAppear {
            self.contentItems = convertToContentItems(from: attributedText)
        }
        .onChange(of: attributedText) { newAttributedText in
            // 当attributedText变化时，重新转换ContentItems
            self.contentItems = convertToContentItems(from: newAttributedText)
        }
    }
    
    /// 将 NSAttributedString 转换为 [ContentItem]
    private func convertToContentItems(from attributedString: NSAttributedString) -> [ContentItem] {
        // 1. 将 NSAttributedString 转换为 HTML 字符串
        guard let html = HTMLConverter.toHTML(from: attributedString) else {
            return [.text("无法加载预览")]
        }

        // 2. 使用 HTMLParser 解析 HTML
        let parser = HTMLParser()
        do {
            // HTMLParser 的 parse 方法直接返回 [ContentItem]
            return try parser.parse(html: html)
        } catch {
            return [.text("预览解析失败: \(error.localizedDescription)")]
        }
    }
}
