import SwiftUI

/// 格式化工具栏
struct ToolbarView: View {
    
    /// 绑定到 `UITextView` 的文本，用于应用格式
    @Binding var attributedText: NSAttributedString
    
    /// 文本视图的引用，用于直接操作
    var textView: UITextView?
    
    /// 点击添加链接按钮时的回调
    var onAddLink: () -> Void
    
    /// 点击添加无序列表按钮时的回调
    var onAddUnorderedList: () -> Void
    
    /// 点击添加有序列表按钮时的回调
    var onAddOrderedList: () -> Void
    
    /// 点击切换文本方向按钮时的回调
    var onToggleTextDirection: () -> Void
    
    /// 点击块引用按钮时的回调
    var onToggleBlockquote: () -> Void

    /// 点击脚注按钮时的回调
    var onAddFootnote: () -> Void

    /// 点击剧透按钮时的回调
    var onAddSpoiler: () -> Void

    /// 点击插入图片按钮时的回调
    var onAddImage: (UIImagePickerController.SourceType) -> Void

    /// 点击手动保存按钮时的回调
    var onSaveManually: (() -> Void)?

    /// 点击新建草稿按钮时的回调
    var onCreateNewDraft: (() -> Void)?

    var body: some View {
        HStack(spacing: 16) {
            // 加粗按钮
            Button(action: toggleBold) {
                Image(systemName: "bold")
            }
            
            // 斜体按钮
            Button(action: toggleItalic) {
                Image(systemName: "italic")
            }
            
            // 块引用按钮
            Button(action: onToggleBlockquote) {
                Image(systemName: "text.quote")
            }
            
            // 插入链接按钮
            Button(action: onAddLink) {
                Image(systemName: "link")
            }
            
            // 无序列表按钮
            Button(action: onAddUnorderedList) {
                Image(systemName: "list.bullet")
            }
            
            // 有序列表按钮
            Button(action: onAddOrderedList) {
                Image(systemName: "list.number")
            }
            
            // 文本方向按钮
            Button(action: onToggleTextDirection) {
                Image(systemName: "text.alignleft")
            }
            
            // 脚注按钮
            Button(action: onAddFootnote) {
                Image(systemName: "f.cursive.circle")
            }
            
            // 剧透按钮
            Button(action: onAddSpoiler) {
                Image(systemName: "eye.slash")
            }

            // 插入图片按钮 - 使用Menu
            Menu {
                Button("从相册选择") {
                    onAddImage(.photoLibrary)
                }

                Button("拍照") {
                    onAddImage(.camera)
                }
            } label: {
                Image(systemName: "photo")
            }

            // 分隔线
            Divider()
                .frame(height: 20)

            // 手动保存按钮
            if let onSaveManually = onSaveManually {
                Button(action: onSaveManually) {
                    Image(systemName: "square.and.arrow.down")
                }
            }

            // 新建草稿按钮
            if let onCreateNewDraft = onCreateNewDraft {
                Button(action: onCreateNewDraft) {
                    Image(systemName: "doc.badge.plus")
                }
            }
        }
        .font(.title2)
    }
    
    // MARK: - Actions
    
    /// 切换选中文字的加粗状态
    private func toggleBold() {
        toggleTrait(.traitBold)
    }
    
    /// 切换选中文字的斜体状态
    private func toggleItalic() {
        toggleTrait(.traitItalic)
    }
    
    /// 切换字体的某个特征（如粗体、斜体）
    private func toggleTrait(_ trait: UIFontDescriptor.SymbolicTraits) {
        guard let textView = textView else { return }
        var range = textView.selectedRange

        // 如果没有选择文本，则将范围扩大到当前行
        if range.length == 0 {
            range = (textView.text as NSString).lineRange(for: range)
        }

        // 确定当前字体，优先从输入属性获取，其次从选中区域获取
        let currentFont = (textView.typingAttributes[.font] as? UIFont) ??
                          (range.length > 0 ? textView.textStorage.attribute(.font, at: range.location, effectiveRange: nil) as? UIFont : nil) ??
                          UIFont.systemFont(ofSize: 16)

        // 切换字体特征
        let newFont: UIFont
        if currentFont.fontDescriptor.symbolicTraits.contains(trait) {
            // 移除特征
            newFont = UIFont(descriptor: currentFont.fontDescriptor.withSymbolicTraits(currentFont.fontDescriptor.symbolicTraits.subtracting(trait))!, size: currentFont.pointSize)
        } else {
            // 添加特征
            newFont = UIFont(descriptor: currentFont.fontDescriptor.withSymbolicTraits(currentFont.fontDescriptor.symbolicTraits.union(trait))!, size: currentFont.pointSize)
        }
        
        // 将新字体应用到输入属性
        textView.typingAttributes[.font] = newFont
        
        // 如果有选中区域，也应用到选中区域
        if range.length > 0 {
            let mutableString = NSMutableAttributedString(attributedString: textView.attributedText)
            mutableString.addAttribute(.font, value: newFont, range: range)
            
            let originalSelectedRange = textView.selectedRange
            textView.attributedText = mutableString
            self.attributedText = mutableString
            textView.selectedRange = originalSelectedRange
        }
    }
    
}
