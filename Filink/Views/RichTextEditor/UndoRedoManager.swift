import Foundation
import UIKit

/// 撤销重做管理器
/// 管理富文本编辑器的撤销和重做操作，最多保存3步操作
class UndoRedoManager: ObservableObject {
    
    // MARK: - Types
    
    /// 编辑操作记录
    struct EditOperation {
        let content: NSAttributedString
        let selectedRange: NSRange
        let timestamp: Date
        
        init(content: NSAttributedString, selectedRange: NSRange) {
            self.content = NSAttributedString(attributedString: content)
            self.selectedRange = selectedRange
            self.timestamp = Date()
        }
    }
    
    // MARK: - Properties
    
    /// 撤销栈，最多保存3个操作
    private var undoStack: [EditOperation] = []
    
    /// 重做栈
    private var redoStack: [EditOperation] = []
    
    /// 最大保存的操作数量
    private let maxOperations = 3
    
    /// 当前是否正在执行撤销/重做操作（防止递归记录）
    private var isPerformingUndoRedo = false
    
    // MARK: - Published Properties
    
    /// 是否可以撤销
    @Published private(set) var canUndo = false
    
    /// 是否可以重做
    @Published private(set) var canRedo = false
    
    // MARK: - Public Methods
    
    /// 记录一个编辑操作
    /// - Parameters:
    ///   - content: 当前内容
    ///   - selectedRange: 当前选中范围
    func recordOperation(content: NSAttributedString, selectedRange: NSRange) {
        // 如果正在执行撤销/重做，不记录操作
        guard !isPerformingUndoRedo else { return }
        
        // 如果内容为空且栈为空，不记录
        if content.string.isEmpty && undoStack.isEmpty {
            return
        }
        
        // 检查是否与最后一个操作相同（避免重复记录）
        if let lastOperation = undoStack.last {
            if lastOperation.content.isEqual(to: content) && 
               NSEqualRanges(lastOperation.selectedRange, selectedRange) {
                return
            }
        }
        
        let operation = EditOperation(content: content, selectedRange: selectedRange)
        
        // 添加到撤销栈
        undoStack.append(operation)
        
        // 限制栈大小
        if undoStack.count > maxOperations {
            undoStack.removeFirst()
        }
        
        // 清空重做栈（新操作会使重做无效）
        redoStack.removeAll()
        
        // 更新状态
        updateCanUndoRedo()
        
        print("【调试】UndoRedoManager: 记录操作，撤销栈大小: \(undoStack.count)")
    }
    
    /// 执行撤销操作
    /// - Returns: 撤销后的内容和选中范围，如果无法撤销返回nil
    func undo() -> (content: NSAttributedString, selectedRange: NSRange)? {
        guard canUndo, let currentOperation = undoStack.popLast() else {
            return nil
        }
        
        isPerformingUndoRedo = true
        defer { isPerformingUndoRedo = false }
        
        // 将当前操作移到重做栈
        redoStack.append(currentOperation)
        
        // 限制重做栈大小
        if redoStack.count > maxOperations {
            redoStack.removeFirst()
        }
        
        // 获取要恢复的操作
        let targetOperation: EditOperation
        if let previousOperation = undoStack.last {
            targetOperation = previousOperation
        } else {
            // 如果撤销栈为空，返回空内容
            targetOperation = EditOperation(
                content: NSAttributedString(string: ""),
                selectedRange: NSRange(location: 0, length: 0)
            )
        }
        
        // 更新状态
        updateCanUndoRedo()
        
        print("【调试】UndoRedoManager: 执行撤销，撤销栈大小: \(undoStack.count)，重做栈大小: \(redoStack.count)")
        
        return (content: targetOperation.content, selectedRange: targetOperation.selectedRange)
    }
    
    /// 执行重做操作
    /// - Returns: 重做后的内容和选中范围，如果无法重做返回nil
    func redo() -> (content: NSAttributedString, selectedRange: NSRange)? {
        guard canRedo, let operationToRedo = redoStack.popLast() else {
            return nil
        }
        
        isPerformingUndoRedo = true
        defer { isPerformingUndoRedo = false }
        
        // 将操作移回撤销栈
        undoStack.append(operationToRedo)
        
        // 限制撤销栈大小
        if undoStack.count > maxOperations {
            undoStack.removeFirst()
        }
        
        // 更新状态
        updateCanUndoRedo()
        
        print("【调试】UndoRedoManager: 执行重做，撤销栈大小: \(undoStack.count)，重做栈大小: \(redoStack.count)")
        
        return (content: operationToRedo.content, selectedRange: operationToRedo.selectedRange)
    }
    
    /// 清空所有操作历史
    func clearHistory() {
        undoStack.removeAll()
        redoStack.removeAll()
        updateCanUndoRedo()
        
        print("【调试】UndoRedoManager: 清空操作历史")
    }
    
    /// 获取操作历史信息（用于调试）
    func getHistoryInfo() -> (undoCount: Int, redoCount: Int) {
        return (undoCount: undoStack.count, redoCount: redoStack.count)
    }
    
    // MARK: - Private Methods
    
    /// 更新撤销重做状态
    private func updateCanUndoRedo() {
        canUndo = !undoStack.isEmpty
        canRedo = !redoStack.isEmpty
    }
}

// MARK: - Extensions

extension UndoRedoManager {
    
    /// 获取撤销栈的描述信息（用于调试）
    var undoStackDescription: String {
        return undoStack.enumerated().map { index, operation in
            let preview = operation.content.string.prefix(20)
            return "\(index + 1). \"\(preview)\" (\(operation.timestamp.formatted(.dateTime.hour().minute().second())))"
        }.joined(separator: "\n")
    }
    
    /// 获取重做栈的描述信息（用于调试）
    var redoStackDescription: String {
        return redoStack.enumerated().map { index, operation in
            let preview = operation.content.string.prefix(20)
            return "\(index + 1). \"\(preview)\" (\(operation.timestamp.formatted(.dateTime.hour().minute().second())))"
        }.joined(separator: "\n")
    }
}
