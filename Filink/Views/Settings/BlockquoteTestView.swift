import SwiftUI
import UIKit

struct BlockquoteTestView: View {
    let testContent = "这是一个测试引用块的内容。\n支持多行文本显示。\n可以包含各种长度的文字内容来测试效果。"

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                Text("SwiftUI 引用块")
                    .font(.headline)

                BlockquoteView(contentItems: sampleContentItems)

            }
            .padding(.horizontal, 16)
            .padding(.top, 20)
        }
        .navigationTitle("引用块样式测试")
        .navigationBarTitleDisplayMode(.inline)
    }

    // 示例ContentItem数组
    private var sampleContentItems: [ContentItem] {
        return [
            .text("这是一个SwiftUI版本的引用块示例。", isBold: false, isItalic: false, isUnderline: false),
            .newline,
            .text("支持", isBold: false, isItalic: false, isUnderline: false),
            .text("粗体", isBold: true, isItalic: false, isUnderline: false),
            .text("和", isBold: false, isItalic: false, isUnderline: false),
            .text("斜体", isBold: false, isItalic: true, isUnderline: false),
            .text("格式。", isBold: false, isItalic: false, isUnderline: false),
            .newline,
            .link(text: "也支持链接", url: URL(string: "https://example.com")!)
        ]
    }

}
