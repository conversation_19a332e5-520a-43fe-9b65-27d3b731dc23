import SwiftUI

/// 用于测试新 `Drawer` 组件的视图。
struct DrawerTestView: View {
    @State private var isDrawerOpen = false

    var body: some View {
        ZStack {
            // 主内容区域
            VStack(spacing: 20) {
                Text("抽屉组件测试页")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("点击下面的按钮来展示或隐藏抽屉。")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                Button(action: {
                    withAnimation(.interactiveSpring()) {
                        isDrawerOpen = true
                    }
                }) {
                    Text("显示抽屉")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                        .shadow(radius: 5)
                }
                .padding(.horizontal, 40)
            }
            .padding()

            // 抽屉组件
            Drawer(isOpen: $isDrawerOpen) {
                // 这是抽屉里显示的内容
                VStack {
                    Text("抽屉内容")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding()
                    
                    Text("你可以向上或向下拖动来改变抽屉的高度，或者完全关闭它。")
                        .padding()
                    
                    Spacer()
                }
            }
        }
        .navigationTitle("抽屉测试")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct DrawerTestView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DrawerTestView()
        }
    }
}
