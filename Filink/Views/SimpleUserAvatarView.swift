import SwiftUI
import Foundation

// MARK: - 图片加载状态管理
class ImageLoadingState: ObservableObject {
    @Published var loadedImage: UIImage? = nil

    // 用于防止重复加载（内部状态，不需要Published）
    var isLoading: Bool = false
    var hasStartedLoading: Bool = false
}

// MARK: - 全局头像缓存管理器（支持持久化）
class SimpleAvatarCache: ObservableObject {
    static let shared = SimpleAvatarCache()
    private var imageCache: [String: UIImage] = [:]
    private let cacheDirectory: URL

    private init() {
        // 创建缓存目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        cacheDirectory = documentsPath.appendingPathComponent("AvatarCache")

        // 确保缓存目录存在
        try? FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)

        // 启动时预加载一些缓存到内存
        preloadRecentCache()
    }

    func getCachedImage(for cacheKey: String) -> UIImage? {
        // 首先检查内存缓存
        if let memoryImage = imageCache[cacheKey] {
            return memoryImage
        }

        // 然后检查磁盘缓存
        if let diskImage = loadImageFromDisk(cacheKey: cacheKey) {
            // 加载到内存缓存中
            imageCache[cacheKey] = diskImage
            return diskImage
        }

        return nil
    }

    func cacheImage(_ image: UIImage, for cacheKey: String) {
        // 缓存到内存
        imageCache[cacheKey] = image

        // 异步缓存到磁盘
        Task.detached { [weak self] in
            self?.saveImageToDisk(image: image, cacheKey: cacheKey)
        }
    }

    func generateCacheKey(avatarURL: String?, username: String, size: SimpleUserAvatarView.AvatarSize) -> String {
        if let url = avatarURL, !url.isEmpty {
            return "\(url.hashValue)_\(size.dimension)"
        }
        return "letter_\(username)_\(size.dimension)"
    }

    // MARK: - 磁盘缓存操作
    private func loadImageFromDisk(cacheKey: String) -> UIImage? {
        let fileURL = cacheDirectory.appendingPathComponent("\(cacheKey).jpg")
        guard let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        return image
    }

    private func saveImageToDisk(image: UIImage, cacheKey: String) {
        let fileURL = cacheDirectory.appendingPathComponent("\(cacheKey).jpg")
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        try? data.write(to: fileURL)
    }

    // 预加载最近的缓存到内存（启动优化）
    private func preloadRecentCache() {
        Task.detached { [weak self] in
            guard let self = self else { return }

            do {
                let files = try FileManager.default.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.contentModificationDateKey])

                // 按修改时间排序，取最近的20个文件
                let recentFiles = files
                    .compactMap { url -> (URL, Date)? in
                        guard let date = try? url.resourceValues(forKeys: [.contentModificationDateKey]).contentModificationDate else { return nil }
                        return (url, date)
                    }
                    .sorted { $0.1 > $1.1 }
                    .prefix(20)

                // 预加载到内存
                for (fileURL, _) in recentFiles {
                    let cacheKey = fileURL.deletingPathExtension().lastPathComponent
                    if let data = try? Data(contentsOf: fileURL),
                       let image = UIImage(data: data) {
                        await MainActor.run {
                            self.imageCache[cacheKey] = image
                        }
                    }
                }
            } catch {
                print("预加载头像缓存失败: \(error)")
            }
        }
    }

    // 清理过期缓存（可选）
    func cleanExpiredCache(olderThan days: Int = 30) {
        Task.detached { [weak self] in
            guard let self = self else { return }

            let expireDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 60 * 60))

            do {
                let files = try FileManager.default.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.contentModificationDateKey])

                for fileURL in files {
                    if let modificationDate = try? fileURL.resourceValues(forKeys: [.contentModificationDateKey]).contentModificationDate,
                       modificationDate < expireDate {
                        try? FileManager.default.removeItem(at: fileURL)
                    }
                }
            } catch {
                print("清理过期缓存失败: \(error)")
            }
        }
    }
}

// MARK: - 简化的用户头像组件
struct SimpleUserAvatarView: View {
    let username: String
    let avatarURL: String?
    let size: AvatarSize

    // 在初始化时生成随机渐变，避免重绘时变化
    private let randomGradient: LinearGradient
    private let cacheKey: String

    // 使用全局状态，避免重绘时丢失
    @StateObject private var imageState = ImageLoadingState()

    // 初始化方法
    init(username: String, avatarURL: String?, size: AvatarSize) {
        self.username = username
        self.avatarURL = avatarURL
        self.size = size
        self.cacheKey = SimpleAvatarCache.shared.generateCacheKey(avatarURL: avatarURL, username: username, size: size)

        // 为每个头像实例生成随机渐变（包括游客）
        self.randomGradient = Self.generateRandomGradient()
    }

    // 头像尺寸枚举
    enum AvatarSize {
        case small, medium, large
        
        var dimension: CGFloat {
            switch self {
            case .small: return 36
            case .medium: return 48
            case .large: return 128
            }
        }
        
        var fontSize: CGFloat {
            dimension * 0.4
        }
    }
    
    // 计算显示的字母
    private var displayLetter: String {
        if username == "游客" || username.isEmpty {
            return "游"
        }
        return username.prefix(1).uppercased()
    }
    
    // 使用初始化时生成的随机渐变
    private var backgroundGradient: LinearGradient {
        return randomGradient
    }

    // 生成完全随机的渐变
    private static func generateRandomGradient() -> LinearGradient {
        // 生成两个随机颜色
        let color1 = generateRandomColor()
        let color2 = generateRandomColor()

        // 随机选择渐变方向
        let startPoints: [UnitPoint] = [.topLeading, .top, .topTrailing, .leading]
        let endPoints: [UnitPoint] = [.bottomTrailing, .bottom, .bottomLeading, .trailing]

        let randomStartPoint = startPoints.randomElement() ?? .topLeading
        let randomEndPoint = endPoints.randomElement() ?? .bottomTrailing

        return LinearGradient(
            gradient: Gradient(colors: [color1, color2]),
            startPoint: randomStartPoint,
            endPoint: randomEndPoint
        )
    }

    // 生成随机颜色
    private static func generateRandomColor() -> Color {
        return Color(
            red: Double.random(in: 0.3...0.9),   // 避免太暗或太亮
            green: Double.random(in: 0.3...0.9),
            blue: Double.random(in: 0.3...0.9)
        )
    }
    
    var body: some View {
        Group {
            if let cachedImage = imageState.loadedImage {
                // 显示缓存的真实头像
                Image(uiImage: cachedImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                // 字母头像作为默认头像（加载中也显示相同效果）
                Text(displayLetter)
                    .font(.system(size: size.fontSize, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: size.dimension, height: size.dimension)
                    .background(backgroundGradient)
            }
        }
        .frame(width: size.dimension, height: size.dimension)
        .clipShape(Circle())
        .overlay(
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
        )
        .onAppear {
            loadImageIfNeeded()
        }
    }

    // 智能加载图片（支持磁盘缓存，防重绘丢失状态）
    private func loadImageIfNeeded() {
        // 如果已经有图片或正在加载，直接返回
        if imageState.loadedImage != nil || imageState.hasStartedLoading {
            return
        }

        // 异步检查缓存，避免阻塞UI
        Task {
            // 首先检查缓存（内存+磁盘）
            if let cachedImage = SimpleAvatarCache.shared.getCachedImage(for: cacheKey) {
                await MainActor.run {
                    self.imageState.loadedImage = cachedImage
                }
                return
            }

            // 如果没有URL，不需要加载
            guard let avatarTemplate = avatarURL, !avatarTemplate.isEmpty else {
                return
            }

            // 构建完整的头像URL
            let finalURL: URL?
            if avatarTemplate.hasPrefix("http") {
                // 如果已经是完整URL，直接使用
                finalURL = URL(string: avatarTemplate)
                // print("【调试】SimpleUserAvatarView: 使用完整URL - \(avatarTemplate)")
            } else {
                // 如果是模板路径，需要构建完整URL
                let avatarSize: SettingsManager.AppConstants.Avatar.Size
                switch size {
                case .small:
                    avatarSize = .small
                case .medium:
                    avatarSize = .medium
                case .large:
                    avatarSize = .large
                }
                finalURL = SettingsManager.AppConstants.Avatar.buildAvatarURL(fromTemplate: avatarTemplate, size: avatarSize)
                print("【调试】SimpleUserAvatarView: 从模板构建URL - 模板: \(avatarTemplate), 最终URL: \(finalURL?.absoluteString ?? "nil")")
            }

            guard let url = finalURL else {
                return
            }

            // 避免重复加载
            await MainActor.run {
                guard !imageState.isLoading && !imageState.hasStartedLoading else { return }
                imageState.isLoading = true
                imageState.hasStartedLoading = true
            }

            // 网络加载图片
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                if let image = UIImage(data: data) {
                    await MainActor.run {
                        // 缓存图片（内存+磁盘）
                        SimpleAvatarCache.shared.cacheImage(image, for: cacheKey)
                        self.imageState.loadedImage = image
                        self.imageState.isLoading = false
                    }
                } else {
                    await MainActor.run {
                        self.imageState.isLoading = false
                    }
                }
            } catch {
                await MainActor.run {
                    self.imageState.isLoading = false
                }
            }
        }
    }
}

// MARK: - 便利构造方法
extension SimpleUserAvatarView {
    static func small(username: String, avatarURL: String? = nil) -> SimpleUserAvatarView {
        SimpleUserAvatarView(username: username, avatarURL: avatarURL, size: .small)
    }

    static func medium(username: String, avatarURL: String? = nil) -> SimpleUserAvatarView {
        SimpleUserAvatarView(username: username, avatarURL: avatarURL, size: .medium)
    }

    static func large(username: String, avatarURL: String? = nil) -> SimpleUserAvatarView {
        SimpleUserAvatarView(username: username, avatarURL: avatarURL, size: .large)
    }
}

