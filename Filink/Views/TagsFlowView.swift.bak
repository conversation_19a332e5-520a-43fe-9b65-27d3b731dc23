import SwiftUI

// MARK: - TagView 组件
struct TagsFlowView: View {
    let category: CategoryItem? // 新增，类型改为 CategoryItem
    let tags: [String] // 保持原始标签字符串
    let geometry: GeometryProxy
    @Environment(\.colorScheme) var colorScheme
    // InternalView 自己去获取全局单例并观察它
    @EnvironmentObject var tagDataLoader: TagDataLoader
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            let rows = createTagRows(geometry: geometry)
            ForEach(0..<rows.count, id: \.self) { rowIndex in
                HStack(spacing: 10) {
                    ForEach(rows[rowIndex], id: \.self) { item in
                        tagView(for: item)
                    }
                    Spacer()
                }
                .padding(.vertical, 2)
            }
        }
        .padding(.vertical, 4)
    }
    
    // 统一的标签视图渲染方法
    private func tagView(for item: TagItem) -> some View {
        switch item {
        case .category(let category):
            return createCategoryView(category)
        case .tag(let tagName):
            return createTagView(tagName)
        }
    }
    
    // 创建分类标签视图
    private func createCategoryView(_ category: CategoryItem) -> AnyView {
        let finalColor = Color.categoryColor(hex: category.color, for: colorScheme)
        // 在亮色模式下移除背景，在深色模式下设置一个可见的背景
        let backgroundOpacity = colorScheme == .dark ? 0.25 : 0.0
        return AnyView(HStack(spacing: 4) {
            Image(systemName: category.icon)
                .font(.caption)
                .foregroundColor(finalColor)
            Text(category.name)
                .font(.caption)
                .foregroundColor(finalColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(finalColor.opacity(backgroundOpacity))
        .cornerRadius(4))
    }
    
    // 创建普通标签视图
    private func createTagView(_ tagName: String) -> AnyView {
        if let tag = tagDataLoader.getTag(by: tagName) {
            return AnyView(HStack(spacing: 4) {
                if let iconName = tag.icon {
                    Image(systemName: iconName)
                        .font(.caption)
                }
                Text(tag.text)
                    .font(.caption)
                    .lineLimit(1)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1))
            .cornerRadius(4)
            .foregroundColor(.secondary))
        } else {
            // 如果在 tagjson 中找不到标签，直接显示原始字符串
            return AnyView(Text(tagName)
                .font(.caption)
                .lineLimit(1)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1))
                .cornerRadius(4)
                .foregroundColor(.secondary))
        }
    }
    
    // 预先计算标签分组
    private func createTagRows(geometry: GeometryProxy) -> [[TagItem]] {
        let allItems = createAllTagItems()
        return arrangeItemsIntoRows(items: allItems, availableWidth: geometry.size.width)
    }
    
    // 创建所有标签项
    private func createAllTagItems() -> [TagItem] {
        var allItems: [TagItem] = []
        if let cat = category {
            allItems.append(.category(cat))
        }
        allItems.append(contentsOf: tags.map { .tag($0) })
        return allItems
    }
    
    // 将标签项排列成行
    private func arrangeItemsIntoRows(items: [TagItem], availableWidth: CGFloat) -> [[TagItem]] {
        var rows: [[TagItem]] = [[]]
        var currentRowWidth: CGFloat = 0
        let horizontalSpacing: CGFloat = 6 // 减少标签之间的间距
        
        for item in items {
            let itemText = getItemText(item)
            let estimatedPadding = getItemPadding(item)
            let itemWidth = estimateWidth(for: itemText) + estimatedPadding
            
            if currentRowWidth + itemWidth > availableWidth && !rows[rows.count-1].isEmpty {
                // 创建新行
                rows.append([item])
                currentRowWidth = itemWidth + horizontalSpacing
            } else {
                // 添加到当前行
                rows[rows.count-1].append(item)
                currentRowWidth += itemWidth + horizontalSpacing
            }
        }
        
        return rows
    }
    
    // 获取标签项文本
    private func getItemText(_ item: TagItem) -> String {
        switch item {
        case .category(let cat):
            return cat.name
        case .tag(let tagName):
            return tagDataLoader.getTag(by: tagName)?.text ?? tagName
        }
    }
    
    // 获取标签项估计内边距
    private func getItemPadding(_ item: TagItem) -> CGFloat {
        switch item {
        case .category:
            return 30 // 分类标签的估计宽度
        case .tag:
            return 26 // 普通标签的估计宽度，减小一点
        }
    }
    
    // 估计文本宽度的辅助函数
    private func estimateWidth(for text: String) -> CGFloat {
        let font = UIFont.preferredFont(forTextStyle: .subheadline)
        let attributes = [NSAttributedString.Key.font: font]
        let size = (text as NSString).size(withAttributes: attributes)
        return size.width
    }
}
