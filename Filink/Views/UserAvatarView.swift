import SwiftUI
import UIKit
import Combine

// MARK: - 自定义容器视图，确保正确的intrinsicContentSize
class AvatarContainerView: UIView {
    private let fixedDimensions: CGFloat

    init(dimensions: CGFloat) {
        self.fixedDimensions = dimensions
        super.init(frame: CGRect(x: 0, y: 0, width: dimensions, height: dimensions))
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override var intrinsicContentSize: CGSize {
        return CGSize(width: fixedDimensions, height: fixedDimensions)
    }
}

// MARK: - 隔离的头像视图 (仿照PostContentView模式)
struct IsolatedUserAvatarView: View, Equatable {
    let username: String
    let identifier: String
    let size: String

    var body: some View {
        // 根据identifier判断是模板还是文件名
        if identifier.hasPrefix("/") {
            // 头像模板路径
            UserAvatarView(
                username: username,
                avatarTemplate: identifier,
                size: sizeEnum,
                dimensions: dimensionsValue,
                enableScrollOptimization: false
            )
        } else {
            // 头像文件名
            UserAvatarView(
                username: username,
                avatarFilename: identifier,
                size: sizeEnum,
                dimensions: dimensionsValue,
                enableScrollOptimization: false
            )
        }
    }

    private var sizeEnum: SettingsManager.AppConstants.Avatar.Size {
        return SettingsManager.AppConstants.Avatar.Size(rawValue: size) ?? .medium
    }

    private var dimensionsValue: CGFloat {
        switch sizeEnum {
        case .small:
            return 36
        case .medium:
            return 48
        case .large:
            return 128
        }
    }

    // 实现 Equatable 以避免不必要的重绘 - 只比较影响渲染的核心数据
    static func == (lhs: IsolatedUserAvatarView, rhs: IsolatedUserAvatarView) -> Bool {
        return lhs.username == rhs.username &&
               lhs.identifier == rhs.identifier &&
               lhs.size == rhs.size
    }
}

/// 全局头像缓存 - 彻底阻止重复加载
class GlobalAvatarCache {
    static let shared = GlobalAvatarCache()
    private init() {}

    // 内存中的头像缓存
    private var imageCache: [String: UIImage] = [:]
    private let queue = DispatchQueue(label: "GlobalAvatarCache", attributes: .concurrent)

    func cacheImage(_ image: UIImage, for key: String) {
        queue.async(flags: .barrier) {
            self.imageCache[key] = image
        }
    }

    func getCachedImage(for key: String) -> UIImage? {
        return queue.sync {
            return imageCache[key]
        }
    }

    func generateKey(username: String, identifier: String, size: String) -> String {
        return "\(username)_\(identifier)_\(size)"
    }
}



/// 用户头像显示组件 - 使用UIViewRepresentable实现高效渲染
struct UserAvatarView: UIViewRepresentable, Equatable {
    /// 用户名
    let username: String

    /// 头像文件名或完整模板路径
    let avatarFilename: String

    /// 头像模板路径（可选，优先使用）
    let avatarTemplate: String?

    /// 头像尺寸类型
    let size: SettingsManager.AppConstants.Avatar.Size

    /// 头像尺寸（以点为单位）
    let dimensions: CGFloat

    /// 边框颜色
    let borderColor: CGColor = CGColor(red: 0/255, green: 174/255, blue: 255/255, alpha: 0.3)

    /// 边框宽度
    let borderWidth: CGFloat = 1

    /// 是否启用滚动优化
    let enableScrollOptimization: Bool



    // 实现Equatable协议，防止不必要的重绘
    static func == (lhs: UserAvatarView, rhs: UserAvatarView) -> Bool {
        return lhs.username == rhs.username &&
               lhs.avatarFilename == rhs.avatarFilename &&
               lhs.avatarTemplate == rhs.avatarTemplate &&
               lhs.size == rhs.size &&
               lhs.dimensions == rhs.dimensions
    }

    /// 生成缓存键
    private var cacheKey: String {
        if let template = avatarTemplate {
            // 使用头像模板时，生成基于URL的通用缓存键
            let finalURL = SettingsManager.AppConstants.Avatar.buildAvatarURL(fromTemplate: template, size: size)?.absoluteString ?? ""
            return "url_\(finalURL.hashValue)_\(size.rawValue)"
        } else {
            // 使用文件名时，使用传统的用户相关缓存键
            return "user_\(username)_\(avatarFilename)_\(size.rawValue)"
        }
    }

    /// 生成用户关联缓存键（用于建立用户到通用缓存的映射）
    private var userCacheKey: String {
        let identifier = avatarTemplate ?? avatarFilename
        return "user_\(username)_\(identifier)_\(size.rawValue)"
    }
    
    /// 初始化（使用头像文件名）
    init(username: String, avatarFilename: String, size: SettingsManager.AppConstants.Avatar.Size, dimensions: CGFloat, enableScrollOptimization: Bool = false) {
        self.username = username
        self.avatarFilename = avatarFilename
        self.avatarTemplate = nil
        self.size = size
        self.dimensions = dimensions
        self.enableScrollOptimization = enableScrollOptimization
    }

    /// 初始化（使用完整头像模板）
    init(username: String, avatarTemplate: String, size: SettingsManager.AppConstants.Avatar.Size, dimensions: CGFloat, enableScrollOptimization: Bool = false) {
        self.username = username
        self.avatarFilename = ""
        self.avatarTemplate = avatarTemplate
        self.size = size
        self.dimensions = dimensions
        self.enableScrollOptimization = enableScrollOptimization
    }
    
    // 创建UIView
    func makeUIView(context: Context) -> UIView {
        // 立即检查全局缓存 - 仿照PostContentView模式
        let cacheKey = GlobalAvatarCache.shared.generateKey(
            username: username,
            identifier: avatarTemplate ?? avatarFilename,
            size: size.rawValue
        )

        let containerView = AvatarContainerView(dimensions: dimensions)
        containerView.backgroundColor = .clear

        let circleView = UIView(frame: CGRect(x: 0, y: 0, width: dimensions, height: dimensions))
        circleView.backgroundColor = .clear  // 改为透明背景
        circleView.layer.cornerRadius = dimensions / 2
        circleView.layer.masksToBounds = true
        circleView.layer.borderWidth = borderWidth
        circleView.layer.borderColor = borderColor
        containerView.addSubview(circleView)

        let imageView = UIImageView(frame: CGRect(x: 0, y: 0, width: dimensions, height: dimensions))
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = dimensions / 2
        circleView.addSubview(imageView)

        // 检查缓存，如果有就直接设置真实头像
        if let cachedImage = GlobalAvatarCache.shared.getCachedImage(for: cacheKey) {
            imageView.image = cachedImage
            imageView.backgroundColor = .clear
        } else {
            // 没有缓存时，立即显示字母头像
            context.coordinator.setupLetterAvatar()
            // 后台静默加载真实头像
            context.coordinator.loadImage()
        }

        context.coordinator.containerView = containerView
        context.coordinator.imageView = imageView
        context.coordinator.enableScrollOptimization = enableScrollOptimization

        return containerView
    }
    
    // 更新UIView
    func updateUIView(_ uiView: UIView, context: Context) {
        // 最高优先级保护：如果已经有真实头像，直接返回，不做任何操作
        if context.coordinator.hasRealImage {
            return
        }

        // 关键修复：只有在真正的属性变化时才更新
        if context.coordinator.shouldUpdate(username: username, avatarFilename: avatarFilename, avatarTemplate: avatarTemplate, size: size) {
            // 更新属性
            context.coordinator.username = username
            context.coordinator.avatarFilename = avatarFilename
            context.coordinator.avatarTemplate = avatarTemplate
            context.coordinator.size = size
            context.coordinator.enableScrollOptimization = enableScrollOptimization

            // 检查是否有缓存图像
            if context.coordinator.hasCachedImage() {
                context.coordinator.loadImageFromCache()
            } else {
                // 显示字母头像并开始加载
                context.coordinator.setupLetterAvatar()
                context.coordinator.isLoading = false
                context.coordinator.loadImage()
            }
        }
    }
    
    // 创建坐标器
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // 坐标器类
    @MainActor
    class Coordinator: NSObject, @unchecked Sendable {
        var parent: UserAvatarView
        
        var username: String
        var avatarFilename: String
        var avatarTemplate: String?
        var size: SettingsManager.AppConstants.Avatar.Size
        var enableScrollOptimization: Bool
        var lastScrollState: ScrollStateManager.ScrollState = .idle
        
        weak var containerView: UIView?
        weak var imageView: UIImageView?
        
        var isLoading: Bool = false
        var hasLetterAvatar: Bool = false

        // 使用全局缓存检查是否已有真实头像
        var hasRealImage: Bool {
            let key = GlobalAvatarCache.shared.generateKey(
                username: username,
                identifier: avatarTemplate ?? avatarFilename,
                size: size.rawValue
            )
            return GlobalAvatarCache.shared.getCachedImage(for: key) != nil
        }

        // 标记真实头像已加载（通过缓存实现）
        private func markRealImageLoaded() {
            // 不需要单独标记，缓存本身就是标记
        }
        
        init(_ parent: UserAvatarView) {
            self.parent = parent
            self.username = parent.username
            self.avatarFilename = parent.avatarFilename
            self.avatarTemplate = parent.avatarTemplate
            self.size = parent.size
            self.enableScrollOptimization = parent.enableScrollOptimization
        }
        
        func shouldUpdate(username: String, avatarFilename: String, avatarTemplate: String?, size: SettingsManager.AppConstants.Avatar.Size) -> Bool {
            // 如果已经有真实头像，绝对不允许更新，这是最重要的保护
            if hasRealImage {
                return false
            }

            // 更严格的比较逻辑，确保真正有变化才更新
            let usernameChanged = self.username != username
            let avatarFilenameChanged = self.avatarFilename != avatarFilename
            let sizeChanged = self.size != size

            // 对avatarTemplate进行更仔细的比较
            let avatarTemplateChanged: Bool
            switch (self.avatarTemplate, avatarTemplate) {
            case (nil, nil):
                avatarTemplateChanged = false
            case (let oldTemplate?, let newTemplate?):
                avatarTemplateChanged = oldTemplate != newTemplate
            case (nil, _), (_, nil):
                avatarTemplateChanged = true
            }

            let shouldUpdate = usernameChanged || avatarFilenameChanged || avatarTemplateChanged || sizeChanged

            return shouldUpdate
        }
        
        func hasCachedImage() -> Bool {
            let primaryCacheKey = self.generatePrimaryCacheKey()
            let userCacheKey = self.generateUserCacheKey()

            if ImageManager.shared.isImageCached(forKey: primaryCacheKey) {
                return true
            }

            if let mappedKey = self.getUserCacheMapping(userKey: userCacheKey),
               ImageManager.shared.isImageCached(forKey: mappedKey) {
                return true
            }

            return false
        }

        func loadImageFromCache() {
            // 最高优先级保护：如果已经有真实头像，不允许从缓存重新加载
            if hasRealImage {
                return
            }

            let primaryCacheKey = self.generatePrimaryCacheKey()
            let userCacheKey = self.generateUserCacheKey()

            if let cachedImage = ImageManager.shared.loadImageFromCache(forKey: primaryCacheKey) {
                // 如果有字母头像，使用过渡动画
                if hasLetterAvatar {
                    self.transitionToRealImage(cachedImage)
                } else {
                    // 直接设置真实头像
                    self.imageView?.image = cachedImage
                    self.imageView?.backgroundColor = .clear
                }
                self.createUserCacheMapping(userKey: userCacheKey, primaryKey: primaryCacheKey)
                self.markRealImageLoaded()
                return
            }

            if let mappedKey = self.getUserCacheMapping(userKey: userCacheKey),
               let cachedImage = ImageManager.shared.loadImageFromCache(forKey: mappedKey) {
                // 如果有字母头像，使用过渡动画
                if hasLetterAvatar {
                    self.transitionToRealImage(cachedImage)
                } else {
                    // 直接设置真实头像
                    self.imageView?.image = cachedImage
                    self.imageView?.backgroundColor = .clear
                }
                self.markRealImageLoaded()
                return
            }
        }

        // 专门用于已确认有真实头像的用户，只从缓存加载
        func loadImageFromCacheOnly() {
            let primaryCacheKey = self.generatePrimaryCacheKey()
            let userCacheKey = self.generateUserCacheKey()

            if let cachedImage = ImageManager.shared.loadImageFromCache(forKey: primaryCacheKey) {
                self.imageView?.subviews.forEach { if $0.tag == 1001 { $0.removeFromSuperview() } }
                self.imageView?.layer.sublayers?.forEach { if $0.name == "gradientLayer" { $0.removeFromSuperlayer() } }
                self.imageView?.image = cachedImage
                self.imageView?.backgroundColor = .clear
                return
            }

            if let mappedKey = self.getUserCacheMapping(userKey: userCacheKey),
               let cachedImage = ImageManager.shared.loadImageFromCache(forKey: mappedKey) {
                self.imageView?.subviews.forEach { if $0.tag == 1001 { $0.removeFromSuperview() } }
                self.imageView?.layer.sublayers?.forEach { if $0.name == "gradientLayer" { $0.removeFromSuperlayer() } }
                self.imageView?.image = cachedImage
                self.imageView?.backgroundColor = .clear
                return
            }

            // 如果缓存丢失，显示字母头像并重新加载
            setupLetterAvatar()
            loadImageForced()
        }

        // 网络加载图像（只在磁盘缓存没有时使用）
        func loadImageFromNetwork() {
            if isLoading {
                return
            }

            Task { @MainActor in
                self.isLoading = true
                // 不再显示加载指示器

                let avatarURL: URL?
                if let template = self.parent.avatarTemplate {
                    avatarURL = SettingsManager.AppConstants.Avatar.buildAvatarURL(fromTemplate: template, size: self.size)
                } else {
                    avatarURL = SettingsManager.AppConstants.Avatar.buildAvatarURL(forUsername: self.username, avatarFilename: self.avatarFilename, size: self.size)
                }

                guard let finalAvatarURL = avatarURL else {
                    self.isLoading = false
                    return
                }

                await self.loadImageWithImageManager(from: finalAvatarURL)
            }
        }

        // 强制加载图像（忽略全局状态）
        func loadImageForced() {
            loadImageFromNetwork()
        }
        
        func loadImage() {
            // 真正的禁止重绘：检查全局状态
            if hasRealImage {
                loadImageFromCacheOnly()
                return
            }

            let primaryCacheKey = self.generatePrimaryCacheKey()
            let userCacheKey = self.generateUserCacheKey()

            // 头像存在磁盘，直接同步加载
            if let cachedImage = ImageManager.shared.loadImageFromCache(forKey: primaryCacheKey) {
                if hasLetterAvatar {
                    self.transitionToRealImage(cachedImage)
                } else {
                    self.imageView?.image = cachedImage
                    self.imageView?.backgroundColor = .clear
                }
                self.createUserCacheMapping(userKey: userCacheKey, primaryKey: primaryCacheKey)
                self.markRealImageLoaded()
                return
            }

            if let mappedKey = self.getUserCacheMapping(userKey: userCacheKey),
               let cachedImage = ImageManager.shared.loadImageFromCache(forKey: mappedKey) {
                if hasLetterAvatar {
                    self.transitionToRealImage(cachedImage)
                } else {
                    self.imageView?.image = cachedImage
                    self.imageView?.backgroundColor = .clear
                }
                self.markRealImageLoaded()
                return
            }

            // 如果磁盘缓存中没有，进行网络加载
            loadImageFromNetwork()
        }

        private func loadImageWithImageManager(from url: URL) async {
            // 一旦开始加载真实头像，就不再受滚动状态影响
            // 移除滚动状态检查，确保真实头像能够正常加载和显示

            do {
                let loadedImage = try await ImageManager.shared.loadImage(from: url)
                await MainActor.run {
                    // 再次检查，防止并发问题导致覆盖
                    if self.hasRealImage {
                        print("【调试】UserAvatarView: 已有真实头像，跳过加载 - \(self.parent.username)")
                        return
                    }

                    self.isLoading = false
                    let primaryCacheKey = self.generatePrimaryCacheKey()
                    let userCacheKey = self.generateUserCacheKey()
                    self.createUserCacheMapping(userKey: userCacheKey, primaryKey: primaryCacheKey)

                    if let imageView = self.imageView {
                        // 从字母头像过渡到真实头像
                        self.transitionToRealImage(loadedImage)
                    }

                    // 缓存到全局缓存
                    let cacheKey = GlobalAvatarCache.shared.generateKey(
                        username: self.parent.username,
                        identifier: self.parent.avatarTemplate ?? self.parent.avatarFilename,
                        size: self.parent.size.rawValue
                    )
                    GlobalAvatarCache.shared.cacheImage(loadedImage, for: cacheKey)

                    // 标记为已有真实头像，这是关键！
                    self.markRealImageLoaded()
                }
            } catch {
                await MainActor.run {
                    // 加载失败时保持字母头像不变
                    self.isLoading = false
                    print("【调试】UserAvatarView: 头像加载失败，保持字母头像 - \(self.parent.username)")
                }
            }
        }
        
        // 设置字母头像
        func setupLetterAvatar() {
            guard let imageView = self.imageView else { return }
            
            // 清理之前的内容
            imageView.subviews.forEach { if $0.tag == 1001 { $0.removeFromSuperview() } }
            imageView.layer.sublayers?.forEach { if $0.name == "gradientLayer" { $0.removeFromSuperlayer() } }
            imageView.image = nil

            // 创建字母头像
            let firstLetter = String(parent.username.prefix(1)).uppercased()
            let label = UILabel()
            label.text = firstLetter
            label.textAlignment = .center
            label.font = UIFont.systemFont(ofSize: parent.dimensions * 0.4, weight: .medium)
            label.textColor = .white
            label.frame = imageView.bounds
            label.tag = 1001
            imageView.addSubview(label)

            // 添加渐变背景
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = imageView.bounds
            gradientLayer.colors = generateLetterAvatarColors()
            gradientLayer.startPoint = CGPoint(x: 0, y: 0)
            gradientLayer.endPoint = CGPoint(x: 1, y: 1)
            gradientLayer.name = "gradientLayer"
            imageView.layer.insertSublayer(gradientLayer, at: 0)
            
            hasLetterAvatar = true
        }
        
        // 从字母头像过渡到真实头像
        private func transitionToRealImage(_ image: UIImage) {
            guard let imageView = self.imageView else { return }
            
            // 使用平滑过渡动画
            UIView.transition(with: imageView, duration: 0.4, options: .transitionCrossDissolve, animations: {
                // 清理字母头像的内容
                imageView.subviews.forEach { if $0.tag == 1001 { $0.removeFromSuperview() } }
                imageView.layer.sublayers?.forEach { if $0.name == "gradientLayer" { $0.removeFromSuperlayer() } }
                
                // 设置真实头像
                imageView.image = image
                imageView.backgroundColor = .clear
            }, completion: { _ in
                self.hasLetterAvatar = false
            })
        }
        
        // 生成字母头像颜色
        private func generateLetterAvatarColors() -> [CGColor] {
            let colorPairs: [(UIColor, UIColor)] = [
                (UIColor(red: 0.25, green: 0.5, blue: 1.0, alpha: 1.0), UIColor(red: 0.5, green: 0.4, blue: 0.9, alpha: 1.0)),
                (UIColor(red: 0.9, green: 0.3, blue: 0.3, alpha: 1.0), UIColor(red: 1.0, green: 0.5, blue: 0.2, alpha: 1.0)),
                (UIColor(red: 0.2, green: 0.7, blue: 0.5, alpha: 1.0), UIColor(red: 0.1, green: 0.5, blue: 0.8, alpha: 1.0)),
                (UIColor(red: 0.6, green: 0.3, blue: 0.7, alpha: 1.0), UIColor(red: 0.9, green: 0.2, blue: 0.5, alpha: 1.0)),
                (UIColor(red: 0.2, green: 0.6, blue: 0.8, alpha: 1.0), UIColor(red: 0.1, green: 0.8, blue: 0.6, alpha: 1.0)),
                (UIColor(red: 0.8, green: 0.3, blue: 0.5, alpha: 1.0), UIColor(red: 0.5, green: 0.2, blue: 0.8, alpha: 1.0)),
                (UIColor(red: 0.7, green: 0.6, blue: 0.2, alpha: 1.0), UIColor(red: 0.9, green: 0.4, blue: 0.1, alpha: 1.0)),
                (UIColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0), UIColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0))
            ]
            
            // 基于用户名生成一致的颜色
            let index = abs(parent.username.hashValue) % colorPairs.count
            let colorPair = colorPairs[index]
            return [colorPair.0.cgColor, colorPair.1.cgColor]
        }

        private func generatePrimaryCacheKey() -> String {
            if let template = self.avatarTemplate {
                let finalURL = SettingsManager.AppConstants.Avatar.buildAvatarURL(fromTemplate: template, size: self.size)?.absoluteString ?? ""
                return "url_\(finalURL.hashValue)_\(self.size.rawValue)"
            } else {
                return "user_\(self.username)_\(self.avatarFilename)_\(self.size.rawValue)"
            }
        }

        private func generateUserCacheKey() -> String {
            let identifier = self.avatarTemplate ?? self.avatarFilename
            return "user_\(self.username)_\(identifier)_\(self.size.rawValue)"
        }

        private func createUserCacheMapping(userKey: String, primaryKey: String) {
            UserDefaults.standard.set(primaryKey, forKey: "avatar_mapping_\(userKey)")
        }

        private func getUserCacheMapping(userKey: String) -> String? {
            return UserDefaults.standard.string(forKey: "avatar_mapping_\(userKey)")
        }

    }
}

/// 常用头像视图扩展
extension UserAvatarView {
    static func commentAvatar(username: String, avatarFilename: String, enableScrollOptimization: Bool = false) -> UserAvatarView {
        UserAvatarView(username: username, avatarFilename: avatarFilename, size: .small, dimensions: 36, enableScrollOptimization: enableScrollOptimization)
    }
    
    static func postAuthorAvatar(username: String, avatarFilename: String, enableScrollOptimization: Bool = false) -> UserAvatarView {
        UserAvatarView(username: username, avatarFilename: avatarFilename, size: .medium, dimensions: 48, enableScrollOptimization: enableScrollOptimization)
    }

    static func profileAvatar(username: String, avatarFilename: String, enableScrollOptimization: Bool = false) -> UserAvatarView {
        UserAvatarView(username: username, avatarFilename: avatarFilename, size: .large, dimensions: 128, enableScrollOptimization: enableScrollOptimization)
    }

    static func scrollAwarePostAuthorAvatar(username: String, avatarFilename: String) -> UserAvatarView {
        // 强制禁用滚动优化，避免头像重绘问题
        UserAvatarView(username: username, avatarFilename: avatarFilename, size: .medium, dimensions: 48, enableScrollOptimization: false)
    }

    static func postAuthorAvatarWithTemplate(username: String, avatarTemplate: String, enableScrollOptimization: Bool = false) -> UserAvatarView {
        // 强制禁用滚动优化，避免头像重绘问题
        UserAvatarView(username: username, avatarTemplate: avatarTemplate, size: .medium, dimensions: 48, enableScrollOptimization: false)
    }

    static func scrollAwarePostAuthorAvatarWithTemplate(username: String, avatarTemplate: String) -> UserAvatarView {
        // 强制禁用滚动优化，避免头像重绘问题
        UserAvatarView(username: username, avatarTemplate: avatarTemplate, size: .medium, dimensions: 48, enableScrollOptimization: false)
    }

    static func profileAvatarWithTemplate(username: String, avatarTemplate: String, enableScrollOptimization: Bool = false) -> UserAvatarView {
        UserAvatarView(username: username, avatarTemplate: avatarTemplate, size: .large, dimensions: 128, enableScrollOptimization: enableScrollOptimization)
    }
}
