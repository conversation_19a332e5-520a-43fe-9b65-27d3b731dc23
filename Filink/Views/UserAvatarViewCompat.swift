import SwiftUI

// MARK: - 兼容层：保持与现有代码的兼容性
struct IsolatedUserAvatarViewCompat: View, Equatable {
    let username: String
    let identifier: String
    let size: String
    
    // 将字符串尺寸转换为枚举
    private var avatarSize: SimpleUserAvatarView.AvatarSize {
        switch size.lowercased() {
        case "small": return .small
        case "medium": return .medium
        case "large": return .large
        default: return .medium
        }
    }
    
    // 构建头像URL
    private var avatarURL: String? {
        guard !identifier.isEmpty else { return nil }
        
        if identifier.hasPrefix("/") {
            // 头像模板路径
            return buildAvatarURL(fromTemplate: identifier)
        } else {
            // 头像文件名
            return buildAvatarURL(forUsername: username, avatarFilename: identifier)
        }
    }
    
    var body: some View {
        SimpleUserAvatarView(
            username: username,
            avatarURL: avatarURL,
            size: avatarSize
        )
    }
    
    // 实现 Equatable
    static func == (lhs: IsolatedUserAvatarViewCompat, rhs: IsolatedUserAvatarViewCompat) -> Bool {
        return lhs.username == rhs.username &&
               lhs.identifier == rhs.identifier &&
               lhs.size == rhs.size
    }
    
    // MARK: - URL构建方法（从SettingsManager复制）
    private func buildAvatarURL(fromTemplate template: String) -> String? {
        let settingsManager = SettingsManager.shared
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        
        let sizeValue: Int
        switch avatarSize {
        case .small: sizeValue = 36
        case .medium: sizeValue = 72
        case .large: sizeValue = 128
        }
        
        let processedTemplate = template.replacingOccurrences(of: "{size}", with: "\(sizeValue)")
        return baseURL.appendingPathComponent(processedTemplate).absoluteString
    }
    
    private func buildAvatarURL(forUsername username: String, avatarFilename: String) -> String? {
        let settingsManager = SettingsManager.shared
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        
        let sizeValue: Int
        switch avatarSize {
        case .small: sizeValue = 36
        case .medium: sizeValue = 48
        case .large: sizeValue = 128
        }
        
        let avatarPath = "/user_avatar/\(settingsManager.website.replacingOccurrences(of: "https://", with: "").replacingOccurrences(of: "http://", with: ""))/\(username)/\(sizeValue)/\(avatarFilename).png"
        return baseURL.appendingPathComponent(avatarPath).absoluteString
    }
}

// MARK: - UserAvatarView兼容层
struct UserAvatarViewCompat: View, Equatable {
    let username: String
    let avatarFilename: String
    let avatarTemplate: String?
    let size: SimpleUserAvatarView.AvatarSize
    
    private var avatarURL: String? {
        if let template = avatarTemplate, !template.isEmpty {
            return buildAvatarURL(fromTemplate: template)
        } else if !avatarFilename.isEmpty {
            return buildAvatarURL(forUsername: username, avatarFilename: avatarFilename)
        }
        return nil
    }
    
    var body: some View {
        SimpleUserAvatarView(
            username: username,
            avatarURL: avatarURL,
            size: size
        )
    }
    
    // 实现 Equatable
    static func == (lhs: UserAvatarViewCompat, rhs: UserAvatarViewCompat) -> Bool {
        return lhs.username == rhs.username &&
               lhs.avatarFilename == rhs.avatarFilename &&
               lhs.avatarTemplate == rhs.avatarTemplate &&
               lhs.size == rhs.size
    }
    
    // MARK: - URL构建方法
    private func buildAvatarURL(fromTemplate template: String) -> String? {
        let settingsManager = SettingsManager.shared
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        
        let sizeValue: Int
        switch size {
        case .small: sizeValue = 36
        case .medium: sizeValue = 48
        case .large: sizeValue = 128
        }
        
        let processedTemplate = template.replacingOccurrences(of: "{size}", with: "\(sizeValue)")
        return baseURL.appendingPathComponent(processedTemplate).absoluteString
    }
    
    private func buildAvatarURL(forUsername username: String, avatarFilename: String) -> String? {
        let settingsManager = SettingsManager.shared
        guard let baseURL = URL(string: settingsManager.website) else { return nil }
        
        let sizeValue: Int
        switch size {
        case .small: sizeValue = 36
        case .medium: sizeValue = 72
        case .large: sizeValue = 128
        }
        
        let avatarPath = "/user_avatar/\(settingsManager.website.replacingOccurrences(of: "https://", with: "").replacingOccurrences(of: "http://", with: ""))/\(username)/\(sizeValue)/\(avatarFilename).png"
        return baseURL.appendingPathComponent(avatarPath).absoluteString
    }
}

// MARK: - 静态工厂方法兼容层
extension UserAvatarViewCompat {
    static func commentAvatar(username: String, avatarFilename: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: avatarFilename, avatarTemplate: nil, size: .small)
    }
    
    static func postAuthorAvatar(username: String, avatarFilename: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: avatarFilename, avatarTemplate: nil, size: .medium)
    }
    
    static func profileAvatar(username: String, avatarFilename: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: avatarFilename, avatarTemplate: nil, size: .large)
    }
    
    static func scrollAwarePostAuthorAvatar(username: String, avatarFilename: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: avatarFilename, avatarTemplate: nil, size: .medium)
    }
    
    static func postAuthorAvatarWithTemplate(username: String, avatarTemplate: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: "", avatarTemplate: avatarTemplate, size: .medium)
    }
    
    static func scrollAwarePostAuthorAvatarWithTemplate(username: String, avatarTemplate: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: "", avatarTemplate: avatarTemplate, size: .medium)
    }
    
    static func profileAvatarWithTemplate(username: String, avatarTemplate: String) -> UserAvatarViewCompat {
        UserAvatarViewCompat(username: username, avatarFilename: "", avatarTemplate: avatarTemplate, size: .large)
    }
}
